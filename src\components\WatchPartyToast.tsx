'use client';

import { Users, X } from 'lucide-react';
import { motion } from 'framer-motion';
import { Toast, ToastClose, ToastDescription, ToastTitle } from '@/components/ui/toast';

interface WatchPartyToastProps {
  title: string;
  description?: string;
  variant?: 'default' | 'success' | 'error' | 'destructive';
  id?: string;
  onOpenChange?: (open: boolean) => void;
}

export function WatchPartyToast({
  title,
  description,
  variant = 'default',
  id,
  onOpenChange,
}: WatchPartyToastProps) {
  // Determine background color based on variant
  const bgColor =
    variant === 'success' ? 'bg-gradient-to-r from-blue-600/90 to-blue-500/90' :
    variant === 'error' || variant === 'destructive' ? 'bg-gradient-to-r from-red-600/90 to-red-500/90' :
    'bg-gradient-to-r from-vista-blue/90 to-blue-500/90';

  return (
    <Toast
      id={id}
      className={`${bgColor} border-0 backdrop-blur-md shadow-lg shadow-blue-500/20 group overflow-hidden`}
      onOpenChange={onOpenChange}
    >
      <div className="absolute inset-0 bg-[url('/images/noise.png')] opacity-5"></div>
      <div className="absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 bg-white/10 rounded-full blur-2xl"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-white/20 p-2 rounded-full">
          <Users className="h-5 w-5 text-white" />
        </div>

        <div className="flex-1">
          <ToastTitle className="text-white font-medium">{title}</ToastTitle>
          {description && (
            <ToastDescription className="text-white/90 text-sm mt-1">
              {description}
            </ToastDescription>
          )}
        </div>

        <ToastClose className="text-white/80 hover:text-white transition-colors" />
      </div>

      {/* Animated progress bar */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 bg-white/30"
        initial={{ width: '0%' }}
        animate={{ width: '100%' }}
        transition={{ duration: 4, ease: 'linear' }}
      />
    </Toast>
  );
}
