// Server component
import { WatchPartyDetailClient } from './watch-party-detail-client'

// Using configuration compatible with static export
export const preferredRegion = 'auto'
export const dynamic = 'auto'

// This export is required for next build with output: export
export function generateStaticParams() {
  // For static export, we need to generate at least one example party ID
  // This ensures the build process has a template to work with
  return [
    { partyId: 'example-party' }, // Example static param
    { partyId: 'template' },      // Another example
    { partyId: 'demo-party' }     // One more for good measure
  ]
}

export default async function WatchPartyDetailPage({ params }: { params: { partyId: string } }) {
  // Properly destructure params to avoid Next.js warnings - await params in Next.js 15
  const { partyId } = await params;

  return <WatchPartyDetailClient partyId={partyId} />
}