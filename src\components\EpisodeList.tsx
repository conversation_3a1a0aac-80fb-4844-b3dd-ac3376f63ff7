'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Episode } from '@/types/index';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

/**
 * Props for the EpisodeList component
 *
 * @interface EpisodeListProps
 * @property {Object[]} seasons - Array of season objects containing episodes
 * @property {number} seasons[].seasonNumber - The season number
 * @property {Episode[]} seasons[].episodes - Array of episodes for this season
 * @property {string} showTitle - The title of the TV show
 * @property {number} seasonCount - The total number of seasons available
 * @property {boolean} isLoading - Whether the episode data is currently loading
 * @property {string} [imdbId] - The IMDb ID for the show (format: 'tt1234567')
 * @property {string} [tmdbId] - The TMDb ID for the show
 * @property {string} [contentId] - The content ID used in the application
 * @property {string} [contentType=show] - The type of content (usually 'show')
 * @property {number} [activeSeason] - The active season from the parent component
 * @property {number} [activeEpisode] - The active episode from the parent component
 */
interface EpisodeListProps {
  seasons: {
    seasonNumber: number;
    episodes: Episode[];
  }[];
  showTitle: string;
  seasonCount: number;
  isLoading: boolean;
  imdbId?: string;
  tmdbId?: string;
  contentId?: string;
  contentType?: string;
  activeSeason?: number;
  activeEpisode?: number;
}

/**
 * Episode List Component
 *
 * Displays a list of episodes for a TV show with season selection capabilities.
 * Allows users to browse and play specific episodes.
 *
 * Features:
 * - Season tabs for quick navigation between seasons
 * - Episode cards with thumbnail images, titles, and descriptions
 * - Play buttons for each episode
 * - Loading states while data is being fetched
 * - Season dropdown for mobile screens
 *
 * @param {EpisodeListProps} props - The component props
 * @returns {React.ReactElement} The rendered episode list
 */
export default function EpisodeList({
  seasons,
  showTitle,
  seasonCount,
  isLoading,
  imdbId,
  tmdbId,
  contentId,
  contentType = 'show',
  activeSeason: parentActiveSeason,
  activeEpisode: parentActiveEpisode
}: EpisodeListProps) {
  const [selectedSeason, setSelectedSeason] = useState<number>(1);
  const [selectedEpisode, setSelectedEpisode] = useState<number>(1);
  const router = useRouter();
  const [currentSeasonData, setCurrentSeasonData] = useState<{episodes: Episode[]}>({episodes: []});

  // Sync with parent's active season/episode
  useEffect(() => {
    if (parentActiveSeason && parentActiveSeason !== selectedSeason) {
      console.log(`[EpisodeList] Syncing with parent activeSeason: ${parentActiveSeason}`);
      setSelectedSeason(parentActiveSeason);
    }
    if (parentActiveEpisode && parentActiveEpisode !== selectedEpisode) {
      console.log(`[EpisodeList] Syncing with parent activeEpisode: ${parentActiveEpisode}`);
      setSelectedEpisode(parentActiveEpisode);
    }
  }, [parentActiveSeason, parentActiveEpisode, selectedSeason, selectedEpisode]);

  // Initialize with the season from URL params or first available season
  useEffect(() => {
    // Only run this effect on client side
    if (typeof window === 'undefined') return;

    console.log('EpisodeList: Initial season setup running', {
      seasonCount,
      seasons: seasons?.length || 0,
      parentActiveSeason,
      parentActiveEpisode
    });

    // Use parent state if available, otherwise check URL
    let initialSeason = parentActiveSeason;
    let initialEpisode = parentActiveEpisode;

    if (!initialSeason) {
      // Check URL for season parameter
      const urlParams = new URLSearchParams(window.location.search);
      const urlSeason = urlParams.get('season');

      if (urlSeason && !isNaN(Number(urlSeason))) {
        // URL param takes priority
        const seasonNum = Number(urlSeason);

        // Validate the season number against the season count
        if (seasonCount > 0 && seasonNum > seasonCount) {
          console.warn(`EpisodeList: URL season ${seasonNum} exceeds available seasons (${seasonCount}), using season 1`);
          initialSeason = 1;

          // Update URL to match season 1
          if (window.history) {
            const url = new URL(window.location.href);
            url.searchParams.set('season', '1');
            window.history.replaceState({}, '', url.toString());
          }
        } else {
          console.log(`EpisodeList: Initializing with season ${seasonNum} from URL params`);
          initialSeason = seasonNum;
        }
      } else {
        // Default to season 1
        initialSeason = 1;
      }
    }

    if (!initialEpisode) {
      initialEpisode = 1;
    }

    // Set the initial values
    if (initialSeason !== selectedSeason) {
      setSelectedSeason(initialSeason);
    }
    if (initialEpisode !== selectedEpisode) {
      setSelectedEpisode(initialEpisode);
    }
  }, [seasons, seasonCount, parentActiveSeason, parentActiveEpisode, selectedSeason, selectedEpisode]);

  // Update currentSeasonData whenever seasons or selectedSeason changes
  useEffect(() => {
    if (!seasons || seasons.length === 0 || !selectedSeason) return;

    console.log(`EpisodeList: Updating current season data`, {
      selectedSeason,
      availableSeasons: seasons.map(s => s.seasonNumber),
      seasonsLength: seasons.length
    });

    // Find the currently selected season
    const foundSeason = seasons.find(season => season.seasonNumber === selectedSeason);

    if (foundSeason) {
      console.log(`EpisodeList: Found season ${selectedSeason} with ${foundSeason.episodes.length} episodes`);
      setCurrentSeasonData(foundSeason);

      // If we found the season but it has no episodes, trigger a fetch
      if (foundSeason.episodes.length === 0 && tmdbId) {
        console.log(`EpisodeList: Season ${selectedSeason} found but has no episodes, triggering a fetch`);
        const event = new CustomEvent('seasonChange', {
          detail: { season: selectedSeason }
        });
        window.dispatchEvent(event);
      }
    } else {
      console.warn(`EpisodeList: Selected season ${selectedSeason} not found in available seasons`);
      setCurrentSeasonData({episodes: []});

      // If the selected season isn't in our list, trigger a fetch
      if (tmdbId) {
        console.log(`EpisodeList: Selected season ${selectedSeason} not loaded yet, triggering a fetch`);
        const event = new CustomEvent('seasonChange', {
          detail: { season: selectedSeason }
        });
        window.dispatchEvent(event);
      }
    }
  }, [seasons, selectedSeason, tmdbId]);

  // We use tmdbId directly in the component

  /**
   * Handles playing an episode when selected
   *
   * Dispatches a custom 'episodeChange' event to inform other components
   * (like the video player) that the current episode has changed.
   *
   * @param {React.MouseEvent} [event] - The click event
   * @param {number} [season] - The season number to play
   * @param {number} [episode=1] - The episode number to play
   * @returns {void}
   */
  const handlePlayEpisode = (event?: React.MouseEvent, season?: number, episode = 1) => {
    if (event) {
      event.preventDefault();
    }

    // Use selectedSeason if season is not provided
    const seasonToPlay = season || selectedSeason;

    console.log(`Play episode: S${seasonToPlay}:E${episode}`);

    // Update selected episode
    setSelectedEpisode(episode);

    // Dispatch event to notify other components that an episode was explicitly selected for playback
    const playEvent = new CustomEvent('episodeChange', {
      detail: { season: seasonToPlay, episode }
    });
    window.dispatchEvent(playEvent);

    // Update URL params without navigation
    const url = new URL(window.location.href);
    url.searchParams.set('season', seasonToPlay.toString());
    url.searchParams.set('episode', episode.toString());
    window.history.replaceState({}, '', url.toString());
  };

  /**
   * Handles season changes from the dropdown selector
   *
   * Updates the active season and dispatches a custom 'seasonChange' event.
   * This is primarily used on mobile views.
   *
   * @param {string} value - The selected season value
   * @returns {void}
   */
  const handleSelectChange = (value: string) => {
    const newSeason = parseInt(value);
    console.log(`Season selected from dropdown: ${newSeason}`);

    if (newSeason !== selectedSeason) {
      console.log(`EpisodeList: Changing from season ${selectedSeason} to ${newSeason}`);

      // Update state
      setSelectedSeason(newSeason);
      setSelectedEpisode(1); // Reset to first episode

      // Update URL params without navigation
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('season', newSeason.toString());
        url.searchParams.set('episode', '1'); // Reset to first episode
        window.history.replaceState({}, '', url.toString());
      } catch (error) {
        console.error('Error updating URL:', error);
      }

      // Dispatch season change event (without triggering player refresh)
      console.log(`EpisodeList: Dispatching seasonChange event for season ${newSeason}`);
      const seasonEvent = new CustomEvent('seasonChange', {
        detail: { season: newSeason }
      });
      window.dispatchEvent(seasonEvent);
    }
  };

  // We're not using season tabs in this version, so we don't need this function

  /**
   * Formats a date string into a human-friendly format
   *
   * @param {string} dateString - The date string to format (YYYY-MM-DD)
   * @returns {string} The formatted date string (e.g., "Jan 1, 2023")
   */
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Unknown date';
    }
  };

  const renderSeasonOptions = () => {
    // Log the season count for debugging
    console.log(`EpisodeList: Rendering season options with seasonCount=${seasonCount}`);

    // Always use the seasonCount from the API as the source of truth
    if (seasonCount > 0) {
      console.log(`EpisodeList: Using seasonCount=${seasonCount} from API`);

      // Generate season options based on the seasonCount from the API
      // Start from season 1 and go up to seasonCount
      const seasonOptions = [];
      for (let i = 1; i <= seasonCount; i++) {
        seasonOptions.push(
          <SelectItem key={i} value={i.toString()}>
            Season {i}
          </SelectItem>
        );
      }

      console.log(`EpisodeList: Generated ${seasonOptions.length} season options`);
      return seasonOptions;
    }

    // If we have seasons with episodes as a fallback
    if (seasons && seasons.length > 0) {
      // Filter out seasons with no episodes and get unique season numbers
      const validSeasons = seasons.filter(s => s.episodes && s.episodes.length > 0);
      const uniqueSeasons = [...new Set(validSeasons.map(s => s.seasonNumber))].sort((a, b) => a - b);

      console.log(`EpisodeList: Found ${uniqueSeasons.length} valid seasons with episodes as fallback`);

      // If we have valid seasons with episodes, show them
      if (uniqueSeasons.length > 0) {
        return uniqueSeasons.map((seasonNum) => (
          <SelectItem key={seasonNum} value={seasonNum.toString()}>
            Season {seasonNum}
          </SelectItem>
        ));
      }
    }

    // If no seasons are available at all, show at least the current selected season
    console.warn('EpisodeList: No seasons available, showing only current season');
    return (
      <SelectItem key={selectedSeason} value={selectedSeason.toString()}>
        Season {selectedSeason}
      </SelectItem>
    );
  };

  if (isLoading) {
    return (
      <div className="py-8 px-4 md:px-8 bg-vista-dark-lighter rounded-lg">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-40" />
        </div>
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex flex-col md:flex-row gap-4 mb-4 p-4">
            <Skeleton className="h-[169px] w-full md:w-[300px] rounded-lg" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-10 w-32 mt-2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  console.log(`EpisodeList: Rendering component with seasonCount=${seasonCount}, selectedSeason=${selectedSeason}, episodes available: ${currentSeasonData?.episodes?.length || 0}, total seasons loaded: ${seasons?.length || 0}`);

  return (
    <div className="py-8 px-4 md:px-8 bg-vista-dark-lighter rounded-lg">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
        <h3 className="text-xl font-bold mb-3 md:mb-0">{showTitle || 'Episodes'}</h3>
        <div className="w-full md:w-auto">
          <form
            onSubmit={(e) => e.preventDefault()}
            className="inline-block"
          >
            <Select
              value={selectedSeason.toString()}
              onValueChange={handleSelectChange}
              disabled={false} // Never disable the select
            >
              <SelectTrigger className="w-full md:w-[180px] bg-vista-dark border-vista-dark-lightest">
                <SelectValue placeholder="Select Season" />
              </SelectTrigger>
              <SelectContent className="bg-vista-dark border-vista-dark-lightest">
                {renderSeasonOptions()}
              </SelectContent>
            </Select>
          </form>
        </div>
      </div>

      {!currentSeasonData || currentSeasonData.episodes.length === 0 ? (
        <div className="text-center p-8">
          <div className="rounded-full bg-vista-dark inline-flex p-4 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-vista-accent">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </div>
          <h4 className="text-lg font-medium mb-2">No Episodes Available</h4>
          <p className="text-vista-light/70 max-w-md mx-auto">
            No episodes were found for this season. This may be because the season hasn't aired yet or the data isn't available.
          </p>
          <Button
            variant="outline"
            className="mt-4 border-vista-accent text-vista-accent hover:bg-vista-accent/10"
            onClick={() => {
              // Trigger a refresh of the current season
              const event = new CustomEvent('seasonChange', {
                detail: { season: selectedSeason }
              });
              window.dispatchEvent(event);
            }}
          >
            Retry
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {currentSeasonData.episodes.map((episode: Episode) => (
            <div
              key={episode.id}
              className="flex flex-col md:flex-row items-start gap-4 p-4 hover:bg-vista-dark-lightest transition-colors duration-200 rounded-lg"
            >
              <div className="relative aspect-video overflow-hidden rounded-md group">
                {episode.stillPath || episode.thumbnail ? (
                  <Image
                    src={episode.stillPath || episode.thumbnail || ''}
                    alt={`${episode.title} thumbnail`}
                    className="object-cover transition-transform duration-200 group-hover:scale-105"
                    width={300}
                    height={169}
                    quality={85}
                    unoptimized={true}
                    onError={(e) => {
                      console.error(`Error loading episode image: ${episode.stillPath || episode.thumbnail}`);
                      // Hide the image element
                      (e.target as HTMLImageElement).style.display = 'none';
                      // Show the fallback container
                      if (e.currentTarget.parentElement) {
                        e.currentTarget.parentElement.classList.add('fallback-active');
                      }
                    }}
                  />
                ) : (
                  <div className="bg-muted flex items-center justify-center w-full h-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="w-12 h-12 text-muted-foreground"
                    >
                      <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z"></path>
                      <polygon points="10 8 16 12 10 16 10 8"></polygon>
                    </svg>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-90 group-hover:opacity-100 transition-opacity duration-200" />
                <button
                  onClick={(event) => handlePlayEpisode(event, episode.seasonNumber || selectedSeason, episode.episodeNumber)}
                  className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  aria-label={`Play episode ${episode.episodeNumber}`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="w-16 h-16 text-white filter drop-shadow-lg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              <div className="flex flex-col justify-between flex-1 p-3">
                <div>
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="text-base font-medium line-clamp-1">{episode.title}</h3>
                    <span className="text-xs text-muted-foreground">
                      {episode.airDate && formatDate(episode.airDate)}
                    </span>
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground mb-2">
                    <span>Episode {episode.episodeNumber}</span>
                    {episode.runtime && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{episode.runtime} min</span>
                      </>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {episode.description || "No description available"}
                  </p>
                </div>

                <Button
                  onClick={(event) => handlePlayEpisode(event, episode.seasonNumber || selectedSeason, episode.episodeNumber)}
                  className="mt-3 bg-vista-accent hover:bg-vista-accent-light"
                  size="sm"
                >
                  Play Episode
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
