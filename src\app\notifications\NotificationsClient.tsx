'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Navbar } from '@/components/Navbar';
import { useNotifications } from '@/contexts/NotificationContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Bell, CheckCircle, Film, Tv, Info, X, Trash2, CheckCheck, Clock, Settings, ChevronDown, RefreshCw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { pusherClient } from '@/lib/pusher-client';
import Link from 'next/link';

// Define notification interface
interface Notification {
  _id: string;
  title: string;
  message: string;
  type: 'new_content' | 'recommendation' | 'update' | 'system';
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
}

export default function NotificationsClient() {
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const { notifications, unreadCount, loading: notificationsLoading, error, fetchNotifications, markAsRead, markAllAsRead, deleteNotification } = useNotifications();
  const { isAuthenticated, user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  // Create a wrapper function for force refreshing
  const forceRefresh = useCallback(() => {
    return fetchNotifications();
  }, [fetchNotifications]);

  // Handle client-side mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect if not authenticated - adding a delay to allow for auth state to be loaded properly
  useEffect(() => {
    if (!isMounted) return;

    // Only redirect if we're certain the user is not authenticated
    // This gives time for the auth context to properly initialize
    let redirectTimer: NodeJS.Timeout;

    if (!isAuthenticated && !authLoading) {
      redirectTimer = setTimeout(() => {
        router.push('/auth');
      }, 500); // Add a small delay to ensure auth context is fully loaded
    }

    return () => {
      if (redirectTimer) clearTimeout(redirectTimer);
    };
  }, [isMounted, isAuthenticated, router, authLoading]);

  // Fetch notifications on mount
  useEffect(() => {
    if (isMounted && isAuthenticated && user?.id) {
      fetchNotifications(); // Fetch on page load
    }
  }, [isMounted, isAuthenticated, user?.id, fetchNotifications]);

  // Set up real-time updates with Pusher
  useEffect(() => {
    // Only run in browser environment and when authenticated
    if (typeof window === 'undefined' || !isMounted || !isAuthenticated || !user?.id) return;

    console.log('Setting up Pusher in NotificationsClient for user:', user.id);

    // Only subscribe to the global notifications channel to reduce Pusher usage
    const globalChannel = pusherClient.subscribe('global-notifications');

    // Handle new notification event from global channel
    globalChannel.bind('new-notification', (data: {
      message: string,
      timestamp: string,
      notificationId?: string,
      title?: string,
      type?: string,
      count?: number,
      userId?: string, // Add userId for filtering
      targetUserId?: string, // Alternative field name
    }) => {
      console.log('NotificationsClient received global notification event:', data);

      // Only process notifications for this user or global notifications
      if (data.userId && data.userId !== user.id && data.targetUserId && data.targetUserId !== user.id) {
        console.log('Ignoring notification for different user:', data.userId || data.targetUserId);
        return;
      }

      // Refresh notifications
      fetchNotifications();

      // Show toast notification
      toast({
        title: data.title || 'New Notification',
        description: data.message,
        duration: 4000,
      });
    });

    // Handle notification deletion event from global channel
    globalChannel.bind('notification-deleted-all', (data: {
      timestamp: string,
      adminId: string,
      deletedCount?: number
    }) => {
      console.log('NotificationsClient received notification deletion event:', data);

      // Refresh notifications to reflect deletions
      fetchNotifications();

      // Show toast notification
      toast({
        title: 'Notifications Cleared',
        description: 'All notifications have been cleared by an administrator.',
        duration: 4000,
      });
    });

    // Handle user-specific notification deletion via global channel
    globalChannel.bind('notification-deleted', (data: {
      notificationId: string,
      timestamp: string,
      adminId: string,
      userId?: string,
      targetUserId?: string
    }) => {
      console.log('NotificationsClient received notification deletion for user:', data);

      // Only process deletions for this user
      if (data.userId && data.userId !== user.id && data.targetUserId && data.targetUserId !== user.id) {
        console.log('Ignoring notification deletion for different user:', data.userId || data.targetUserId);
        return;
      }

      // Refresh notifications to reflect the deletion
      fetchNotifications();

      // Show toast notification
      toast({
        title: 'Notification Removed',
        description: 'A notification has been removed by an administrator.',
        duration: 3000,
      });
    });

    // Cleanup function
    return () => {
      try {
        // Unbind all events
        globalChannel.unbind('new-notification');
        globalChannel.unbind('notification-deleted-all');
        globalChannel.unbind('notification-deleted');

        // Unsubscribe from channel
        pusherClient.unsubscribe('global-notifications');

        console.log('Cleaned up Pusher in NotificationsClient');
      } catch (e) {
        console.warn('Error unsubscribing from Pusher in NotificationsClient:', e);
      }
    };
  }, [isMounted, isAuthenticated, user?.id, fetchNotifications, toast]);

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter((notification: Notification) => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    if (activeTab === 'content') return notification.type === 'new_content';
    if (activeTab === 'recommendations') return notification.type === 'recommendation';
    if (activeTab === 'system') return notification.type === 'update' || notification.type === 'system';
    return true;
  });

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification._id);
    }

    // Navigate to content if available
    if (notification.contentId && notification.contentType) {
      router.push(`/details/${notification.contentType === 'movie' ? 'movies' : 'shows'}/${notification.contentId}`);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = () => {
    markAllAsRead();
    toast({
      title: 'All notifications marked as read',
      description: 'Your notifications have been updated',
    });
  };

  // Handle refresh with loading state
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);

    try {
      // Force refresh
      console.log('Forcing notification refresh');
      await forceRefresh();

      // Add a small delay to ensure the UI updates
      setTimeout(() => {
        toast({
          title: 'Notifications refreshed',
          description: 'Your notifications are now up to date',
        });
        setIsRefreshing(false);
      }, 500);
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      toast({
        title: 'Refresh failed',
        description: 'Could not refresh notifications. Please try again.',
        variant: 'destructive',
      });
      setIsRefreshing(false);
    }
  }, [forceRefresh, toast]);

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_content':
        return <Film className="h-5 w-5 text-vista-blue" />;
      case 'recommendation':
        return <Tv className="h-5 w-5 text-emerald-400" />;
      case 'update':
        return <Info className="h-5 w-5 text-amber-400" />;
      case 'system':
        return <Bell className="h-5 w-5 text-purple-400" />;
      default:
        return <Bell className="h-5 w-5 text-vista-light" />;
    }
  };

  // Get background class based on notification type
  const getNotificationBgClass = (type: string) => {
    switch (type) {
      case 'new_content':
        return 'bg-vista-blue/15 ring-vista-blue/40';
      case 'recommendation':
        return 'bg-emerald-400/15 ring-emerald-400/40';
      case 'update':
        return 'bg-amber-400/15 ring-amber-400/40';
      case 'system':
        return 'bg-purple-400/15 ring-purple-400/40';
      default:
        return 'bg-vista-light/15 ring-vista-light/40';
    }
  };

  // Combine loading states for improved UX
  const isLoading = authLoading || notificationsLoading || !isMounted;

  // Placeholder for pre-hydration or loading state
  if (isLoading) {
    return (
      <main className="min-h-screen bg-gradient-to-b from-[#121825] via-[#162033] to-[#192338] text-slate-100">
        <Navbar />
        <div className="container max-w-4xl mx-auto px-4 pt-24 pb-24 flex items-center justify-center">
          <div className="bg-gradient-to-br from-slate-900/80 to-slate-800/30 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-12 text-center">
            <div className="w-16 h-16 border-4 border-vista-blue/30 border-t-vista-blue rounded-full animate-spin mx-auto mb-6"></div>
            <h2 className="text-xl font-medium text-white">Loading notifications</h2>
            <p className="text-slate-300/70 mt-2">Please wait while we retrieve your notifications...</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-[#121825] via-[#162033] to-[#192338] text-slate-100">
      <Navbar />

      <div className="container max-w-4xl mx-auto px-3 xs:px-4 pt-20 pb-28">
        <div className="bg-gradient-to-br from-slate-900/80 to-slate-800/30 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="p-4 md:p-6 border-b border-white/10 bg-gradient-to-r from-vista-blue/5 to-transparent">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center">
                <div className="bg-vista-blue/15 p-3 rounded-full mr-4 ring-2 ring-vista-blue/20 shadow-lg shadow-vista-blue/10">
                  <Bell className="h-7 w-7 text-vista-blue" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">Notifications</h1>
                  <p className="text-slate-300/70 text-sm mt-1">Stay updated with the latest content</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="h-9 px-3 rounded-full text-slate-300 hover:text-white hover:bg-white/10 border-white/20"
                      >
                        <RefreshCw className={`h-4 w-4 mr-1.5 ${isRefreshing ? 'animate-spin' : ''}`} />
                        <span className="hidden sm:inline">Refresh</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Force refresh notifications</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {unreadCount > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-vista-blue border-vista-blue/40 hover:bg-vista-blue/10 h-9 rounded-full text-xs px-3 shadow-sm shadow-vista-blue/5"
                  >
                    <CheckCheck className="h-3.5 w-3.5 mr-1.5" />
                    <span className="hidden sm:inline">Mark all as read</span>
                    <span className="sm:hidden">Mark all</span>
                  </Button>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-9 w-9 p-0 rounded-full text-slate-300 hover:text-white hover:bg-white/10">
                      <Settings className="h-4 w-4" />
                      <span className="sr-only">Settings</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-slate-800 border-white/10 text-slate-100">
                    <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10">
                      Notification settings
                    </DropdownMenuItem>
                    <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10">
                      Mute all notifications
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Mobile Tab Selection */}
          <div className="block sm:hidden p-4 border-b border-white/10">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between bg-slate-800/40 border-white/10 hover:bg-slate-700/40 hover:border-white/20 py-3 h-auto text-base">
                  {activeTab === 'all' ? 'All notifications' :
                   activeTab === 'unread' ? 'Unread' :
                   activeTab === 'content' ? 'New Content' :
                   activeTab === 'recommendations' ? 'Recommendations' : 'System'}
                  {activeTab === 'unread' && unreadCount > 0 && (
                    <Badge variant="default" className="ml-2 bg-vista-blue/20 text-white border border-white/20 h-6 min-w-6 px-1.5 text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                  <ChevronDown className="h-5 w-5 ml-2 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-[calc(100vw-32px)] max-w-[300px] bg-slate-800 border-white/10 text-slate-100">
                <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10 py-3 text-base" onClick={() => setActiveTab('all')}>
                  All notifications
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10 py-3 text-base" onClick={() => setActiveTab('unread')}>
                  <span>Unread</span>
                  {unreadCount > 0 && (
                    <Badge variant="default" className="ml-2 bg-vista-blue/20 text-white border border-white/20 h-6 min-w-6 px-1.5 text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10 py-3 text-base" onClick={() => setActiveTab('content')}>
                  New Content
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10 py-3 text-base" onClick={() => setActiveTab('recommendations')}>
                  Recommendations
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer hover:bg-vista-blue/10 focus:bg-vista-blue/10 py-3 text-base" onClick={() => setActiveTab('system')}>
                  System
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Desktop Tabs */}
          <div className="hidden sm:block px-6 pt-5 pb-1">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-slate-800/40 border border-white/10 p-1 w-full rounded-lg grid grid-cols-5 h-auto">
                <TabsTrigger value="all" className="rounded-md data-[state=active]:bg-vista-blue data-[state=active]:text-white py-1.5 text-sm data-[state=active]:shadow-md data-[state=active]:shadow-vista-blue/20">
                  All
                </TabsTrigger>
                <TabsTrigger value="unread" className="rounded-md data-[state=active]:bg-vista-blue data-[state=active]:text-white py-1.5 text-sm data-[state=active]:shadow-md data-[state=active]:shadow-vista-blue/20">
                  Unread
                  {unreadCount > 0 && (
                    <Badge variant="default" className="ml-1 bg-vista-blue/20 text-white border border-white/20 h-4 min-w-4 px-1 text-[10px]">
                      {unreadCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="content" className="rounded-md data-[state=active]:bg-vista-blue data-[state=active]:text-white py-1.5 text-sm data-[state=active]:shadow-md data-[state=active]:shadow-vista-blue/20">
                  <span className="hidden md:inline">New Content</span>
                  <span className="md:hidden">Content</span>
                </TabsTrigger>
                <TabsTrigger value="recommendations" className="rounded-md data-[state=active]:bg-vista-blue data-[state=active]:text-white py-1.5 text-sm data-[state=active]:shadow-md data-[state=active]:shadow-vista-blue/20">
                  <span className="hidden md:inline">Recommendations</span>
                  <span className="md:hidden">Recom.</span>
                </TabsTrigger>
                <TabsTrigger value="system" className="rounded-md data-[state=active]:bg-vista-blue data-[state=active]:text-white py-1.5 text-sm data-[state=active]:shadow-md data-[state=active]:shadow-vista-blue/20">
                  System
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Notification Content */}
          <div className="p-3 xs:p-4 sm:p-6">
            {notificationsLoading ? (
              // Loading state
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <Card key={i} className="bg-slate-800/30 border-white/5 overflow-hidden">
                    <CardContent className="p-3 sm:p-4">
                      <div className="flex items-start gap-3">
                        <Skeleton className="h-10 w-10 rounded-full bg-white/10" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-3/4 bg-white/10" />
                          <Skeleton className="h-3 w-full bg-white/5" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : error ? (
              // Error state
              <Card className="bg-slate-800/30 border-red-400/30 text-red-300 overflow-hidden rounded-xl shadow-lg">
                <CardContent className="p-6 text-center">
                  <div className="bg-red-400/10 p-3 rounded-full mx-auto mb-4 w-16 h-16 flex items-center justify-center">
                    <X className="h-8 w-8 text-red-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">Failed to load notifications</h3>
                  <p className="text-red-300/80 mb-4 text-sm max-w-md mx-auto">{error}</p>
                  <Button
                    variant="outline"
                    onClick={forceRefresh}
                    className="border-red-400/30 text-red-300 hover:bg-red-400/10"
                  >
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            ) : filteredNotifications.length === 0 ? (
              // Empty state
              <Card className="bg-gradient-to-br from-slate-800/40 to-slate-800/20 border-white/5 border-dashed overflow-hidden rounded-xl shadow-lg">
                <CardContent className="p-6 text-center">
                  <div className="bg-vista-blue/5 p-3 rounded-full mx-auto mb-4 w-16 h-16 flex items-center justify-center">
                    <Bell className="h-8 w-8 text-slate-300/50" />
                  </div>
                  <h3 className="text-lg font-medium mb-2 text-white">No notifications</h3>
                  <p className="text-slate-300/70 text-sm max-w-md mx-auto mb-4">
                    {activeTab === 'all'
                      ? "You don't have any notifications yet. Check back later for updates on new content and recommendations."
                      : `You don't have any ${activeTab} notifications at the moment.`}
                  </p>
                  {activeTab !== 'all' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('all')}
                      className="text-slate-300 hover:text-white bg-white/5 hover:bg-white/10 border-white/10 transition-all duration-300"
                    >
                      View all notifications
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              // Notifications list
              <div className="space-y-4">
                <div className="text-xs text-slate-300/70 flex justify-between items-center px-2 mb-3">
                  <span>
                    {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
                  </span>
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    Updated {formatDistanceToNow(new Date(), { addSuffix: true })}
                  </span>
                </div>

                {filteredNotifications.map((notification) => (
                  <Card
                    key={notification._id}
                    className={`bg-gradient-to-r ${
                      !notification.read
                        ? 'from-vista-blue/5 to-transparent border-l-4 border-l-vista-blue'
                        : 'from-white/5 to-transparent'
                    } hover:bg-white/10 border-white/10 transition-all duration-300 overflow-hidden rounded-lg group shadow-md mb-4`}
                  >
                    <CardContent className="p-4 sm:p-5">
                      <div
                        className="cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start gap-4">
                          <div className={`flex-shrink-0 p-2.5 rounded-full ring-2 ${getNotificationBgClass(notification.type)}`}>
                            {getNotificationIcon(notification.type)}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex flex-col xs:flex-row justify-between gap-1 mb-2">
                              <h3 className="text-sm xs:text-base font-medium text-white pr-2 flex-1 leading-tight">
                                {notification.title}
                              </h3>

                              <div className="mt-1 xs:mt-0">
                                <span className="text-xs text-slate-300/70 whitespace-nowrap">
                                  {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                                </span>
                              </div>
                            </div>

                            <p className="text-xs xs:text-sm text-slate-300/90 mb-3 leading-relaxed">
                              {notification.message}
                            </p>

                            {notification.expiresAt && (
                              <div className="text-xs text-amber-300/90 bg-amber-500/15 px-3 py-1.5 rounded-sm border border-amber-500/20 mb-3 flex items-center w-fit">
                                <Clock className="h-3.5 w-3.5 mr-1.5" />
                                <span className="font-medium">Expires on {new Date(notification.expiresAt).toLocaleDateString()} at {new Date(notification.expiresAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                              </div>
                            )}

                            <div className="flex flex-wrap items-center gap-2">
                              <Badge
                                variant="outline"
                                className="text-xs px-2 py-0.5 h-6 border-white/20 bg-white/10 text-white rounded-sm"
                              >
                                {notification.type === 'new_content' ? 'New Content' :
                                 notification.type === 'recommendation' ? 'Recommendation' :
                                 notification.type === 'update' ? 'Update' : 'System'}
                              </Badge>

                              {!notification.read && notification.type !== 'new_content' && (
                                <Badge
                                  className="text-xs px-2 py-0.5 h-6 bg-vista-blue/30 text-white border border-vista-blue/40 rounded-sm"
                                >
                                  New
                                </Badge>
                              )}

                              {notification.expiresAt && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Badge
                                        variant="outline"
                                        className="text-xs px-2 py-0.5 h-6 border-amber-500/40 bg-amber-500/20 text-amber-300 rounded-sm flex items-center shadow-sm"
                                      >
                                        <Clock className="h-3.5 w-3.5 mr-1.5" />
                                        Expires {formatDistanceToNow(new Date(notification.expiresAt), { addSuffix: true })}
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent className="bg-slate-800 border-white/10 text-slate-100">
                                      <p>This notification will be automatically deleted on {new Date(notification.expiresAt).toLocaleDateString()} at {new Date(notification.expiresAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}

                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7 p-0 ml-auto text-slate-300/40 hover:text-red-400 hover:bg-red-400/10 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        deleteNotification(notification._id);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Delete notification</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modern compact footer */}
      <footer className="bg-slate-900/60 backdrop-blur-md border-t border-white/10 py-4 text-slate-300/60 fixed bottom-0 left-0 right-0 z-10">
        <div className="container max-w-6xl mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center text-xs">
            <div className="flex items-center space-x-4 mb-2 md:mb-0">
              <span>© 2025 StreamVista</span>
              <span className="hidden md:inline">•</span>
              <Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
              <span className="hidden md:inline">•</span>
              <Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
            </div>
            <div className="flex items-center space-x-3">
              <Link href="/help" className="hover:text-white transition-colors">Help Center</Link>
              <div className="flex items-center space-x-2 ml-2">
                <a href="#" className="hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-github">
                    <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                    <path d="M9 18c-4.51 2-5-2-7-2" />
                  </svg>
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter">
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                  </svg>
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-mail">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}
