/**
 * VidSrc API Service
 *
 * Provides utilities for interacting with the VidSrc API for streaming movies and TV shows.
 * Based on documentation available at https://vidsrc.me/api/
 */

// VidSrc API domains - multiple ones are provided for reliability
// Using recommended domains from the official documentation
export const VIDSRC_DOMAINS = ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net'];
const DEFAULT_DOMAIN = 'vidsrc.xyz'; // Updated to the recommended domain from docs

// Content ID types
export interface ContentIds {
  imdbId?: string;    // IMDb ID in the format 'tt1234567'
  tmdbId?: string;    // TMDb ID as a string or number
}

// Episode information for TV shows
export interface EpisodeInfo {
  season: number;
  episode: number;
}

// Latest content interfaces
export interface VidSrcLatestItem {
  id: string;         // Either IMDb ID or TMDb ID
  idType: 'imdb' | 'tmdb';
  title: string;
  year?: number;
  poster?: string;
  addedDate?: string;
}

export interface VidSrcLatestEpisode extends VidSrcLatestItem {
  season: number;
  episode: number;
  showTitle?: string;
}

/**
 * Gets the embed URL for a movie
 *
 * @param contentIds IMDb or TMDb ID for the movie
 * @param options Additional options like subtitles URL and language
 * @returns The full embed URL for the movie
 */
export function getMovieEmbedUrl(
  contentIds: ContentIds,
  options?: {
    subtitleUrl?: string;
    language?: string;
    domain?: string;
  }
): string {
  const domain = options?.domain || DEFAULT_DOMAIN;
  const baseUrl = `https://${domain}/embed/movie`;

  let params = new URLSearchParams();

  // Must have either imdbId or tmdbId
  if (contentIds.imdbId) {
    params.append('imdb', contentIds.imdbId);
  } else if (contentIds.tmdbId) {
    params.append('tmdb', contentIds.tmdbId);
  } else {
    throw new Error('Either IMDb ID or TMDb ID is required');
  }

  // Add optional parameters
  if (options?.subtitleUrl) {
    params.append('sub_url', options.subtitleUrl);
  }

  if (options?.language) {
    params.append('ds_lang', options.language);
  }

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Gets the embed URL for a TV show episode
 *
 * @param contentIds IMDb or TMDb ID for the show
 * @param episodeInfo Season and episode numbers
 * @param options Additional options like subtitles URL and language
 * @returns The full embed URL for the TV episode
 */
export function getTvEmbedUrl(
  contentIds: ContentIds,
  episodeInfo: EpisodeInfo,
  options?: {
    subtitleUrl?: string;
    language?: string;
    domain?: string;
  }
): string {
  const domain = options?.domain || DEFAULT_DOMAIN;
  const baseUrl = `https://${domain}/embed/tv`;

  let params = new URLSearchParams();

  // Must have either imdbId or tmdbId
  if (contentIds.imdbId) {
    params.append('imdb', contentIds.imdbId);
  } else if (contentIds.tmdbId) {
    params.append('tmdb', contentIds.tmdbId);
  } else {
    throw new Error('Either IMDb ID or TMDb ID is required');
  }

  // Add required season and episode
  params.append('season', episodeInfo.season.toString());
  params.append('episode', episodeInfo.episode.toString());

  // Add optional parameters
  if (options?.subtitleUrl) {
    params.append('sub_url', options.subtitleUrl);
  }

  if (options?.language) {
    params.append('ds_lang', options.language);
  }

  // Debug log to help troubleshoot URL construction without exposing full URL
  console.log(`VidSrc API: Constructed TV URL for content ID: ${contentIds.imdbId || contentIds.tmdbId}, season: ${episodeInfo.season}, episode: ${episodeInfo.episode}`);

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Gets the URL for the latest movies added to the service
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns The URL for the latest movies JSON data
 */
export function getLatestMoviesUrl(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): string {
  return `https://${domain}/movies/latest/page-${pageNumber}.json`;
}

/**
 * Gets the URL for the latest TV shows added to the service
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns The URL for the latest TV shows JSON data
 */
export function getLatestShowsUrl(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): string {
  return `https://${domain}/tvshows/latest/page-${pageNumber}.json`;
}

/**
 * Gets the URL for the latest TV episodes added to the service
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns The URL for the latest episodes JSON data
 */
export function getLatestEpisodesUrl(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): string {
  return `https://${domain}/episodes/latest/page-${pageNumber}.json`;
}

/**
 * Fetches the latest movies from VidSrc API
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns A promise resolving to an array of movie items
 */
export async function fetchLatestMovies(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): Promise<VidSrcLatestItem[]> {
  try {
    const url = getLatestMoviesUrl(pageNumber, domain);
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch latest movies: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Handle different response structures
    // VidSrc API sometimes returns { response: [ items ], pages: number }
    // and sometimes directly returns the array
    if (Array.isArray(data)) {
      return data.map(processLatestItem);
    } else if (data && typeof data === 'object') {
      // Try to extract from response property first (common API pattern)
      if (Array.isArray(data.response)) {
        return data.response.map(processLatestItem);
      }

      // Try other common array properties
      const possibleArrayProps = ['results', 'data', 'items', 'movies'];
      for (const prop of possibleArrayProps) {
        if (Array.isArray(data[prop])) {
          return data[prop].map(processLatestItem);
        }
      }

      // If we can't find an array property, try to extract items from the object
      const items = Object.values(data).filter(val =>
        typeof val === 'object' && val !== null && ('id' in val || 'title' in val || 'imdb_id' in val)
      );

      if (items.length > 0) {
        return items.map(processLatestItem);
      }
    }

    // If all else fails, return empty array
    console.warn('Could not extract movie items from response:', data);
    return [];
  } catch (error) {
    console.error('Error fetching latest movies:', error);
    return [];
  }
}

/**
 * Fetches the latest TV shows from VidSrc API
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns A promise resolving to an array of TV show items
 */
export async function fetchLatestShows(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): Promise<VidSrcLatestItem[]> {
  try {
    const url = getLatestShowsUrl(pageNumber, domain);
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch latest shows: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Handle different response structures
    // VidSrc API sometimes returns { response: [ items ], pages: number }
    // and sometimes directly returns the array
    if (Array.isArray(data)) {
      return data.map(processLatestItem);
    } else if (data && typeof data === 'object') {
      // Try to extract from response property first (common API pattern)
      if (Array.isArray(data.response)) {
        return data.response.map(processLatestItem);
      }

      // Try other common array properties
      const possibleArrayProps = ['results', 'data', 'items', 'shows'];
      for (const prop of possibleArrayProps) {
        if (Array.isArray(data[prop])) {
          return data[prop].map(processLatestItem);
        }
      }

      // If we can't find an array property, try to extract items from the object
      const items = Object.values(data).filter(val =>
        typeof val === 'object' && val !== null && ('id' in val || 'title' in val || 'imdb_id' in val)
      );

      if (items.length > 0) {
        return items.map(processLatestItem);
      }
    }

    // If all else fails, return empty array
    console.warn('Could not extract show items from response:', data);
    return [];
  } catch (error) {
    console.error('Error fetching latest shows:', error);
    return [];
  }
}

/**
 * Fetches the latest TV episodes from VidSrc API
 *
 * @param pageNumber The page number for pagination (starting from 1)
 * @param domain Optional domain to use
 * @returns A promise resolving to an array of episode items
 */
export async function fetchLatestEpisodes(pageNumber: number = 1, domain: string = DEFAULT_DOMAIN): Promise<VidSrcLatestEpisode[]> {
  try {
    const url = getLatestEpisodesUrl(pageNumber, domain);
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch latest episodes: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Handle different response structures
    // VidSrc API sometimes returns { response: [ items ], pages: number }
    // and sometimes directly returns the array
    if (Array.isArray(data)) {
      return data.map(processLatestEpisode);
    } else if (data && typeof data === 'object') {
      // Try to extract from response property first (common API pattern)
      if (Array.isArray(data.response)) {
        return data.response.map(processLatestEpisode);
      }

      // Try other common array properties
      const possibleArrayProps = ['results', 'data', 'items', 'episodes'];
      for (const prop of possibleArrayProps) {
        if (Array.isArray(data[prop])) {
          return data[prop].map(processLatestEpisode);
        }
      }

      // If we can't find an array property, try to extract items from the object
      const items = Object.values(data).filter(val =>
        typeof val === 'object' && val !== null &&
        (('id' in val || 'imdb_id' in val) && ('season' in val || 'episode' in val))
      );

      if (items.length > 0) {
        return items.map(processLatestEpisode);
      }
    }

    // If all else fails, return empty array
    console.warn('Could not extract episode items from response:', data);
    return [];
  } catch (error) {
    console.error('Error fetching latest episodes:', error);
    return [];
  }
}

/**
 * Processes a raw item from the latest content API
 * @param item Raw item from the API
 * @returns Processed item with consistent structure
 */
function processLatestItem(item: any): VidSrcLatestItem {
  // Handle different ID formats from VidSrc API
  let id = item.id || item.imdb_id || '';
  let idType: 'imdb' | 'tmdb' = 'imdb';

  // Determine if it's an IMDb or TMDb ID
  if (id.startsWith('tt')) {
    idType = 'imdb';
  } else if (item.tmdb_id) {
    id = item.tmdb_id.toString();
    idType = 'tmdb';
  }

  return {
    id: id,
    idType,
    title: item.title || 'Unknown Title',
    year: item.year ? parseInt(item.year) : undefined,
    poster: item.poster || undefined,
    addedDate: item.added || undefined,
  };
}

/**
 * Processes a raw episode from the latest content API
 * @param item Raw episode item from the API
 * @returns Processed episode with consistent structure
 */
function processLatestEpisode(item: any): VidSrcLatestEpisode {
  const baseItem = processLatestItem(item);

  return {
    ...baseItem,
    season: item.season ? parseInt(item.season) : 1,
    episode: item.episode ? parseInt(item.episode) : 1,
    showTitle: item.show_title || undefined,
  };
}

/**
 * Attempts to find the most reliable VidSrc domain by testing connection speeds
 *
 * @returns {Promise<string>} The most responsive domain
 */
export async function getOptimalVidSrcDomain(): Promise<string> {
  // Start with a copy of the domain list
  const domains = [...VIDSRC_DOMAINS];
  let bestDomain = DEFAULT_DOMAIN;
  let bestTime = Infinity;

  try {
    // Test each domain for response time
    await Promise.all(domains.map(async (domain) => {
      const startTime = Date.now();
      try {
        // Make a simple HEAD request to check response
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3s timeout

        const response = await fetch(`https://${domain}`, {
          method: 'HEAD',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const time = Date.now() - startTime;
          console.log(`VidSrc domain ${domain} responded in ${time}ms`);

          if (time < bestTime) {
            bestTime = time;
            bestDomain = domain;
          }
        }
      } catch (err) {
        console.warn(`VidSrc domain ${domain} failed to respond:`, err);
      }
    }));

    console.log(`VidSrc optimal domain is ${bestDomain} (${bestTime}ms)`);
    return bestDomain;
  } catch (error) {
    console.error('Error finding optimal VidSrc domain:', error);
    return DEFAULT_DOMAIN; // Fallback to default
  }
}