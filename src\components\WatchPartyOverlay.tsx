"use client"

import { useEffect, useRef, useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { ChevronRight, MessageCircle, SmilePlus, Users } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useWatchParty } from "@/lib/WatchPartyContext"
import { WatchPartyMember, ChatMessage, WatchPartyState } from "@/lib/WatchPartyContext"
import data from "@emoji-mart/data"
import Picker from "@emoji-mart/react"
import { cn } from "@/lib/utils"
import { WATCH_PARTY_EVENTS } from "@/lib/pusher-server"


interface WatchPartyOverlayProps {
  className?: string
}

// Helper to work with different message formats
interface DisplayMessage {
  id: string;
  userId: string;
  userName: string;
  content: string;
  isSystem: boolean;
}

// EmojiData interface for the emoji picker
interface EmojiData {
  native: string;
  id: string;
  name: string;
  [key: string]: unknown;
}

// Define our own interface for the WatchPartyContext which matches what we need
interface WatchPartyContextInterface {
  currentParty: WatchPartyState | null;
  sendMessage: (message: string) => void;
  sendReaction: (reaction: string) => void;
  connectionState: 'connected' | 'connecting' | 'disconnected' | 'failed';
  isHost?: boolean;
  error?: null | string;
}

// Type for fallback implementation when context is not available
interface WatchPartyFallback {
  currentParty: null;
  sendMessage: (message: string) => void;
  sendReaction: (reaction: string) => void;
  connectionState: 'disconnected';
  error: null;
}

// Add a custom error handler for the watch party hook
function useWatchPartyWithFallback(): WatchPartyContextInterface | WatchPartyFallback {
  try {
    // Try to use the regular hook
    return useWatchParty();
  } catch (err) {
    // If it fails, log it and return a mock implementation
    console.error('Error using WatchParty hook in overlay:', err);

    // Return mock implementation
    return {
      currentParty: null,
      sendMessage: () => {},
      sendReaction: () => {},
      connectionState: 'disconnected' as const,
      error: null
    };
  }
}

export function WatchPartyOverlay({ className }: WatchPartyOverlayProps) {
  const watchParty = useWatchPartyWithFallback();
  
  // Destructure from watchParty with proper type awareness
  const {
    currentParty,
    sendMessage,
    sendReaction,
    connectionState,
  } = watchParty;

  const [isOpen, setIsOpen] = useState(true);
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // No longer need to use emit since we're using Pusher now
  const handleTypingStart = () => {
    // Implementation removed as we're using Pusher now
    // Typing indicators could be implemented through Pusher channels if needed
  };

  const handleTypingStop = () => {
    // Implementation removed as we're using Pusher now
  };

  const currentUser = currentParty?.members.find((m: WatchPartyMember) => m.id === currentParty.hostId);

  // Convert messages to a consistent format
  const formatMessages = (): DisplayMessage[] => {
    if (!currentParty?.messages) return [];

    return currentParty.messages.map((msg: ChatMessage) => ({
      id: msg.id,
      userId: msg.memberId,
      userName: msg.memberName,
      content: msg.content,
      isSystem: msg.type === 'system'
    }));
  }

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [currentParty?.messages])

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim() || !currentUser) return

    sendMessage(message)
    setMessage("")
    setIsTyping(false)
    handleTypingStop()
  }

  const handleTyping = () => {
    if (!currentUser) return

    if (!isTyping) {
      setIsTyping(true)
      handleTypingStart()
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      handleTypingStop()
    }, 2000)
  }

  if (!currentParty) return null

  const displayMessages = formatMessages();

  return (
    <motion.div
      className={cn(
        "absolute top-0 right-0 bottom-0 bg-vista-dark-lighter/95 backdrop-blur-sm w-80 flex flex-col border-l border-vista-light/10 shadow-xl",
        !isOpen && "hidden"
      )}
      initial={{ x: 80, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 80, opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Header */}
      <div className="p-4 border-b border-vista-light/10 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="w-5 h-5 text-vista-light" />
          <h3 className="font-medium text-vista-light">Watch Party</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(false)}
          className="h-auto p-1"
        >
          <ChevronRight className="w-5 h-5 text-vista-light" />
        </Button>
      </div>

      {/* Connection Status - removed since we're using Pusher now */}

      {/* Rest of the component */}
      <ScrollArea className="flex-1 p-4" ref={scrollRef}>
        <div className="space-y-4">
          {displayMessages.map((msg) => (
            <div
              key={msg.id}
              className={`flex items-start gap-2 ${
                msg.isSystem
                  ? "justify-center"
                  : msg.userId === currentParty.hostId
                  ? "justify-end"
                  : ""
              }`}
            >
              {!msg.isSystem && msg.userId !== currentParty.hostId && (
                <Avatar className="h-8 w-8">
                  <AvatarImage src={`https://avatar.vercel.sh/${msg.userId}.png`} />
                  <AvatarFallback>{msg.userName[0]}</AvatarFallback>
                </Avatar>
              )}
              <div
                className={`rounded-lg px-3 py-2 ${
                  msg.isSystem
                    ? "bg-muted text-muted-foreground"
                    : msg.userId === currentParty.hostId
                    ? "bg-primary text-primary-foreground"
                    : "bg-accent"
                }`}
              >
                {!msg.isSystem && msg.userId !== currentParty.hostId && (
                  <div className="mb-1 text-xs font-medium">{msg.userName}</div>
                )}
                {msg.content}
              </div>
              {!msg.isSystem && msg.userId === currentParty.hostId && (
                <Avatar className="h-8 w-8">
                  <AvatarImage src={`https://avatar.vercel.sh/${msg.userId}.png`} />
                  <AvatarFallback>{msg.userName[0]}</AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      <form onSubmit={handleSendMessage} className="p-4 border-t">
        <div className="flex gap-2">
          <Textarea
            value={message}
            onChange={(e) => {
              setMessage(e.target.value)
              handleTyping()
            }}
            placeholder="Type a message..."
            className="min-h-0 h-9 resize-none"
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" className="shrink-0">
                <SmilePlus className="h-5 w-5" />
              </Button>
            </PopoverTrigger>
            <PopoverContent side="top" align="end" className="p-0">
              <Picker
                data={data}
                onEmojiSelect={(emoji: EmojiData) => sendReaction(emoji.native)}
                theme="light"
              />
            </PopoverContent>
          </Popover>
          <Button type="submit" size="icon" className="shrink-0">
            <MessageCircle className="h-5 w-5" />
          </Button>
        </div>
      </form>
    </motion.div>
  )
}