'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';

interface BannerAdUploadWidgetProps {
  onImageUploaded: (imageUrl: string) => void;
  currentImageUrl?: string;
  onImageRemoved?: () => void;
  disabled?: boolean;
}

declare global {
  interface Window {
    cloudinary: any;
  }
}

const CLOUDINARY_CLOUD_NAME = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'streamvista';
const CLOUDINARY_UPLOAD_PRESET = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'streamvista_unsigned';

export default function BannerAdUploadWidget({
  onImageUploaded,
  currentImageUrl,
  onImageRemoved,
  disabled = false
}: BannerAdUploadWidgetProps) {
  const [loaded, setLoaded] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const cloudinaryRef = useRef<any>(null);
  const widgetRef = useRef<any>(null);

  useEffect(() => {
    // Load Cloudinary script dynamically - using the CORRECT URL
    if (!loaded) {
      const script = document.createElement('script');
      script.src = 'https://upload-widget.cloudinary.com/global/all.js';
      script.async = true;
      script.onload = () => setLoaded(true);
      document.body.appendChild(script);
      return () => {
        document.body.removeChild(script);
        // Reset uploading state when component unmounts
        if (isUploading) {
          setIsUploading(false);
        }
      };
    }

    if (loaded && !widgetRef.current) {
      cloudinaryRef.current = window.cloudinary;
      widgetRef.current = cloudinaryRef.current.createUploadWidget(
        {
          cloudName: CLOUDINARY_CLOUD_NAME,
          uploadPreset: CLOUDINARY_UPLOAD_PRESET,
          folder: 'streamvista/banner-ads',
          // Let Cloudinary generate the public ID automatically to avoid timestamp mismatches
          // publicId will be generated based on the original filename or auto-generated
          sources: ['local', 'url'],
          maxImageFileSize: 10000000, // 10MB
          multiple: false,
          cropping: true,
          croppingAspectRatio: 16 / 9, // Banner aspect ratio
          // CRITICAL: Set z-index higher than dialog (z-50 = 50, so we use 9999)
          zIndex: 9999,
          styles: {
            palette: {
              window: '#121212',
              sourceBg: '#1a1a1a',
              windowBorder: '#1a1a1a',
              tabIcon: '#007bff',
              inactiveTabIcon: '#6c757d',
              menuIcons: '#007bff',
              link: '#007bff',
              action: '#007bff',
              inProgress: '#007bff',
              complete: '#28a745',
              error: '#dc3545',
              textDark: '#f8f9fa',
              textLight: '#f8f9fa',
            },
          },
        },
        (error: any, result: any) => {
          // Handle widget close event
          if (result && result.event === 'close') {
            setIsUploading(false);
            // Clean up when widget closes
            cleanupCloudinaryWidget();
            return;
          }

          // Handle widget display changed event - CRITICAL for z-index fix
          if (result && result.event === 'display-changed') {
            // Disable dialog backdrop pointer events when widget opens
            disableDialogBackdrop();

            // Force z-index on all Cloudinary elements when widget opens
            setTimeout(() => {
              const cloudinaryElements = document.querySelectorAll('[class*="cloudinary"], [id*="cloudinary"]');
              cloudinaryElements.forEach((el) => {
                (el as HTMLElement).style.zIndex = '9999';
                (el as HTMLElement).style.position = 'fixed';
              });
            }, 100);
          }

          // Handle successful upload
          if (!error && result && result.event === 'success') {
            // Get secure URL from result and pass to onImageUploaded callback
            const secureUrl = result.info.secure_url;
            console.log('Cloudinary upload successful:', {
              secureUrl,
              publicId: result.info.public_id,
              originalFilename: result.info.original_filename,
              format: result.info.format
            });
            onImageUploaded(secureUrl);
            setIsUploading(false);
            // Clean up when upload is successful
            cleanupCloudinaryWidget();
            toast.success('Banner image uploaded successfully!');
          }
          // Handle upload error
          else if (error) {
            setIsUploading(false);
            // Clean up on error too
            cleanupCloudinaryWidget();
            toast.error('Error uploading banner image');
            console.error('Cloudinary upload error:', error);
          }
        }
      );
    }
  }, [loaded, onImageUploaded]);

  // Helper functions to manage dialog backdrop
  const disableDialogBackdrop = () => {
    // Find and disable dialog overlay/backdrop pointer events
    const dialogOverlays = document.querySelectorAll('[data-radix-dialog-overlay]');
    dialogOverlays.forEach((overlay) => {
      (overlay as HTMLElement).style.pointerEvents = 'none';
    });

    // Also target by class name as backup
    const overlayElements = document.querySelectorAll('.fixed.inset-0.z-50');
    overlayElements.forEach((overlay) => {
      if (overlay.classList.contains('bg-black/80') || overlay.classList.contains('backdrop-blur-sm')) {
        (overlay as HTMLElement).style.pointerEvents = 'none';
      }
    });
  };

  const enableDialogBackdrop = () => {
    // Re-enable dialog overlay/backdrop pointer events
    const dialogOverlays = document.querySelectorAll('[data-radix-dialog-overlay]');
    dialogOverlays.forEach((overlay) => {
      (overlay as HTMLElement).style.pointerEvents = 'auto';
    });

    // Also target by class name as backup
    const overlayElements = document.querySelectorAll('.fixed.inset-0.z-50');
    overlayElements.forEach((overlay) => {
      if (overlay.classList.contains('bg-black/80') || overlay.classList.contains('backdrop-blur-sm')) {
        (overlay as HTMLElement).style.pointerEvents = 'auto';
      }
    });
  };

  const cleanupCloudinaryWidget = () => {
    // Remove active class from body
    document.body.classList.remove('cloudinary-widget-active');

    // Re-enable dialog backdrop
    enableDialogBackdrop();

    // Remove injected CSS
    const existingStyle = document.getElementById('cloudinary-z-index-fix');
    if (existingStyle) {
      existingStyle.remove();
    }
  };

  const handleClick = () => {
    setIsUploading(true);

    // Inject CSS to ensure widget appears above dialog and disable dialog backdrop
    const style = document.createElement('style');
    style.id = 'cloudinary-z-index-fix';
    style.textContent = `
      /* Force Cloudinary widget above all dialogs */
      .cloudinary-widget-overlay,
      .cloudinary-widget,
      .cloudinary-widget-modal,
      .cloudinary-upload-widget,
      .cloudinary-upload-widget-overlay,
      .cloudinary-upload-widget-modal,
      [class*="cloudinary"],
      [id*="cloudinary"] {
        z-index: 9999 !important;
        position: fixed !important;
      }

      /* Ensure overlay is clickable */
      .cloudinary-widget-overlay,
      .cloudinary-upload-widget-overlay {
        pointer-events: auto !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
      }

      /* Ensure modal content is clickable and properly positioned */
      .cloudinary-widget-modal,
      .cloudinary-upload-widget-modal {
        pointer-events: auto !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 9999 !important;
      }

      /* CRITICAL: Disable dialog backdrop when Cloudinary is open */
      .cloudinary-widget-active [data-radix-dialog-overlay],
      .cloudinary-widget-active .fixed.inset-0.z-50.bg-black\\/80 {
        pointer-events: none !important;
      }

      /* Ensure Cloudinary widget content is always clickable */
      [class*="cloudinary"] * {
        pointer-events: auto !important;
      }
    `;

    // Remove existing style if it exists
    const existingStyle = document.getElementById('cloudinary-z-index-fix');
    if (existingStyle) {
      existingStyle.remove();
    }

    document.head.appendChild(style);

    if (widgetRef.current) {
      // Add class to body to indicate Cloudinary widget is active
      document.body.classList.add('cloudinary-widget-active');

      // Disable dialog backdrop immediately
      disableDialogBackdrop();

      // Open the widget
      widgetRef.current.open();

      // Additional z-index enforcement after opening
      setTimeout(() => {
        const cloudinaryElements = document.querySelectorAll('[class*="cloudinary"], [id*="cloudinary"]');
        cloudinaryElements.forEach((el) => {
          (el as HTMLElement).style.zIndex = '9999';
          (el as HTMLElement).style.position = 'fixed';
        });

        // Double-check backdrop is disabled
        disableDialogBackdrop();
      }, 200);
    }
  };

  const handleRemoveImage = () => {
    if (onImageRemoved) {
      onImageRemoved();
      toast.success('Banner image removed');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-vista-light">
          Banner Image
        </label>
        {currentImageUrl && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleRemoveImage}
            disabled={disabled}
            className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
          >
            <X className="h-4 w-4 mr-1" />
            Remove
          </Button>
        )}
      </div>

      {currentImageUrl ? (
        <div className="relative group">
          <div className="relative w-full h-48 rounded-lg overflow-hidden border border-vista-light/20">
            {/* Debug info */}
            <div className="absolute top-2 left-2 z-10 bg-black/70 text-white text-xs p-1 rounded">
              URL: {currentImageUrl.substring(0, 50)}...
            </div>
            <Image
              src={currentImageUrl}
              alt="Banner preview"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              onError={(e) => {
                console.error('Image failed to load:', currentImageUrl);
                console.error('Error details:', e);
              }}
              onLoad={() => {
                console.log('Image loaded successfully:', currentImageUrl);
              }}
              unoptimized={true}
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button
                type="button"
                variant="secondary"
                onClick={handleClick}
                disabled={disabled || isUploading || !loaded}
                className="bg-white/20 hover:bg-white/30 text-white border-white/20"
              >
                <Upload className="h-4 w-4 mr-2" />
                Change Image
              </Button>
            </div>
          </div>
          <p className="text-xs text-vista-light/60 mt-2">
            Recommended: 1920x1080px (16:9 aspect ratio) for best results
          </p>
        </div>
      ) : (
        <div className="border-2 border-dashed border-vista-light/20 rounded-lg p-8 text-center">
          <ImageIcon className="h-12 w-12 text-vista-light/40 mx-auto mb-4" />
          <p className="text-vista-light/60 mb-4">
            Upload a banner image for your ad
          </p>
          <p className="text-xs text-vista-light/40 mb-4">
            Recommended: 1920x1080px (16:9 aspect ratio)<br />
            Maximum file size: 10MB<br />
            Supported formats: JPG, PNG, GIF, WebP
          </p>
          <Button
            type="button"
            onClick={handleClick}
            disabled={disabled || isUploading || !loaded}
            className="bg-vista-blue hover:bg-vista-blue/90"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isUploading ? 'Uploading...' : 'Upload Image'}
          </Button>
        </div>
      )}
    </div>
  );
}
