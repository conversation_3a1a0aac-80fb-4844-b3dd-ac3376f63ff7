'use client'

import { useState, useEffect } from 'react'
import { Users, Crown, CheckCircle2, Clock, UserCircle, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { WatchPartyMember } from '@/lib/WatchPartyContext'
import { motion, AnimatePresence } from 'framer-motion'

interface WatchPartyMembersListProps {
  members: WatchPartyMember[];
  currentUserId: string;
  onKickMember?: (memberId: string) => void;
  isHost: boolean;
}

export default function WatchPartyMembersList({
  members,
  currentUserId,
  onKickMember,
  isHost
}: WatchPartyMembersListProps) {
  // Sort members: host first, then alphabetically
  const sortedMembers = [...members].sort((a, b) => {
    // Host always comes first
    if (a.isHost && !b.isHost) return -1;
    if (!a.isHost && b.isHost) return 1;

    // Then sort by ready status
    if (a.isReady && !b.isReady) return -1;
    if (!a.isReady && b.isReady) return 1;

    // Finally sort alphabetically
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="wp-glass-card rounded-xl overflow-hidden shadow-lg border border-white/10">
      <div className="bg-gradient-to-r from-slate-800/80 to-slate-900/80 p-4 border-b border-white/10 flex items-center justify-between">
        <div className="flex items-center">
          <div className="bg-sky-500/20 p-1.5 rounded-lg mr-3">
            <Users className="w-4 h-4 text-sky-400" />
          </div>
          <h3 className="text-sm font-medium text-white">Party Members</h3>
        </div>
        <Badge variant="outline" className="bg-black/30 text-sky-300 border-sky-500/30 px-2.5">
          {members.length}
        </Badge>
      </div>

      <ScrollArea className="h-[350px] wp-scrollbar">
        <AnimatePresence>
          <div className="p-3 space-y-2">
            {sortedMembers.map((member) => (
              <motion.div
                key={member.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className={`
                  flex items-center justify-between p-3 rounded-lg
                  ${member.id === currentUserId
                    ? 'bg-gradient-to-r from-sky-500/20 to-blue-500/10 border border-sky-500/30'
                    : 'hover:bg-black/20 border border-white/5 hover:border-white/10'}
                  transition-all duration-200
                `}
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="w-10 h-10 border-2 border-white/10 shadow-md">
                    <AvatarImage src={member.avatar || `https://avatar.vercel.sh/${member.id}.png`} />
                    <AvatarFallback className="bg-gradient-to-br from-sky-500 to-blue-600 text-white">
                      {member.name[0]}
                    </AvatarFallback>
                  </Avatar>

                  <div>
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-white mr-2">
                        {member.name} {member.id === currentUserId && <span className="text-sky-300/70">(you)</span>}
                      </span>

                      {member.isHost && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Badge variant="secondary" className="h-5 px-1.5 flex items-center gap-1 bg-yellow-500/20 border-yellow-500/30 text-yellow-300">
                                <Crown className="w-3 h-3 text-yellow-400" fill="currentColor" />
                                <span className="text-[10px]">Host</span>
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent className="bg-slate-900 border-yellow-500/30 text-white">
                              <p className="text-xs">Party Host</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>

                    <div className="flex items-center mt-1.5">
                      {member.isReady ? (
                        <Badge variant="outline" className="h-5 px-1.5 flex items-center gap-1 bg-green-500/20 text-green-300 border-green-500/30">
                          <CheckCircle2 className="w-3 h-3" fill="currentColor" />
                          <span className="text-[10px]">Ready</span>
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="h-5 px-1.5 flex items-center gap-1 bg-amber-500/20 text-amber-300 border-amber-500/30">
                          <Clock className="w-3 h-3" />
                          <span className="text-[10px]">Not Ready</span>
                        </Badge>
                      )}

                      <span className="text-xs text-slate-400 ml-2">
                        Joined {new Date(member.joinedAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </div>
                </div>

                {isHost && currentUserId !== member.id && onKickMember && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-slate-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg"
                    onClick={() => onKickMember(member.id)}
                  >
                    <X className="w-3.5 h-3.5 mr-1.5" />
                    Kick
                  </Button>
                )}
              </motion.div>
            ))}

            {members.length === 0 && (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="bg-slate-800/50 p-4 rounded-full mb-4">
                  <Users className="w-10 h-10 text-slate-500" />
                </div>
                <h3 className="text-lg font-medium text-slate-300 mb-1">No Members Yet</h3>
                <p className="text-slate-400 text-sm max-w-xs text-center">Share your party code with friends to invite them to join</p>
              </div>
            )}
          </div>
        </AnimatePresence>
      </ScrollArea>
    </div>
  )
}