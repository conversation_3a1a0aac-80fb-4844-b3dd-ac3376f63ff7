'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { 
  getMovieEmbedUrl, 
  getTvEmbedUrl, 
  ContentIds, 
  EpisodeInfo,
  VIDSRC_DOMAINS 
} from '@/lib/vidsrc-api';
import { Button } from '@/components/ui/button';
import { ArrowLeft, RefreshCcw, Info } from 'lucide-react';
import Link from 'next/link';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * Generic watch page with query parameter handling
 * This is used for direct VidSrc playback via query parameters
 * It's separate from the watch/[id] route, which goes through content ID resolution first
 */
export default function WatchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [embedUrl, setEmbedUrl] = useState<string | null>(null);
  const [title, setTitle] = useState('Loading...');
  const [error, setError] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<string>('1');
  const [selectedEpisode, setSelectedEpisode] = useState<string>('1');
  const [showEpisodeSelector, setShowEpisodeSelector] = useState(false);
  const [seasons, setSeasons] = useState<number[]>([1]);
  const [episodes, setEpisodes] = useState<number[]>([1]);
  const [contentIdInfo, setContentIdInfo] = useState<{ids: ContentIds; type: string; title: string | null; contentId?: string}>({
    ids: {}, 
    type: '', 
    title: null
  });
  const [selectedDomain, setSelectedDomain] = useState<string>(VIDSRC_DOMAINS[0]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load content and create embed URL
  const loadContent = () => {
    try {
      if (!searchParams) {
        console.error('SearchParams not available yet');
        return;
      }

      const id = searchParams.get('id');
      const idType = searchParams.get('idType') as 'imdb' | 'tmdb';
      const type = searchParams.get('type');
      const providedTitle = searchParams.get('title');
      const contentId = searchParams.get('contentId');
      const season = searchParams.get('season');
      const episode = searchParams.get('episode');
      
      // Debug query parameters
      console.log('Watch page query parameters:', {
        id, idType, type, providedTitle, contentId, season, episode
      });
      
      if (!id || !idType || !type) {
        setError('Missing required parameters');
        return;
      }
      
      const contentIds: ContentIds = {};
      if (idType === 'imdb') {
        contentIds.imdbId = id;
      } else {
        contentIds.tmdbId = id;
      }
      
      // Store content info for episode selector
      setContentIdInfo({
        ids: contentIds,
        type,
        title: providedTitle,
        contentId: contentId || undefined
      });
      
      if (type === 'movie') {
        // Handling movie embed
        const url = getMovieEmbedUrl(contentIds, { domain: selectedDomain });
        setEmbedUrl(url);
        setTitle(providedTitle || 'Movie');
      } else if (type === 'tv') {
        // Handling TV show embed
        if (!season || !episode) {
          // If season and episode are not provided, show selector
          setShowEpisodeSelector(true);
          setTitle(providedTitle || 'TV Show');
          
          // Generate some seasons and episodes for selection
          // In a real app, this data would come from TMDb API
          setSeasons(Array.from({length: 5}, (_, i) => i + 1));
          setEpisodes(Array.from({length: 10}, (_, i) => i + 1));
          return;
        }
        
        const episodeInfo: EpisodeInfo = {
          season: parseInt(season),
          episode: parseInt(episode)
        };
        
        const url = getTvEmbedUrl(contentIds, episodeInfo, { domain: selectedDomain });
        setEmbedUrl(url);
        
        const showTitle = providedTitle || 'TV Show';
        setTitle(`${showTitle} - S${season} E${episode}`);
      } else {
        setError('Invalid content type');
      }
    } catch (error) {
      console.error('Error in loadContent:', error);
      setError(`Failed to load content: ${error}`);
    }
  };

  // Initial load
  useEffect(() => {
    loadContent();
  }, [searchParams, selectedDomain]);

  // Handle domain change
  const handleDomainChange = (domain: string) => {
    setSelectedDomain(domain);
    setEmbedUrl(null); // Reset embed URL to trigger reload
  };

  // Handle video refresh
  const handleRefreshVideo = () => {
    setIsRefreshing(true);
    setEmbedUrl(null);
    
    // Short delay to ensure iframe reloads
    setTimeout(() => {
      loadContent();
      setIsRefreshing(false);
    }, 500);
  };

  // Handle episode selection
  const handleWatchEpisode = () => {
    if (!contentIdInfo.ids || !contentIdInfo.type) return;
    
    if (contentIdInfo.type === 'tv') {
      const episodeInfo: EpisodeInfo = {
        season: parseInt(selectedSeason),
        episode: parseInt(selectedEpisode)
      };
      
      const url = getTvEmbedUrl(contentIdInfo.ids, episodeInfo, { domain: selectedDomain });
      setEmbedUrl(url);
      
      const showTitle = contentIdInfo.title || 'TV Show';
      setTitle(`${showTitle} - S${selectedSeason} E${selectedEpisode}`);
      
      setShowEpisodeSelector(false);
    }
  };

  // Generate back link to content details
  const getBackLink = () => {
    if (contentIdInfo.contentId) {
      // If we have a contentId, use it to go back to the detail page
      return `/${contentIdInfo.type === 'tv' ? 'shows' : 'movies'}/${contentIdInfo.contentId}`;
    }
    // Otherwise go to home
    return '/';
  };

  return (
    <div 
      className="min-h-screen bg-vista-dark text-vista-light" 
      style={{ paddingTop: '90px' }}
    >
      {/* Header */}
      <div className="p-4 mt-8 flex items-center justify-between border-b border-vista-dark-lighter">
        <div className="flex items-center gap-2">
          <Link href={getBackLink()}>
            <Button variant="ghost" size="sm" className="text-vista-light">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-xl font-semibold">{title}</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefreshVideo}
            disabled={isRefreshing}
            className="text-vista-light"
          >
            <RefreshCcw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Select value={selectedDomain} onValueChange={handleDomainChange}>
            <SelectTrigger className="w-[180px] bg-vista-dark-lighter text-vista-light border-vista-dark-lighter">
              <SelectValue placeholder="Select domain" />
            </SelectTrigger>
            <SelectContent className="bg-vista-dark-lighter text-vista-light border-vista-dark-lighter">
              {VIDSRC_DOMAINS.map(domain => (
                <SelectItem key={domain} value={domain}>
                  {domain}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Episode selector */}
      {showEpisodeSelector && (
        <div className="max-w-4xl mx-auto p-6 my-4 bg-vista-dark-lighter rounded-lg">
          <h2 className="text-xl font-medium mb-4">Select Episode</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm mb-2">Season</label>
              <Select value={selectedSeason} onValueChange={setSelectedSeason}>
                <SelectTrigger className="bg-vista-dark text-vista-light border-vista-dark">
                  <SelectValue placeholder="Select season" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark text-vista-light border-vista-dark">
                  {seasons.map(season => (
                    <SelectItem key={season} value={season.toString()}>
                      Season {season}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-2">Episode</label>
              <Select value={selectedEpisode} onValueChange={setSelectedEpisode}>
                <SelectTrigger className="bg-vista-dark text-vista-light border-vista-dark">
                  <SelectValue placeholder="Select episode" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark text-vista-light border-vista-dark">
                  {episodes.map(episode => (
                    <SelectItem key={episode} value={episode.toString()}>
                      Episode {episode}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button onClick={handleWatchEpisode} className="bg-vista-blue hover:bg-vista-blue/90">
            Watch Episode
          </Button>
        </div>
      )}
      
      {/* Player */}
      <div className="max-w-6xl mx-auto p-4 pt-8">
        {error ? (
          <div className="bg-red-900/20 p-6 rounded-lg flex flex-col items-center justify-center aspect-video">
            <div className="text-red-400 text-xl mb-4">{error}</div>
            <Button variant="outline" onClick={() => router.push('/')}>
              Return Home
            </Button>
          </div>
        ) : embedUrl ? (
          <div className="aspect-video bg-black rounded-lg overflow-hidden relative">
            <iframe
              src={embedUrl}
              className="w-full h-full"
              frameBorder="0"
              allowFullScreen
            />
          </div>
        ) : (
          <div className="aspect-video bg-vista-dark-lighter flex items-center justify-center rounded-lg">
            <div className="animate-pulse text-vista-light">Loading player...</div>
          </div>
        )}
        
        {/* Player explanation */}
        <div className="mt-6 bg-vista-dark-lighter p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="w-5 h-5 text-vista-light/60 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-vista-light font-medium">About VidSrc Playback</h3>
              <p className="text-vista-light/70 text-sm mt-1">
                This player uses the VidSrc API to stream content. If playback fails, try changing the domain using the selector above.
                For TV shows, you can select different episodes using the episode selector.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 