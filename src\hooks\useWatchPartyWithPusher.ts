import { useState, useEffect, useCallback, useRef } from 'react';
import { pusherClient } from '@/lib/pusher-client';
import { WATCH_PARTY_EVENTS } from '@/lib/pusher-server';
import { v4 as uuidv4 } from 'uuid';

export interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  joinedAt: string;
  isReady: boolean;
}

export interface ChatMessage {
  id: string;
  memberId: string;
  memberName: string;
  avatar?: string;  // Added avatar property
  content: string;
  timestamp: string;
  type: 'chat' | 'system' | 'reaction';
}

export interface WatchPartyState {
  id: string;
  contentId: string;
  content?: any;
  hostId: string;
  title?: string;           // Title of the party
  contentType?: string;     // Type of content (video, movie, show)
  hostName?: string;        // Name of the host
  members: WatchPartyMember[];
  messages: ChatMessage[];
  currentTime: number;
  isPlaying: boolean;
  createdAt: string;
  startedAt?: string;
  currentSeason?: number;
  currentEpisode?: number;
}

export interface IContent {
  id: string;
  title: string;
  posterPath: string;
  backdropPath: string;
  type: 'movie' | 'show';
  overview: string;
  year: string;
  rating: number;
  genres?: string[];
}

export function useWatchPartyWithPusher() {
  // Use a persisted userId that survives page refreshes
  const [userId] = useState<string>(() => {
    // Check if we have a stored userId in localStorage
    const storedId = typeof window !== 'undefined' ? localStorage.getItem('watchPartyUserId') : null;

    // If we have a stored ID, use it, otherwise generate a new one
    const id = storedId || uuidv4().substring(0, 8);

    // Save the ID to localStorage for future use
    if (typeof window !== 'undefined' && !storedId) {
      localStorage.setItem('watchPartyUserId', id);
    }

    console.log('[Watch Party] Using user ID:', id);
    return id;
  });

  // Get the authenticated user's name from localStorage
  const [userName, setUserName] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      // Try to get the user data from localStorage
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          if (user && user.name) {
            return user.name;
          }
        } catch (error) {
          console.error('Error parsing user data:', error);
        }
      }

      // Try to get the active profile from localStorage
      const profileData = localStorage.getItem('activeProfile');
      if (profileData) {
        try {
          const profile = JSON.parse(profileData);
          if (profile && profile.name) {
            return profile.name;
          }
        } catch (error) {
          console.error('Error parsing profile data:', error);
        }
      }
    }

    // Fallback to a random name if no user data is available
    return `User-${Math.floor(Math.random() * 1000)}`;
  });

  const [currentParty, setCurrentParty] = useState<WatchPartyState | null>(null);
  const [availableParties, setAvailableParties] = useState<WatchPartyState[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // References to channels
  const mainChannelRef = useRef<any>(null);
  const partyChannelRef = useRef<any>(null);
  const lastFetchTimeRef = useRef<number>(0);

  // Fetch available parties
  const fetchAvailableParties = useCallback(async (forceFetch: boolean = false) => {
    // Implement debouncing to prevent rapid sequential calls
    const now = Date.now();
    const minTimeBetweenFetches = 1000; // 1 second minimum between fetches (reduced from 2s)

    if (!forceFetch && now - lastFetchTimeRef.current < minTimeBetweenFetches) {
      // Skip if fetching too frequently and not forced
      console.log('[Watch Party] Skipping fetch due to debounce', {
        timeSinceLastFetch: now - lastFetchTimeRef.current,
        minTimeBetweenFetches
      });
      return availableParties; // Return current state for debounced requests
    }

    try {
      console.log('[Watch Party] Starting parties fetch (forced:', forceFetch, ')');
      setIsLoading(true);
      setError(null);

      // Update timestamp before fetch
      lastFetchTimeRef.current = now;

      console.log('[Watch Party] Fetching available parties (forced:', forceFetch, ')');

      // Using POST instead of GET since the server likely expects POST for all requests
      let response;
      try {
        response = await fetch('/api/watch-party', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event: 'get-watch-parties'
          }),
          // Important to prevent caching for party updates
          cache: 'no-store',
          next: { revalidate: 0 }
        });

        // Add logging for response status
        console.log(`[Watch Party] Fetch response status: ${response.status}`);

        if (!response.ok) {
          // Special handling for empty parties list (don't treat as error)
          if (response.status === 404) {
            console.log('[Watch Party] No parties found (404 response)');
            setAvailableParties([]);
            return [];
          }

          throw new Error(`Failed to fetch parties: ${response.status}`);
        }
      } catch (fetchError) {
        // Handle network errors (like CORS, network offline, etc.)
        console.error('[Watch Party] Network error fetching parties:', fetchError);
        setAvailableParties([]);
        setError('Could not connect to server. Please try again later.');
        setIsLoading(false);
        return [];
      }

      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('[Watch Party] Error parsing JSON response:', jsonError);
        setAvailableParties([]);
        setError('Invalid response from server');
        setIsLoading(false);
        return [];
      }

      // Enhanced debugging info
      if (data.parties && data.parties.length > 0) {
        console.log(`[Watch Party] Received ${data.parties.length} parties from API:`,
          data.parties.map((p: WatchPartyState) => ({
            id: p.id,
            hostId: p.hostId,
            members: p.members.length
          }))
        );

        // Add this log to see all party IDs clearly after fetch
        console.log(`[Watch Party] Fetch result - all party IDs: ${data.parties.map((p: WatchPartyState) => p.id).join(', ')}`);

        // Check if any parties have this user as host
        const myParties = data.parties.filter((p: WatchPartyState) => p.hostId === userId);
        if (myParties.length > 0) {
          console.log(`[Watch Party] Found ${myParties.length} parties where user ${userId} is host:`,
            myParties.map((p: WatchPartyState) => p.id).join(', '));
        } else {
          console.log(`[Watch Party] User ${userId} is not hosting any parties`);
        }

        // Check if any parties have this user as member
        const partiesAsMember = data.parties.filter((p: WatchPartyState) =>
          p.members.some(m => m.id === userId)
        );
        if (partiesAsMember.length > 0) {
          console.log(`[Watch Party] Found ${partiesAsMember.length} parties where user ${userId} is a member:`,
            partiesAsMember.map((p: WatchPartyState) => p.id).join(', '));
        }
      } else {
        console.log('[Watch Party] No parties received from API or empty array returned');
      }

      // Always update state with the latest parties - add this log
      console.log(`[Watch Party] Updating availableParties state with ${data.parties?.length || 0} parties`);
      setAvailableParties(data.parties || []);
      return data.parties || [];
    } catch (err) {
      console.error('[Watch Party] Error fetching available parties:', err);

      // Don't set error state for empty parties - just return empty array
      if (err instanceof Error && err.message.includes('404')) {
        setAvailableParties([]);
        return [];
      }

      setError('Failed to load available watch parties.');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Subscribe to the main watch parties channel
  useEffect(() => {
    // Subscribe to main channel for all parties
    const mainChannel = pusherClient.subscribe('watch-parties');
    mainChannelRef.current = mainChannel;

    // Handle new parties with memoization to prevent re-renders
    const handlePartyCreated = (party: WatchPartyState) => {
      console.log('[Watch Party] Pusher received party created event:', party.id);

      // Immediately update the UI with the new party
      setAvailableParties(prev => {
        // Check if this is actually a new party or different from what we have
        const existingPartyIndex = prev.findIndex(p => p.id === party.id);

        if (existingPartyIndex >= 0) {
          // Party exists, check if it's actually different
          if (JSON.stringify(prev[existingPartyIndex]) === JSON.stringify(party)) {
            return prev; // No change needed
          }

          // Update the existing party
          const newParties = [...prev];
          newParties[existingPartyIndex] = party;
          console.log('[Watch Party] Updated existing party via Pusher:', party.id);
          return newParties;
        } else {
          // It's a new party, add it
          console.log('[Watch Party] Added new party via Pusher:', party.id);
          return [...prev, party];
        }
      });

      // We don't need to force a fetch immediately after updating the UI
      // This was causing the debounce to prevent updates
      // Instead, schedule a background refresh after a longer delay
      setTimeout(() => {
        fetchAvailableParties(true).catch(error => {
          console.error('[Watch Party] Error fetching parties after Pusher event:', error);
        });
      }, 2000); // Longer delay to avoid UI flicker
    };

    // Handle party update events for the main channel
    const handlePartyUpdate = (party: WatchPartyState) => {
      console.log('[Watch Party] Pusher received party update event on main channel:', party.id);

      // Immediately update the UI with the updated party
      setAvailableParties(prev => {
        // Find the party in the current list
        const existingPartyIndex = prev.findIndex(p => p.id === party.id);

        if (existingPartyIndex >= 0) {
          // Party exists, check if it's actually different to avoid unnecessary re-renders
          if (JSON.stringify(prev[existingPartyIndex]) === JSON.stringify(party)) {
            console.log('[Watch Party] Party update identical to current state, skipping update');
            return prev; // No change needed
          }

          // Party exists and has changes, update it
          console.log('[Watch Party] Updating existing party with new data');
          const newParties = [...prev];
          newParties[existingPartyIndex] = party;
          return newParties;
        } else {
          // It's a new party, add it
          console.log('[Watch Party] Adding new party from update event:', party.id);
          return [...prev, party];
        }
      });
    };

    // Handle party deleted events
    const handlePartyDeleted = (data: { partyId: string }) => {
      console.log('[Watch Party] Pusher received party deleted event:', data.partyId);

      // Log the current state before updating
      console.log('[Watch Party] Current parties before deletion:',
        availableParties.map(p => p.id).join(', '));

      // Remove the party from available parties with explicit logging
      setAvailableParties(prev => {
        const prevCount = prev.length;
        const filtered = prev.filter(p => p.id !== data.partyId);
        const newCount = filtered.length;

        if (prevCount === newCount) {
          console.log(`[Watch Party] Warning: Party ${data.partyId} was not found in state to remove`);
        } else {
          console.log(`[Watch Party] Successfully removed party ${data.partyId} from state (${prevCount} → ${newCount})`);
        }

        return filtered;
      });

      // If we were in this party, clear current party state
      if (currentParty && currentParty.id === data.partyId) {
        console.log(`[Watch Party] Clearing current party state for deleted party ${data.partyId}`);
        setCurrentParty(null);
      }

      // Force a fetch to make sure we have the latest data
      console.log(`[Watch Party] Scheduling forced fetch after delete event for party ${data.partyId}`);
      setTimeout(() => {
        fetchAvailableParties(true).then(parties => {
          console.log(`[Watch Party] Forced fetch completed after delete event. Received ${parties.length} parties:`,
            parties.map((p: WatchPartyState) => p.id).join(', '));
        }).catch(error => {
          console.error('[Watch Party] Error fetching parties after delete event:', error);
        });
      }, 500); // Increased delay to ensure server has time to process
    };

    mainChannel.bind(WATCH_PARTY_EVENTS.PARTY_CREATED, handlePartyCreated);
    mainChannel.bind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);
    mainChannel.bind(WATCH_PARTY_EVENTS.PARTY_DELETED, handlePartyDeleted);

    // Fetch available parties once on mount
    fetchAvailableParties();

    return () => {
      // Cleanup: unbind event handler first, then unsubscribe
      if (mainChannelRef.current) {
        mainChannelRef.current.unbind(WATCH_PARTY_EVENTS.PARTY_CREATED, handlePartyCreated);
        mainChannelRef.current.unbind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);
        mainChannelRef.current.unbind(WATCH_PARTY_EVENTS.PARTY_DELETED, handlePartyDeleted);
        mainChannelRef.current.unbind_all();
        pusherClient.unsubscribe('watch-parties');
      }
    };
  }, [fetchAvailableParties, currentParty]);

  // Subscribe to specific party channel when joining a party
  useEffect(() => {
    if (!currentParty) return;

    // Subscribe to specific party channel
    const partyChannel = pusherClient.subscribe(`watch-party-${currentParty.id}`);
    partyChannelRef.current = partyChannel;

    console.log(`Subscribed to watch-party-${currentParty.id} channel`);

    // Create a ref for currentParty at the top level of the hook
    const currentPartyRef = { current: currentParty };

    // Handle party updates with strict equality checking to prevent unnecessary re-renders
    const handlePartyUpdate = (party: WatchPartyState) => {
      // Deep comparison to avoid unnecessary updates
      if (JSON.stringify(currentPartyRef.current) === JSON.stringify(party)) {
        console.log("Party update identical to current state, skipping update");
        return;
      }

      console.log("Received party update with changes");
      setCurrentParty(party);
    };

    // Handle member updates with improved real-time handling
    const handleMemberUpdate = (members: WatchPartyMember[]) => {
      // Log the update for debugging
      console.log("Received member update:", members);

      // Always update the members to ensure real-time synchronization
      // This ensures we don't miss any member updates due to equality checking
      setCurrentParty(prev => {
        if (!prev) return null;
        return { ...prev, members };
      });

      // Update the reference for future comparisons
      if (currentPartyRef.current) {
        currentPartyRef.current = {
          ...currentPartyRef.current,
          members
        };
      }
    };

    // Handle playback updates with improved real-time handling
    const handlePlaybackUpdate = (data: { currentTime: number, isPlaying: boolean, updateCount?: number }) => {
      // Log the update for debugging
      console.log("Received playback update:", data);

      // Always update the playback state to ensure real-time synchronization
      setCurrentParty(prev => {
        if (!prev) return null;
        return {
          ...prev,
          currentTime: data.currentTime,
          isPlaying: data.isPlaying
        };
      });

      // Update the reference for future comparisons
      if (currentPartyRef.current) {
        currentPartyRef.current = {
          ...currentPartyRef.current,
          currentTime: data.currentTime,
          isPlaying: data.isPlaying
        };
      }
    };

    // Handle new messages with message ID checking
    const handleNewMessage = (message: ChatMessage) => {
      // Check if message already exists to prevent duplicates
      const messageExists = currentPartyRef.current?.messages.some(m => m.id === message.id);
      if (messageExists) {
        console.log("Message already exists, skipping update");
        return;
      }

      console.log("Received new message:", message.content);
      setCurrentParty(prev => {
        if (!prev) return null;
        return {
          ...prev,
          messages: [...prev.messages, message]
        };
      });
    };

    // Bind event handlers
    partyChannel.bind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);
    partyChannel.bind(WATCH_PARTY_EVENTS.MEMBER_UPDATE, handleMemberUpdate);
    partyChannel.bind(WATCH_PARTY_EVENTS.PLAYBACK_UPDATE, handlePlaybackUpdate);
    partyChannel.bind(WATCH_PARTY_EVENTS.NEW_MESSAGE, handleNewMessage);

    return () => {
      // Unbind all events first, then unsubscribe
      if (partyChannelRef.current) {
        partyChannelRef.current.unbind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);
        partyChannelRef.current.unbind(WATCH_PARTY_EVENTS.MEMBER_UPDATE, handleMemberUpdate);
        partyChannelRef.current.unbind(WATCH_PARTY_EVENTS.PLAYBACK_UPDATE, handlePlaybackUpdate);
        partyChannelRef.current.unbind(WATCH_PARTY_EVENTS.NEW_MESSAGE, handleNewMessage);

        partyChannelRef.current.unbind_all();
        pusherClient.unsubscribe(`watch-party-${currentParty.id}`);
        console.log(`Unsubscribed from watch-party-${currentParty.id} channel`);
      }
    };
  }, [currentParty?.id]); // Only depend on the party ID, not the whole party object

  // Create a new watch party
  const createParty = useCallback(async (content: IContent, season?: number, episode?: number, isPrivate: boolean = false): Promise<string> => {
    try {
      setIsLoading(true);
      setError(null);

      // Log important details about the party being created
      console.log('[Watch Party] Creating party with content:', content.title,
          `(ID: ${content.id}, Type: ${content.type}${content.type === 'show' ? `, Season: ${season}, Episode: ${episode}` : ''})`);

      // Use AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      // Build request data to match API expectations
      const requestData: any = {
        contentId: content.id,
        title: content.title || 'Untitled Party',
        contentType: content.type || 'video',
        hostId: userId,
        hostName: userName,
        // Properly format additional fields for API
        posterPath: content.posterPath || '',
        backdropPath: content.backdropPath || '',
        overview: content.overview || '',
        private: isPrivate,
        // Explicitly include necessary content fields directly in the main object
        content: {
          id: content.id,
          title: content.title || 'Untitled Content',
          posterPath: content.posterPath || '',
          backdropPath: content.backdropPath || '',
          type: content.type || 'video',
          overview: content.overview || '',
          year: content.year || new Date().getFullYear().toString(),
          rating: content.rating || 0,
          genres: content.genres || []
        }
      };

      // Add season and episode for TV shows if provided
      if (content.type === 'show') {
        // Ensure we have valid values - null/undefined will be replaced with defaults on the server
        requestData.season = season !== undefined ? season : 1;
        requestData.episode = episode !== undefined ? episode : 1;
        console.log(`[Watch Party] Setting TV show parameters: Season ${requestData.season}, Episode ${requestData.episode}`);
      }

      try {
        console.log('[Watch Party] Sending create party request with data:', JSON.stringify(requestData, null, 2));

        const response = await fetch('/api/watch-party', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event: 'create-watch-party', // Match server-side constant
            data: requestData
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
          console.error('[Watch Party] Create party API error:', errorData);
          throw new Error(errorData.error || `Failed to create watch party (${response.status})`);
        }

        const data = await response.json().catch(() => {
          throw new Error('Failed to parse server response');
        });

        console.log('[Watch Party] Create party API response:', data);

        // Immediately add the new party to the available parties list for instant feedback
        if (data.party) {
          console.log('[Watch Party] Adding newly created party to local state:', data.party);
          setAvailableParties(prev => {
            // Check if this party already exists (shouldn't happen for new parties)
            const existingIndex = prev.findIndex(p => p.id === data.party.id);
            if (existingIndex >= 0) {
              // Replace the existing party
              const newParties = [...prev];
              newParties[existingIndex] = data.party;
              return newParties;
            } else {
              // Add the new party
              return [...prev, data.party];
            }
          });
        }

        // Also refresh available parties list in the background
        // This ensures we have the latest data from the server
        setTimeout(() => {
          fetchAvailableParties(true).catch(err =>
            console.error('[Watch Party] Error fetching parties after creation:', err)
          );
        }, 1000); // Longer delay to avoid UI flicker

        if (data.party && data.party.id) {
          console.log('[Watch Party] Party created with ID:', data.party.id);
          return data.party.id;
        } else if (data.partyId) {
          console.log('[Watch Party] Party created with ID:', data.partyId);
          return data.partyId;
        } else {
          throw new Error('Invalid response: Missing party ID');
        }
      } catch (fetchError: any) {
        if (fetchError.name === 'AbortError') {
          throw new Error('Request timeout: Server took too long to respond');
        }
        throw fetchError;
      }
    } catch (err: any) {
      console.error('[Watch Party] Error creating party:', err);
      setError(err.message || 'Failed to create watch party');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [userId, userName, fetchAvailableParties]);

  // Join an existing watch party
  const joinParty = useCallback(async (partyId: string): Promise<void> => {
    // Check if already in this party - no need to join again
    if (currentParty?.id === partyId) {
      console.log(`[Watch Party] Already in party ${partyId}, skipping join`);
      return;
    }

    // Check if we should prevent rejoining this specific party
    // This helps avoid errors when a party has been deleted
    const urlParams = new URLSearchParams(window.location.search);
    const preventRejoinId = urlParams.get('preventRejoin');

    if (preventRejoinId && preventRejoinId === partyId) {
      console.log(`[Watch Party] Preventing rejoin of recently exited/deleted party: ${partyId}`);
      throw new Error(`Party not available: ${partyId}`);
    }

    // Store for error handling
    let lastError: Error | null = null;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount <= maxRetries) {
      try {
        setIsLoading(true);
        setError(null);

        // No longer setting userName from parameter as we're using the authenticated user's name

        if (!partyId?.trim()) {
          throw new Error('Invalid party ID');
        }

        console.log(`[Watch Party] Join attempt ${retryCount + 1}/${maxRetries + 1} for party: ${partyId}, member: ${userName}`);

        // Add timeout handling
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

        try {
          // When retrying, first check if the party exists using get-parties
          if (retryCount > 0) {
            try {
              console.log(`[Watch Party] Verifying party ${partyId} exists before retrying join...`);
              const verifyResponse = await fetch('/api/watch-party', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  event: 'get-watch-parties',
                }),
                signal: controller.signal,
                cache: 'no-store'
              });

              if (verifyResponse.ok) {
                const data = await verifyResponse.json();
                const partyExists = data.parties && data.parties.some((p: any) => p.id === partyId);

                if (!partyExists) {
                  console.error(`[Watch Party] Verification failed: Party ${partyId} not found, won't retry join`);
                  throw new Error(`Party not found: ${partyId}`);
                } else {
                  console.log(`[Watch Party] Verification successful: Party ${partyId} exists, will retry join`);
                }
              }
            } catch (verifyError) {
              if (verifyError instanceof Error && verifyError.message.includes('not found')) {
                throw verifyError; // Rethrow "not found" errors without further retries
              }
              console.warn(`[Watch Party] Verification warning:`, verifyError);
              // Continue with join attempt even if verification fails for other reasons
            }
          }

          const response = await fetch('/api/watch-party', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              event: 'join-watch-party', // Match server-side constant
              data: {
                partyId,
                memberId: userId,
                memberName: userName
              }
            }),
            signal: controller.signal,
            cache: 'no-store' // Ensure fresh responses
          });

          clearTimeout(timeoutId);

          // First check if the response was successful
          if (!response.ok) {
            let errorData;
            let errorStatus = response.status;

            try {
              errorData = await response.json();
            } catch (parseError) {
              throw new Error(`Failed to join watch party: Server responded with ${response.status}`);
            }

            // Special handling for party not found errors
            if (errorStatus === 404) {
              console.error(`[Watch Party] Party not found: ${partyId}`);
              throw new Error(`Party not found: ${partyId}`);
            }

            throw new Error(errorData.error || `Failed to join watch party: ${response.status}`);
          }

          // Parse the JSON response with error handling
          let data;
          try {
            data = await response.json();
          } catch (parseError) {
            throw new Error('Failed to parse server response when joining party');
          }

          // Check if the response contains the expected data
          if (!data.party) {
            throw new Error('Invalid server response: Missing party data');
          }

          // Validate that the party has the correct ID
          if (data.party.id !== partyId) {
            console.error(`[Watch Party] Party ID mismatch: requested ${partyId}, got ${data.party.id}`);
            throw new Error('Server returned incorrect party');
          }

          setCurrentParty(data.party);
          console.log(`[Watch Party] Successfully joined party: ${partyId}`);
          return; // Success! Exit retry loop
        } catch (fetchError: any) {
          if (fetchError.name === 'AbortError') {
            throw new Error('Request timeout - server took too long to respond');
          }
          throw fetchError;
        }
      } catch (err: any) {
        lastError = err instanceof Error ? err : new Error(String(err));
        const errorMessage = lastError.message || 'Failed to join watch party';

        // Don't retry for "party not found" errors
        if (errorMessage.toLowerCase().includes('not found')) {
          console.error(`[Watch Party] Error joining party: ${errorMessage}`, err);
          console.warn(`[Watch Party] Handled error: ${errorMessage}`);
          setError(errorMessage);
          throw lastError;
        }

        // Exponential backoff for other errors
        retryCount++;

        if (retryCount <= maxRetries) {
          // Wait longer between each retry
          const delay = 1000 * Math.pow(1.5, retryCount);
          console.log(`[Watch Party] Join attempt ${retryCount} failed. Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          console.error(`[Watch Party] Failed to join after ${maxRetries + 1} attempts:`, err);
          setError(errorMessage);
          throw lastError;
        }
      } finally {
        if (retryCount > maxRetries) {
          setIsLoading(false);
        }
      }
    }

    // We should never reach here, but TS needs this
    if (lastError) throw lastError;
  }, [userId, userName, currentParty]);

  // Leave the current watch party with improved reliability
  const leaveParty = useCallback(async (): Promise<void> => {
    if (!currentParty) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log(`[Watch Party] Leaving party ${currentParty.id} as user ${userId}`);

      // Make the API call with retry logic
      let success = false;
      let attempts = 0;
      const maxAttempts = 3;

      while (!success && attempts < maxAttempts) {
        attempts++;
        try {
          const response = await fetch('/api/watch-party', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              event: WATCH_PARTY_EVENTS.LEAVE_PARTY,
              data: {
                partyId: currentParty.id,
                memberId: userId
              }
            }),
            // Ensure the request is not cached
            cache: 'no-store'
          });

          if (response.ok) {
            success = true;
            console.log(`[Watch Party] Successfully left party ${currentParty.id}`);
          } else {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error(`[Watch Party] Error leaving party (attempt ${attempts}/${maxAttempts}):`, errorData);

            // Wait before retrying
            if (attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500 * attempts));
            }
          }
        } catch (fetchError) {
          console.error(`[Watch Party] Fetch error leaving party (attempt ${attempts}/${maxAttempts}):`, fetchError);

          // Wait before retrying
          if (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 500 * attempts));
          }
        }
      }

      // Always clear the current party state, even if the API call failed
      // This ensures the UI updates correctly
      setCurrentParty(null);

      // If we're using Pusher, also unsubscribe from the channel
      if (partyChannelRef.current) {
        try {
          partyChannelRef.current.unbind_all();
          pusherClient.unsubscribe(`watch-party-${currentParty.id}`);
          console.log(`[Watch Party] Unsubscribed from watch-party-${currentParty.id} channel`);
        } catch (unbindError) {
          console.error('[Watch Party] Error unsubscribing from Pusher channel:', unbindError);
        }
      }
    } catch (err: any) {
      console.error('[Watch Party] Error in leaveParty function:', err);
      setError(err.message || 'Failed to leave watch party');
      // Still clear the current party to ensure UI updates
      setCurrentParty(null);
    } finally {
      setIsLoading(false);
    }
  }, [currentParty, userId]);

  // Update playback state
  const updatePlayback = useCallback(async (currentTime: number, isPlaying: boolean): Promise<void> => {
    if (!currentParty) return;

    try {
      await fetch('/api/watch-party', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: WATCH_PARTY_EVENTS.PLAYBACK_UPDATE,
          data: {
            partyId: currentParty.id,
            currentTime,
            isPlaying
          }
        }),
      });
    } catch (err: any) {
      console.error('Error updating playback:', err);
    }
  }, [currentParty]);

  // Send a chat message
  const sendMessage = useCallback(async (content: string): Promise<void> => {
    if (!currentParty) return;

    try {
      // Find the current user in the party members to get their avatar
      const currentMember = currentParty.members.find(m => m.id === userId);
      const avatar = currentMember?.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${userName}`;

      const message: ChatMessage = {
        id: uuidv4(),
        memberId: userId,
        memberName: userName,
        avatar: avatar, // Include the user's avatar
        content,
        timestamp: new Date().toISOString(),
        type: 'chat'
      };

      console.log('[Watch Party] Sending message with avatar:', avatar);

      await fetch('/api/watch-party', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: WATCH_PARTY_EVENTS.NEW_MESSAGE,
          data: {
            partyId: currentParty.id,
            message
          }
        }),
      });
    } catch (err: any) {
      console.error('Error sending message:', err);
      throw err; // Re-throw the error so it can be caught by the caller
    }
  }, [currentParty, userId, userName]);

  // Send a reaction
  const sendReaction = useCallback(async (reaction: string): Promise<void> => {
    if (!currentParty) return;

    try {
      // Find the current user in the party members to get their avatar
      const currentMember = currentParty.members.find(m => m.id === userId);
      const avatar = currentMember?.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${userName}`;

      const message: ChatMessage = {
        id: uuidv4(),
        memberId: userId,
        memberName: userName,
        avatar: avatar, // Include the user's avatar
        content: reaction,
        timestamp: new Date().toISOString(),
        type: 'reaction'
      };

      console.log('[Watch Party] Sending reaction with avatar:', avatar);

      await fetch('/api/watch-party', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: WATCH_PARTY_EVENTS.NEW_MESSAGE,
          data: {
            partyId: currentParty.id,
            message
          }
        }),
      });
    } catch (err: any) {
      console.error('Error sending reaction:', err);
      throw err; // Re-throw the error so it can be caught by the caller
    }
  }, [currentParty, userId, userName]);

  // Check if the current user is the host
  const isHost = currentParty ? currentParty.hostId === userId : false;

  const deleteParty = useCallback(async (partyId: string, onSuccess?: () => void) => {
    // Validate partyId
    if (!partyId || typeof partyId !== 'string') {
      console.error('[Watch Party] Invalid party ID for deletion:', partyId);
      setError('Invalid party ID provided for deletion.');
      return false;
    }

    try {
      console.log(`[Watch Party] Deleting party: ${partyId}`);
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/watch-party', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'delete-watch-party', // Correct event name
          data: { partyId }
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to delete watch party' }));
        throw new Error(errorData.error || 'Failed to delete watch party');
      }

      console.log(`[Watch Party] Successfully deleted party via API: ${partyId}`);

      // 1. Set current party state to null immediately
      setCurrentParty(null);

      // 2. Call onSuccess callback for navigation *before* any background refresh
      if (onSuccess) {
        onSuccess();
      }

      // 3. Optionally trigger a background refresh of the available parties list (no longer awaiting completeRefresh)
      // We can do this non-blockingly
      fetchAvailableParties(true).catch(err => {
          console.warn("[Watch Party] Background refresh after deletion failed:", err);
      });

      return true;
    } catch (error) {
      console.error('[Watch Party] Error deleting party:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete watch party.');
      // Ensure loading is false on error
      setIsLoading(false);
      return false;
    } finally {
      // Ensure loading is always set to false eventually, though it might be set earlier on error
      setIsLoading(false);
    }
  }, [userId, setIsLoading, setError, setCurrentParty, fetchAvailableParties]);

  return {
    userId,
    userName,
    setUserName,
    currentParty,
    availableParties,
    isLoading,
    error,
    isHost,
    createParty,
    joinParty,
    leaveParty,
    updatePlayback,
    sendMessage,
    sendReaction,
    fetchAvailableParties,
    deleteParty
  };
}