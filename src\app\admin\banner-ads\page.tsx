'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToastHelpers } from '@/lib/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import BannerAdForm, { BannerAdFormData } from '@/components/admin/BannerAdForm';
import BannerAnalytics from '@/components/admin/BannerAnalytics';
import { IBannerAd } from '@/models/BannerAd';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  BarChart3,
  Calendar,
  Loader2,
  AlertTriangle,
  RefreshCw,
  Power,
  ExternalLink
} from 'lucide-react';
import Image from 'next/image';

interface BannerAdWithAnalytics extends IBannerAd {
  createdBy: {
    name: string;
    email: string;
  };
  updatedBy?: {
    name: string;
    email: string;
  };
}

// Active Banners Table Component
function ActiveBannersTable({
  bannerAds,
  onToggleStatus,
  onEdit,
  onDelete,
  loading
}: {
  bannerAds: BannerAdWithAnalytics[];
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  onEdit: (banner: BannerAdWithAnalytics) => void;
  onDelete: (id: string) => void;
  loading: boolean;
}) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
      </div>
    );
  }

  if (bannerAds.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Eye className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-lg font-medium text-vista-light mb-2">No Active Banners</h3>
        <p className="text-vista-light/60 mb-4">
          You don't have any banners currently running on your website.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-vista-light/70 mb-4">
        Showing {bannerAds.length} active banner{bannerAds.length !== 1 ? 's' : ''} currently live on your website
      </div>

      <div className="grid gap-4">
        {bannerAds.map((banner) => (
          <Card key={banner._id} className="border-l-4 border-l-green-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {/* Banner Preview */}
                  <div className="relative w-20 h-12 rounded overflow-hidden flex-shrink-0">
                    <Image
                      src={banner.imageUrl}
                      alt={banner.title}
                      fill
                      className="object-cover"
                      sizes="80px"
                    />
                  </div>

                  {/* Banner Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-vista-light">{banner.title}</h3>
                      <div className="flex gap-1">
                        {banner.styling.positions?.map((position) => (
                          <Badge key={position} variant="outline" className="text-xs">
                            {position.replace('-', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {banner.description && (
                      <p className="text-sm text-vista-light/60 mb-2">{banner.description}</p>
                    )}
                    <div className="flex items-center gap-4 text-xs text-vista-light/50">
                      <span className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {banner.analytics.views} views
                      </span>
                      <span className="flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        {banner.analytics.clicks} clicks
                      </span>
                      {banner.linkUrl && (
                        <span className="flex items-center gap-1">
                          <ExternalLink className="h-3 w-3" />
                          <a
                            href={banner.linkUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:text-vista-blue"
                          >
                            {banner.linkUrl.replace(/^https?:\/\//, '')}
                          </a>
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleStatus(banner._id, banner.isActive)}
                    className="text-orange-400 hover:text-orange-300 hover:bg-orange-400/10"
                    title="Disable Banner"
                  >
                    <Power className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(banner)}
                    title="Edit Banner"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete(banner._id)}
                    className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                    title="Delete Banner"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default function BannerAdsPage() {
  const [bannerAds, setBannerAds] = useState<BannerAdWithAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedBannerAd, setSelectedBannerAd] = useState<BannerAdWithAnalytics | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  const { toast } = useToastHelpers();
  const { user } = useAuth();

  const fetchBannerAds = async (page = 1, search = '', status = 'all') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(search && { search }),
        ...(status !== 'all' && { status })
      });

      const response = await fetch(`/api/admin/banner-ads?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch banner ads');
      }

      const data = await response.json();
      setBannerAds(data.bannerAds);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching banner ads:', error);
      toast.error('Failed to load banner ads');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBannerAds(1, searchTerm, statusFilter);
  }, [searchTerm, statusFilter]);

  const handleCreateBannerAd = async (data: BannerAdFormData) => {
    try {
      setIsSubmitting(true);
      const response = await fetch('/api/admin/banner-ads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create banner ad');
      }

      // The API returns the created banner object directly
      const createdBanner = await response.json();
      console.log('Banner created successfully:', createdBanner);

      toast.success('Banner ad created successfully');
      setIsCreateModalOpen(false);
      fetchBannerAds(pagination.page, searchTerm, statusFilter);
    } catch (error) {
      console.error('Error creating banner ad:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create banner ad');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditBannerAd = async (data: BannerAdFormData) => {
    if (!selectedBannerAd) return;

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/admin/banner-ads/${selectedBannerAd._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update banner ad');
      }

      // The API returns the updated banner object directly
      const updatedBanner = await response.json();
      console.log('Banner updated successfully:', updatedBanner);

      toast.success('Banner ad updated successfully');
      setIsEditModalOpen(false);
      setSelectedBannerAd(null);
      fetchBannerAds(pagination.page, searchTerm, statusFilter);
    } catch (error) {
      console.error('Error updating banner ad:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update banner ad');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteBannerAd = async (id: string) => {
    if (!confirm('Are you sure you want to delete this banner ad?')) return;

    try {
      const response = await fetch(`/api/admin/banner-ads/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete banner ad');
      }

      toast.success('Banner ad deleted successfully');
      fetchBannerAds(pagination.page, searchTerm, statusFilter);
    } catch (error) {
      console.error('Error deleting banner ad:', error);
      toast.error('Failed to delete banner ad');
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/banner-ads/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update banner ad status');
      }

      toast.success(`Banner ad ${!currentStatus ? 'activated' : 'deactivated'}`);
      fetchBannerAds(pagination.page, searchTerm, statusFilter);
    } catch (error) {
      console.error('Error updating banner ad status:', error);
      toast.error('Failed to update banner ad status');
    }
  };

  const getStatusBadge = (bannerAd: BannerAdWithAnalytics) => {
    const now = new Date();
    const startDate = bannerAd.startDate ? new Date(bannerAd.startDate) : null;
    const endDate = bannerAd.endDate ? new Date(bannerAd.endDate) : null;

    if (!bannerAd.isActive) {
      return <Badge variant="secondary">Inactive</Badge>;
    }

    if (startDate && now < startDate) {
      return <Badge variant="outline">Scheduled</Badge>;
    }

    if (endDate && now > endDate) {
      return <Badge variant="destructive">Expired</Badge>;
    }

    return <Badge variant="default" className="bg-green-600">Active</Badge>;
  };

  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Banner Ads</h1>
          <p className="text-vista-light/70">Manage promotional banners for your website</p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-vista-blue hover:bg-vista-blue/90"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Banner Ad
        </Button>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="active">Active Banners</TabsTrigger>
          <TabsTrigger value="all">All Banners</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-6">
          {/* Quick Stats for Active Banners */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="text-sm font-medium text-vista-light/70">Live Banners</div>
                </div>
                <div className="text-2xl font-bold text-vista-light mt-1">
                  {bannerAds.filter(banner => {
                    const now = new Date();
                    const endDate = banner.endDate ? new Date(banner.endDate) : null;
                    return banner.isActive && (!endDate || now <= endDate);
                  }).length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Eye className="w-4 h-4 text-vista-light/70" />
                  <div className="text-sm font-medium text-vista-light/70">Total Views</div>
                </div>
                <div className="text-2xl font-bold text-vista-light mt-1">
                  {bannerAds.filter(banner => {
                    const now = new Date();
                    const endDate = banner.endDate ? new Date(banner.endDate) : null;
                    return banner.isActive && (!endDate || now <= endDate);
                  }).reduce((sum, banner) => sum + banner.analytics.views, 0).toLocaleString()}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4 text-vista-light/70" />
                  <div className="text-sm font-medium text-vista-light/70">Total Clicks</div>
                </div>
                <div className="text-2xl font-bold text-vista-light mt-1">
                  {bannerAds.filter(banner => {
                    const now = new Date();
                    const endDate = banner.endDate ? new Date(banner.endDate) : null;
                    return banner.isActive && (!endDate || now <= endDate);
                  }).reduce((sum, banner) => sum + banner.analytics.clicks, 0).toLocaleString()}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-vista-light/70" />
                  <div className="text-sm font-medium text-vista-light/70">Expiring Soon</div>
                </div>
                <div className="text-2xl font-bold text-vista-light mt-1">
                  {bannerAds.filter(banner => {
                    if (!banner.endDate || !banner.isActive) return false;
                    const now = new Date();
                    const endDate = new Date(banner.endDate);
                    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
                  }).length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Active Banners Tab */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                Currently Running Banners
              </CardTitle>
              <CardDescription>
                Manage banners that are currently live on your website. You can quickly disable, edit, or delete them.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ActiveBannersTable
                bannerAds={bannerAds.filter(banner => {
                  const now = new Date();
                  const endDate = banner.endDate ? new Date(banner.endDate) : null;
                  return banner.isActive && (!endDate || now <= endDate);
                })}
                onToggleStatus={handleToggleStatus}
                onEdit={(banner) => {
                  setSelectedBannerAd(banner);
                  setIsEditModalOpen(true);
                }}
                onDelete={handleDeleteBannerAd}
                loading={loading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-6">

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-vista-light/40" />
              <Input
                placeholder="Search banner ads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => fetchBannerAds(pagination.page, searchTerm, statusFilter)}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Banner Ads Table */}
      <Card>
        <CardHeader>
          <CardTitle>Banner Ads ({pagination.total})</CardTitle>
          <CardDescription>
            Manage your promotional banners and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
            </div>
          ) : bannerAds.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-vista-light/40 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-vista-light mb-2">No banner ads found</h3>
              <p className="text-vista-light/60 mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Create your first banner ad to get started'}
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-vista-blue hover:bg-vista-blue/90"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Banner Ad
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banner</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Analytics</TableHead>
                    <TableHead>Status & End Date</TableHead>
                    <TableHead>Positions</TableHead>
                    <TableHead>Created By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bannerAds.map((bannerAd) => (
                    <TableRow key={bannerAd._id}>
                      <TableCell>
                        <div className="relative w-16 h-9 rounded overflow-hidden">
                          <Image
                            src={bannerAd.imageUrl}
                            alt={bannerAd.title}
                            fill
                            className="object-cover"
                            sizes="64px"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-vista-light">{bannerAd.title}</div>
                          {bannerAd.description && (
                            <div className="text-sm text-vista-light/60 truncate max-w-xs">
                              {bannerAd.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(bannerAd)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{bannerAd.priority}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          <div className="flex items-center gap-2">
                            <Eye className="h-3 w-3" />
                            <span>{bannerAd.analytics.views}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <BarChart3 className="h-3 w-3" />
                            <span>{bannerAd.analytics.clicks}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          <div className="flex items-center gap-1">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>Live</span>
                          </div>
                          <div>End: {formatDate(bannerAd.endDate)}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {bannerAd.styling.positions?.map((position: string) => (
                            <Badge key={position} variant="outline" className="text-xs">
                              {position.replace('-', ' ')}
                            </Badge>
                          )) || <Badge variant="outline" className="text-xs">top</Badge>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">
                            {bannerAd.createdBy?.name || 'Unknown User'}
                          </div>
                          <div className="text-vista-light/60">
                            {bannerAd.createdBy?.email || 'No email'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(bannerAd._id, bannerAd.isActive)}
                            title={bannerAd.isActive ? 'Deactivate' : 'Activate'}
                          >
                            {bannerAd.isActive ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedBannerAd(bannerAd);
                              setIsEditModalOpen(true);
                            }}
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteBannerAd(bannerAd._id)}
                            title="Delete"
                            className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-vista-light/60">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchBannerAds(pagination.page - 1, searchTerm, statusFilter)}
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-vista-light/60">
                  Page {pagination.page} of {pagination.pages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchBannerAds(pagination.page + 1, searchTerm, statusFilter)}
                  disabled={pagination.page >= pagination.pages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <BannerAnalytics />
        </TabsContent>
      </Tabs>

      {/* Create Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Banner Ad</DialogTitle>
          </DialogHeader>
          <BannerAdForm
            mode="create"
            onSubmit={handleCreateBannerAd}
            onCancel={() => setIsCreateModalOpen(false)}
            isLoading={isSubmitting}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Banner Ad</DialogTitle>
          </DialogHeader>
          {selectedBannerAd && (
            <BannerAdForm
              mode="edit"
              bannerAd={selectedBannerAd}
              onSubmit={handleEditBannerAd}
              onCancel={() => {
                setIsEditModalOpen(false);
                setSelectedBannerAd(null);
              }}
              isLoading={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
