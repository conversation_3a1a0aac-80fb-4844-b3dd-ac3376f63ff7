/**
 * Scroll utilities for smooth page navigation and element focusing
 */

/**
 * <PERSON>moothly scrolls to the top of the page
 * @param behavior - The scroll behavior ('smooth' or 'auto')
 */
export function scrollToTop(behavior: ScrollBehavior = 'smooth'): void {
  if (typeof window !== 'undefined') {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior
    })
  }
}

/**
 * Smoothly scrolls to a specific element by ID
 * @param elementId - The ID of the element to scroll to
 * @param behavior - The scroll behavior ('smooth' or 'auto')
 * @param offset - Optional offset from the top of the element (useful for fixed headers)
 */
export function scrollToElement(
  elementId: string, 
  behavior: ScrollBehavior = 'smooth',
  offset: number = 0
): void {
  if (typeof window !== 'undefined') {
    const element = document.getElementById(elementId)
    if (element) {
      const elementPosition = element.offsetTop - offset
      window.scrollTo({
        top: elementPosition,
        left: 0,
        behavior
      })
    }
  }
}

/**
 * Scrolls to a specific element using scrollIntoView
 * @param elementId - The ID of the element to scroll to
 * @param options - ScrollIntoView options
 */
export function scrollIntoView(
  elementId: string,
  options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'start' }
): void {
  if (typeof window !== 'undefined') {
    const element = document.getElementById(elementId)
    if (element) {
      element.scrollIntoView(options)
    }
  }
}

/**
 * Debounced scroll to top function to prevent multiple rapid calls
 * @param delay - Delay in milliseconds
 * @param behavior - The scroll behavior
 */
export function debouncedScrollToTop(delay: number = 100, behavior: ScrollBehavior = 'smooth'): void {
  if (typeof window !== 'undefined') {
    setTimeout(() => {
      scrollToTop(behavior)
    }, delay)
  }
} 