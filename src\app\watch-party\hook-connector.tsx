'use client'

import React, { useContext, createContext } from 'react'

// This is a simple hook connector that provides a fallback implementation
// when the actual WatchPartyContext is not available. This helps prevent
// errors during development, especially with Turbopack.

// Define model types
interface WatchPartyMember {
  id: string
  name: string
  avatar?: string
  isHost: boolean
  joinedAt: string
  isReady: boolean
}

interface ChatMessage {
  id: string
  memberId: string
  memberName: string
  avatar?: string
  content: string
  timestamp: string
  type: 'chat' | 'system' | 'reaction'
}

interface WatchPartyState {
  id: string
  contentId: string
  content?: ContentDetails
  hostId: string
  title?: string
  contentType?: string
  hostName?: string
  members: WatchPartyMember[]
  messages: ChatMessage[]
  currentTime: number
  isPlaying: boolean
  createdAt: string
  startedAt?: string
  currentSeason?: number
  currentEpisode?: number
}

interface ContentDetails {
  id: string
  title: string
  posterPath?: string
  backdropPath?: string
  type: 'movie' | 'show'
  overview?: string
  year?: string
  rating?: number
  genres?: string[]
}

// Define WatchParty interface
interface WatchPartyContextType {
  currentParty: WatchPartyState | null
  createParty: (contentId: string, name?: string, options?: Record<string, unknown>) => void
  joinParty: (partyId: string, name?: string) => void
  leaveParty: () => void
  sendMessage: (content: string, type?: 'chat' | 'system' | 'reaction') => void
  sendReaction: (emoji: string) => void
  setReady: (isReady: boolean) => void
  updatePlayback: (currentTime: number, isPlaying: boolean) => void
  endParty: () => void
  isHost: boolean
  isReady: boolean
  isConnected: boolean
  isConnecting: boolean
  connectionState: string
  availableParties: WatchPartyState[]
  fetchAvailableParties: (forceFetch?: boolean) => Promise<WatchPartyState[]>
  isLoading: boolean
  userId: string
}

// Create a fallback context with empty data
const defaultContext: WatchPartyContextType = {
  currentParty: null,
  createParty: () => console.log('Mock createParty called'),
  joinParty: () => console.log('Mock joinParty called'),
  leaveParty: () => console.log('Mock leaveParty called'),
  sendMessage: () => console.log('Mock sendMessage called'),
  sendReaction: () => console.log('Mock sendReaction called'),
  setReady: () => console.log('Mock setReady called'),
  updatePlayback: () => console.log('Mock updatePlayback called'),
  endParty: () => console.log('Mock endParty called'),
  isHost: false,
  isReady: false,
  isConnected: false,
  isConnecting: false,
  connectionState: 'disconnected',
  availableParties: [],
  fetchAvailableParties: async () => {
    console.log('Mock fetchAvailableParties called')
    return []
  },
  isLoading: false,
  userId: 'mock-user-id'
}

const FallbackContext = createContext<WatchPartyContextType>(defaultContext)

// Simple hook that provides the watch party context or a fallback
export function useWatchParty() {
  try {
    // Try to use the actual context if it's available in the component tree
    const actualContext = useContext(FallbackContext)
    
    // If we have some data, return that
    if (actualContext && Object.keys(actualContext).length > 0) {
      return actualContext
    }
    
    // Otherwise, return our fallback data
    return defaultContext
  } catch (error) {
    console.warn('Using fallback WatchParty context:', error)
    return defaultContext
  }
}

// Simple provider that just passes children through
// We use Pusher for real-time communication, so socketUrl is no longer needed
export function WatchPartyProvider({ 
  children 
}: { 
  children: React.ReactNode
}) {
  return <>{children}</>
} 