import { ReactNode } from 'react'
import { <PERSON>rrorProvider } from '@/lib/ErrorContext'
import { ToastProvider } from '@/lib/ToastContext'
import { WatchPartyWrapper } from '@/app/watch-party/watch-party-wrapper'
import dynamic from 'next/dynamic'

export default function WatchLayout({
  children,
}: {
  children: ReactNode
}) {
  return (
    <ErrorProvider>
      <ToastProvider>
        <WatchPartyWrapper>
          {children}
        </WatchPartyWrapper>
      </ToastProvider>
    </ErrorProvider>
  );
}