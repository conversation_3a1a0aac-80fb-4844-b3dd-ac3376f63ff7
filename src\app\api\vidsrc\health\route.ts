import { NextRequest, NextResponse } from 'next/server';
import { checkAllDomains, VIDSRC_DOMAINS } from '@/lib/domain-health';

/**
 * API endpoint to check health of all VidSrc domains
 * 
 * @param request The incoming request
 * @returns JSON data with health status of all domains
 */
export async function GET(request: NextRequest) {
  try {
    // Extract the force refresh parameter
    const { searchParams } = new URL(request.url);
    const forceRefresh = searchParams.get('refresh') === 'true';
    
    // Get health status of all domains
    const healthResults = await checkAllDomains(forceRefresh);
    
    // Return the results
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      domains: healthResults,
      recommended: healthResults.length > 0 
        ? healthResults
            .filter(domain => domain.status === 'ok')
            .sort((a, b) => a.responseTime - b.responseTime)[0]?.domain || VIDSRC_DOMAINS[0]
        : VIDSRC_DOMAINS[0]
    });
  } catch (error) {
    console.error('Error checking domain health:', error);
    return NextResponse.json(
      { error: 'Failed to check domain health' },
      { status: 500 }
    );
  }
} 