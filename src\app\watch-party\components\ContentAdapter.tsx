'use client'

import { ContentCardType } from '@/lib/content-utils'
import { Content as SelectorContent } from './ContentSelector'
import { ensureContentImagePaths } from '@/utils/image-utils'

/**
 * Converts ContentCardType to SelectorContent
 * This is used to bridge between the ContentBrowser and ContentSelector components
 */
export function convertCardToSelectorContent(content: ContentCardType): SelectorContent {
  return {
    id: content.id,
    title: content.title,
    description: content.description || '',
    posterPath: content.imagePath,
    backdropPath: content.backdropPath || '',
    rating: content.userRating,
    releaseDate: content.year ? `${content.year}-01-01` : undefined,
    type: content.type === 'movies' ? 'movie' : 'show',
    tmdbId: content.tmdbId,
    genres: content.genres || []
  };
}

/**
 * Converts SelectorContent to ContentCardType
 * This is used to bridge between the ContentSelector and ContentBrowser components
 */
export function convertSelectorToCardContent(content: SelectorContent): ContentCardType {
  return {
    id: content.id,
    title: content.title,
    imagePath: content.posterPath || '',
    backdropPath: content.backdropPath,
    type: content.type === 'movie' ? 'movies' : 'shows',
    year: content.releaseDate ? new Date(content.releaseDate).getFullYear().toString() : '',
    userRating: content.rating,
    genres: content.genres,
    description: content.description,
    tmdbId: content.tmdbId
  };
}

/**
 * ContentAdapter component that can be used to adapt between different content formats
 * This is a higher-order component that wraps either ContentBrowser or ContentSelector
 */
interface ContentAdapterProps {
  onSelectContent: (content: SelectorContent | null) => void;
  initialSelectedContent?: SelectorContent | null;
  className?: string;
}

export default function ContentAdapter({ 
  onSelectContent, 
  initialSelectedContent,
  className = ''
}: ContentAdapterProps) {
  // This component can be expanded to provide more advanced adaptation between
  // different content formats if needed in the future
  
  // For now, we're just re-exporting the ContentSelector component
  // with the same interface
  return (
    <div className={className}>
      {/* Future implementation could go here */}
    </div>
  );
}
