'use client'

import { useState, useEffect } from 'react'
import { useWatchParty as useLibWatchParty } from '@/lib/WatchPartyContext'

// Define types for the content and party members
export interface ContentType {
  id: string | number
  title: string
  type: 'movie' | 'tv'
  posterPath?: string
  backdropPath?: string
  overview?: string
  year?: string
  genres?: string[]
}

export interface PartyMember {
  id: string
  name: string
  isHost: boolean
  joinedAt: string
  isReady: boolean
  avatar?: string
}

export interface ChatMessage {
  id: string
  memberId: string
  memberName: string
  content: string
  timestamp: string
  type: 'chat' | 'system' | 'reaction'
}

export interface WatchPartyType {
  id: string
  contentId: string
  content?: ContentType
  hostId: string
  members: PartyMember[]
  messages: ChatMessage[]
  currentTime: number
  isPlaying: boolean
  createdAt: string
}

/**
 * Custom hook to access watch party functionality
 * Uses the watch party context from the lib folder
 */
export function useWatchParty() {
  const context = useLibWatchParty()
  const [isReady, setReady] = useState(false)
  
  return {
    currentParty: context.currentParty,
    availableParties: context.availableParties || [],
    userId: context.userId,
    isHost: context.isHost,
    isReady,
    setReady,
    createParty: context.createParty,
    joinParty: context.joinParty,
    leaveParty: context.leaveParty,
    fetchAvailableParties: context.fetchAvailableParties,
    sendMessage: context.sendMessage,
    sendReaction: context.sendReaction
  }
} 