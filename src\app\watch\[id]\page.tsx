// This file is a server component
import { Suspense } from 'react'
import { popularMovies, popularShows, getContentById } from '@/data/content'
import WatchContentClient from './watch-content-client'
// Using kebab-case filename for consistency
import DirectWatchClient from './direct-watch-client'

// Configure for compatibility with static export and dynamic params
export const preferredRegion = 'auto'
export const dynamic = 'auto'

interface PageProps {
  params: {
    id: string
  }
}

export default async function WatchPage({ params }: PageProps) {
  // In Next.js 15, we need to await the params to prevent sync dynamic APIs error
  const { id } = await params;
  
  // Determine if this is a watch party (contentId starts with 'party-')
  const isWatchParty = id.startsWith('party-');
  
  // Render the appropriate component based on the context
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-vista-dark">
        <div className="text-vista-light text-lg">Loading content...</div>
      </div>
    }>
      {isWatchParty ? (
        <WatchContentClient contentId={id.replace('party-', '')} />
      ) : (
        <DirectWatchClient contentId={id} />
      )}
    </Suspense>
  );
}

// Generate static params for all content
export function generateStaticParams() {
  return [
    ...popularMovies.map(movie => ({ id: String(movie.id) })),
    ...popularShows.map(show => ({ id: String(show.id) }))
  ];
} 