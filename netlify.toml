[build]
  command = "npm run mongo:check && npm run build"
  publish = ".next"
  ignore_warnings = true

# Use the Next.js plugin for Netlify
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Configure serverless function settings for MongoDB connections
[functions]
  node_bundler = "esbuild"
  included_files = ["src/lib/**", "src/models/**"]

  # Set the Netlify environment flag for connection handling
  [functions.environment]
    NETLIFY = "true"
    AWS_LAMBDA_JS_RUNTIME = "nodejs18.x"
    # Function timeout settings
    FUNCTION_TIME_LIMIT = "30"
    MAX_DURATION = "30"
    # MongoDB connection optimization flags
    MONGODB_CONNECTION_LIMIT = "10"
    MONGODB_SERVERLESS_MODE = "true"

# Force long cache for static assets
[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Special headers for authentication routes to ensure proper cookie handling
[[headers]]
  for = "/api/auth/*"
  [headers.values]
    Cache-Control = "no-store, no-cache, must-revalidate"
    Pragma = "no-cache"
    # Ensure cookies can be set properly
    Access-Control-Allow-Credentials = "true"
    Access-Control-Allow-Headers = "Content-Type, Authorization"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"

# Increase function timeout for auth functions specifically
[functions."api/auth/*"]
  included_files = ["src/lib/**", "src/models/**"]
  [functions."api/auth/*".environment]
    FUNCTION_TIME_LIMIT = "60"
    MAX_DURATION = "60"
    NETLIFY = "true"

# Optimize database-heavy endpoints
[functions."api/admin/*"]
  included_files = ["src/lib/**", "src/models/**"]
  [functions."api/admin/*".environment]
    FUNCTION_TIME_LIMIT = "30"
    MAX_DURATION = "30"
    NETLIFY = "true"

# Health check endpoints
[functions."api/health/*"]
  included_files = ["src/lib/**", "src/models/**"]

[build.environment]
  NODE_VERSION = "18.20.8"
  NPM_VERSION = "10.8.2"
  NEXT_TELEMETRY_DISABLED = "1"
  MONGODB_DB = "streamvista"
  # Enable connection optimization during build
  MONGODB_OPTIMIZE_SERVERLESS = "true"
  # Disable server-side rendering for problematic components
  NEXT_RUNTIME = "nodejs"

# Domain redirect - with explicit handling for both HTTP and HTTPS
[[redirects]]
  from = "http://streamvistaa.netlify.app/*"
  to = "https://streamvista.xyz/:splat"
  status = 301
  force = true

[[redirects]]
  from = "https://streamvistaa.netlify.app/*"
  to = "https://streamvista.xyz/:splat"
  status = 301
  force = true

# Ensure streamvista.xyz domain works correctly
[[redirects]]
  from = "https://streamvista.xyz/*"
  to = "/index.html"
  status = 200
  force = false

# Fallback for Next.js routing
[[redirects]]
  from = "/*"
  to = "/.netlify/functions/nextjs"
  status = 200
  force = false
