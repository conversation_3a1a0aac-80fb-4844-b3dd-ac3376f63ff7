const TMDB_API_KEY = process.env.NEXT_PUBLIC_TMDB_API_KEY;
const TMDB_ACCESS_TOKEN = process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN;
const TMDB_BASE_URL = process.env.NEXT_PUBLIC_TMDB_BASE_URL || 'https://api.themoviedb.org/3';
const TMDB_AUTH_METHOD = process.env.NEXT_PUBLIC_TMDB_AUTH_METHOD || 'api_key';

/**
 * Fetches data from the TMDb API
 * @param endpoint The API endpoint to fetch from (e.g., "movie/popular")
 * @param params Additional query parameters
 * @returns The JSON response from the API
 */
export async function fetchFromTMDB(endpoint: string, params: Record<string, string> = {}) {
  // Check if we have at least one auth method available
  if (!TMDB_API_KEY && !TMDB_ACCESS_TOKEN) {
    console.error('Neither TMDB_API_KEY nor TMDB_ACCESS_TOKEN is defined in environment variables');
    // Return mock data instead of throwing an error
    return getMockData(endpoint);
  }

  const url = new URL(`${TMDB_BASE_URL}/${endpoint}`);

  // Add auth parameters based on the configured method
  if (TMDB_AUTH_METHOD === 'api_key' && TMDB_API_KEY) {
    url.searchParams.append('api_key', TMDB_API_KEY);
  }

  // Add language parameter
  url.searchParams.append('language', 'en-US');

  // Add additional parameters
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  const options: RequestInit = {
    method: 'GET',
    headers: {}
  };

  // If using bearer token auth, add the Authorization header
  if (TMDB_AUTH_METHOD === 'bearer_token' && TMDB_ACCESS_TOKEN) {
    options.headers = {
      Authorization: `Bearer ${TMDB_ACCESS_TOKEN}`,
      accept: 'application/json'
    };
  }

  try {
    const response = await fetch(url.toString(), options);

    if (!response.ok) {
      // Log error without exposing the URL or API key
      console.error(`TMDb API error for endpoint "${endpoint}": ${response.status} ${response.statusText}`);
      return getMockData(endpoint);
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching from TMDb:', error);
    return getMockData(endpoint);
  }
}

/**
 * Returns mock data for different endpoint types when the API is unavailable
 */
function getMockData(endpoint: string) {
  console.log(`Returning mock data for endpoint: ${endpoint}`);

  // Extract the main resource type from the endpoint
  const isMovie = endpoint.includes('movie/');
  const isTv = endpoint.includes('tv/');
  const isRecommendations = endpoint.includes('/recommendations');
  const isPopular = endpoint.includes('/popular');
  const isSeason = endpoint.includes('/season/');

  // Default mock item structure
  const mockMovieItem = {
    id: 550,
    title: "Fight Club",
    overview: "A ticking-time-bomb insomniac and a slippery soap salesman channel primal male aggression into a shocking new form of therapy.",
    poster_path: "/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg",
    backdrop_path: "/rr7E0NoGKxvbkb89eR1GwfoYjpA.jpg",
    release_date: "1999-10-15",
    vote_average: 8.4,
    media_type: "movie",
    genre_ids: [18, 53, 35]
  };

  const mockTvItem = {
    id: 1396,
    name: "Breaking Bad",
    overview: "A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine in order to secure his family's future.",
    poster_path: "/ggFHVNu6YYI5L9pCfOacjizRGt.jpg",
    backdrop_path: "/tsRy63Mu5cu8etL1X7ZLyf7UP1M.jpg",
    first_air_date: "2008-01-20",
    vote_average: 8.7,
    media_type: "tv",
    genre_ids: [18, 80]
  };

  // Mock data for movie endpoints
  if (isMovie) {
    if (isRecommendations) {
      return {
        page: 1,
        results: Array(10).fill(null).map((_, i) => ({
          ...mockMovieItem,
          id: 550 + i,
          title: `Mock Movie Recommendation ${i + 1}`
        }))
      };
    }

    if (isPopular) {
      return {
        page: 1,
        results: Array(10).fill(null).map((_, i) => ({
          ...mockMovieItem,
          id: 550 + i,
          title: `Mock Popular Movie ${i + 1}`
        }))
      };
    }

    // Single movie
    return mockMovieItem;
  }

  // Mock data for TV endpoints
  if (isTv) {
    if (isRecommendations) {
      return {
        page: 1,
        results: Array(10).fill(null).map((_, i) => ({
          ...mockTvItem,
          id: 1396 + i,
          name: `Mock TV Recommendation ${i + 1}`
        }))
      };
    }

    if (isPopular) {
      return {
        page: 1,
        results: Array(10).fill(null).map((_, i) => ({
          ...mockTvItem,
          id: 1396 + i,
          name: `Mock Popular TV Show ${i + 1}`
        }))
      };
    }

    if (isSeason) {
      return {
        id: 1396,
        name: "Season 1",
        episodes: Array(8).fill(null).map((_, i) => ({
          id: 62085 + i,
          name: `Mock Episode ${i + 1}`,
          overview: `This is a mock episode ${i + 1} description.`,
          episode_number: i + 1,
          still_path: "/qyoCNLAg3hUFMtXm1AXZ1BFyAZx.jpg",
          air_date: "2008-01-20",
          runtime: 45
        }))
      };
    }

    // Single TV show
    return mockTvItem;
  }

  // General search/discover
  return {
    page: 1,
    results: [
      ...Array(5).fill(null).map((_, i) => ({
        ...mockMovieItem,
        id: 550 + i,
        title: `Mock Movie ${i + 1}`
      })),
      ...Array(5).fill(null).map((_, i) => ({
        ...mockTvItem,
        id: 1396 + i,
        name: `Mock TV Show ${i + 1}`
      }))
    ]
  };
}