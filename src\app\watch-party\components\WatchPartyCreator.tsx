'use client'

import React, { useState, useMemo, use<PERSON><PERSON>back, forwardRef } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Loader2, Link, Users, Play, Copy, Check, Calendar, Clock, UserCircle, Film, Hash, Settings, MessageCircle, Lock, CalendarClock, RefreshCw, ArrowRight, Info, MessageSquare } from 'lucide-react'
import ContentSelector, { Content as SelectorContent } from './ContentSelector'
import { useWatchParty } from '@/lib/WatchPartyContext'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { motion } from 'framer-motion'
import { useToast } from '@/components/ui/use-toast'
import { useError } from '@/lib/ErrorContext'
import { WatchPartyState, WatchPartyMember } from '@/lib/WatchPartyContext'
import { IContent } from '@/data/content'
import { Separator } from '@/components/ui/separator'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Content } from '@/types'
import { useWatchPartyWithPusher, IContent as PusherContent } from '@/hooks/useWatchPartyWithPusher'
import { ContentCardType } from '@/lib/content-utils'

interface WatchPartyCreatorProps {
  className?: string;
  onBackToList?: () => void;
}

interface ExtendedParty extends WatchPartyState {
  contentId: string;
}

// Create a completely isolated form input component with forwarded ref
const FocusableInput = forwardRef<
  HTMLInputElement,
  {
    id: string;
    label: string;
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    icon?: React.ReactNode;
  }
>(({ id, label, value, onChange, placeholder, icon }, ref) => {
  return (
    <div className="space-y-3">
      <Label htmlFor={id} className="text-white font-medium flex items-center">
        {icon && <span className="mr-2 text-sky-400">{icon}</span>}
        {label}
      </Label>
      <div className="relative">
        <Input
          ref={ref}
          id={id}
          placeholder={placeholder}
          className="bg-black/20 border-white/10 text-white rounded-lg focus:border-sky-500 focus:ring-1 focus:ring-sky-500 pl-4 py-6"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
        {icon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 opacity-50">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
});

FocusableInput.displayName = 'FocusableInput';

// Isolated party form component
const PartyForm = ({
  selectedContent,
  onCreateParty
}: {
  selectedContent: Content | null;
  onCreateParty: (data: {
    partyName: string;
    enableChat: boolean;
    enableScheduling: boolean;
    isPrivateParty: boolean;
    scheduleDate?: Date;
    scheduleTime?: string;
  }) => void;
}) => {
  // Removed userName state as we'll use the authenticated user's name
  const [partyName, setPartyName] = useState('')
  const [enableChat, setEnableChat] = useState(true)
  const [enableScheduling, setEnableScheduling] = useState(false)
  const [isPrivateParty, setIsPrivateParty] = useState(true)
  const [scheduleDate, setScheduleDate] = useState<Date>()
  const [scheduleTime, setScheduleTime] = useState('')
  const [isCreatingParty, setIsCreatingParty] = useState(false)

  // Update party name when content changes
  React.useEffect(() => {
    if (selectedContent && !partyName) {
      setPartyName(`${selectedContent.title} Party`)
    }
  }, [selectedContent, partyName])

  // Generate time options
  const timeOptions = useMemo(() => {
    const options = []
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const hourFormatted = hour.toString().padStart(2, '0')
        const minuteFormatted = minute.toString().padStart(2, '0')
        const time = `${hourFormatted}:${minuteFormatted}`
        const label = new Date(0, 0, 0, hour, minute).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
        options.push({ value: time, label })
      }
    }
    return options
  }, [])

  const handleSubmit = () => {
    setIsCreatingParty(true)
    onCreateParty({
      partyName,
      enableChat,
      enableScheduling,
      isPrivateParty,
      scheduleDate,
      scheduleTime
    })
    setIsCreatingParty(false)
  }

  return (
    <div className="wp-glass-card rounded-xl p-6 space-y-6 shadow-lg">
      <div className="flex items-center space-x-3">
        <div className="bg-gradient-to-r from-blue-600 to-teal-500 p-2.5 rounded-lg shadow-md">
          <Play className="h-5 w-5 text-white" fill="white" />
        </div>
        <h2 className="text-xl font-semibold text-white">Party Details</h2>
      </div>

      <FocusableInput
        id="party-name"
        label="Party Name"
        value={partyName}
        onChange={setPartyName}
        placeholder="Give your party a name"
        icon={<Film className="h-5 w-5" />}
      />

      <Separator className="bg-white/10 my-2" />

      <div className="space-y-5">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-teal-500 to-blue-600 p-2 rounded-lg shadow-md">
            <Settings className="h-4 w-4 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-white">Party Settings</h3>
        </div>

        <div className="flex items-center justify-between py-3 px-4 rounded-lg bg-black/20 border border-white/5 hover:border-blue-500/30 transition-colors duration-200">
          <div className="space-y-0.5">
            <Label htmlFor="chat-toggle" className="text-white font-medium flex items-center">
              <MessageCircle className="h-4 w-4 mr-2 text-blue-400" />
              Enable Chat
            </Label>
            <p className="text-sm text-slate-400">Allow chat during the watch party</p>
          </div>
          <Switch
            id="chat-toggle"
            checked={enableChat}
            onCheckedChange={setEnableChat}
            className="data-[state=checked]:bg-gradient-to-r from-blue-500 to-teal-600 data-[state=checked]:border-0"
          />
        </div>

        <div className="flex items-center justify-between py-3 px-4 rounded-lg bg-black/20 border border-white/5 hover:border-blue-500/30 transition-colors duration-200">
          <div className="space-y-0.5">
            <Label htmlFor="private-toggle" className="text-white font-medium flex items-center">
              <Lock className="h-4 w-4 mr-2 text-blue-400" />
              Private Party
            </Label>
            <p className="text-sm text-slate-400">Only people with the code can join</p>
          </div>
          <Switch
            id="private-toggle"
            checked={isPrivateParty}
            onCheckedChange={setIsPrivateParty}
            className="data-[state=checked]:bg-gradient-to-r from-blue-500 to-teal-600 data-[state=checked]:border-0"
          />
        </div>

        <div className="flex items-center justify-between py-3 px-4 rounded-lg bg-black/20 border border-white/5 hover:border-blue-500/30 transition-colors duration-200">
          <div className="space-y-0.5">
            <Label htmlFor="schedule-toggle" className="text-white font-medium flex items-center">
              <CalendarClock className="h-4 w-4 mr-2 text-blue-400" />
              Schedule for Later
            </Label>
            <p className="text-sm text-slate-400">Set a future date and time</p>
          </div>
          <Switch
            id="schedule-toggle"
            checked={enableScheduling}
            onCheckedChange={setEnableScheduling}
            className="data-[state=checked]:bg-gradient-to-r from-blue-500 to-teal-600 data-[state=checked]:border-0"
          />
        </div>

        {enableScheduling && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4 mt-2 bg-gradient-to-b from-blue-900/30 to-teal-900/30 p-5 rounded-xl border border-blue-500/20 backdrop-blur-sm"
          >
            <div className="grid grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label className="text-white font-medium flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-blue-400" />
                  Date
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal border-white/10 bg-black/20 text-white rounded-lg hover:bg-black/30 hover:border-blue-500/30",
                        !scheduleDate && "text-slate-400"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4 text-blue-400" />
                      {scheduleDate ? format(scheduleDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 bg-slate-900 border-white/10 rounded-lg shadow-xl">
                    <CalendarComponent
                      mode="single"
                      selected={scheduleDate}
                      onSelect={setScheduleDate}
                      initialFocus
                      disabled={(date) => date < new Date()}
                      className="bg-slate-900 text-white rounded-lg"
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-white font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-blue-400" />
                  Time
                </Label>
                <Select value={scheduleTime} onValueChange={setScheduleTime}>
                  <SelectTrigger className="border-white/10 bg-black/20 text-white rounded-lg hover:bg-black/30 hover:border-blue-500/30">
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-900 border-white/10 text-white rounded-lg">
                    {timeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value} className="focus:bg-blue-500/20 focus:text-white">
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      <Button
        onClick={handleSubmit}
        className={`w-full mt-4 font-medium py-6 h-auto text-lg shadow-lg ${isCreatingParty || !selectedContent ?
          'bg-slate-700/50 text-slate-300 cursor-not-allowed' :
          'bg-gradient-to-r from-blue-500 to-teal-600 hover:from-blue-400 hover:to-teal-500 text-white'}`}
        disabled={isCreatingParty || !selectedContent}
      >
        {isCreatingParty ? (
          <>
            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
            Creating Party...
          </>
        ) : (
          <>
            <Play className="mr-2 h-5 w-5" fill="currentColor" />
            Create Party
          </>
        )}
      </Button>
    </div>
  );
};

// Isolated join form component
const JoinForm = ({
  onJoinParty
}: {
  onJoinParty: (data: { partyCode: string }) => void;
}) => {
  const [partyCode, setPartyCode] = useState('')
  const [isJoining, setIsJoining] = useState(false)

  const handleSubmit = () => {
    setIsJoining(true)
    onJoinParty({ partyCode })
    setIsJoining(false)
  }

  return (
    <Card className="wp-glass-card border-white/10 shadow-lg rounded-xl overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-blue-900/50 to-teal-900/50 border-b border-white/10 pb-5">
        <div className="flex items-center space-x-3 mb-2">
          <div className="bg-gradient-to-r from-blue-500 to-teal-600 p-2.5 rounded-lg shadow-md">
            <Users className="h-5 w-5 text-white" />
          </div>
          <CardTitle className="text-xl text-white">Join an existing party</CardTitle>
        </div>
        <CardDescription className="text-slate-300">
          Enter a party code to join friends watching now
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <FocusableInput
          id="party-code"
          label="Party Code"
          value={partyCode}
          onChange={setPartyCode}
          placeholder="Enter the party code"
          icon={<Hash className="h-5 w-5" />}
        />

        <Button
          onClick={handleSubmit}
          className={`w-full mt-6 font-medium py-6 h-auto text-lg shadow-lg ${isJoining ?
            'bg-slate-700/50 text-slate-300 cursor-not-allowed' :
            'bg-gradient-to-r from-blue-500 to-teal-600 hover:from-blue-400 hover:to-teal-500 text-white'}`}
          disabled={isJoining}
        >
          {isJoining ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Joining Party...
            </>
          ) : (
            <>
              <Users className="mr-2 h-5 w-5" />
              Join Party
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

// Helper to convert SelectorContent to Content for creator
const convertToCreatorContent = (content: SelectorContent | null): Content | null => {
  if (!content) return null;

  return {
    id: content.id,
    title: content.title,
    posterPath: content.posterPath || '',
    backdropPath: content.backdropPath || '',
    type: content.type,
    year: content.releaseDate ? new Date(content.releaseDate).getFullYear() : undefined,
    rating: content.rating || 0,
    genres: content.genres || [],
    overview: content.description || ''
  };
};

// Debug function to log content details
function logContentDetails(prefix: string, content: any) {
  console.log(`${prefix} Content:`, {
    id: content?.id,
    title: content?.title,
    type: content?.type,
    posterPath: content?.posterPath?.substring(0, 50) + '...',
  });
}

export default function WatchPartyCreator({ className = '', onBackToList }: WatchPartyCreatorProps) {
  const [tab, setTab] = useState<'create' | 'join'>('create')
  const [selectedContent, setSelectedContent] = useState<Content | null>(null)
  const [isCreatingParty, setIsCreatingParty] = useState(false)
  const [isJoiningParty, setIsJoiningParty] = useState(false)

  const { createParty, joinParty, availableParties } = useWatchParty()
  const { addAlert } = useError()
  const { toast } = useToast()
  const router = useRouter()
  const { createParty: createPartyWithPusher, fetchAvailableParties } = useWatchPartyWithPusher()

  // Handle returning to the party list (main view)
  const handleBackToList = () => {
    // Use the callback if provided by the parent
    if (onBackToList) {
      onBackToList();
      return;
    }

    // Fallback to direct navigation if no callback
    setIsCreatingParty(false);
    router.push('/watch-party');
  }

  // Handle content selection with type conversion
  const handleContentSelect = useCallback((content: SelectorContent | null) => {
    console.log('[WatchParty Creator] Content selected:', content?.title);
    const convertedContent = convertToCreatorContent(content);

    if (content && convertedContent) {
      // Log details for debugging
      logContentDetails('[WatchParty Creator] Original', content);
      logContentDetails('[WatchParty Creator] Converted', convertedContent);
    }

    setSelectedContent(convertedContent);
  }, []);

  // Handle party creation with validation
  const handleCreateParty = async (formData: {
    partyName: string;
    enableChat: boolean;
    enableScheduling: boolean;
    isPrivateParty: boolean;
    scheduleDate?: Date;
    scheduleTime?: string;
  }) => {
    if (!selectedContent || !formData.partyName.trim()) {
      addAlert({
        title: 'Missing Information',
        message: !selectedContent ? 'Please select content to watch' :
                'Please enter a party name',
        type: 'error'
      })
      return
    }

    // Validate scheduled time if scheduling is enabled
    if (formData.enableScheduling) {
      if (!formData.scheduleDate) {
        addAlert({
          title: 'Missing Date',
          message: 'Please select a date for the scheduled party',
          type: 'error'
        })
        return
      }

      if (!formData.scheduleTime) {
        addAlert({
          title: 'Missing Time',
          message: 'Please select a time for the scheduled party',
          type: 'error'
        })
        return
      }

      // Check if scheduled date and time are in the future
      if (formData.scheduleDate && formData.scheduleTime) {
        const scheduledDateTime = new Date(formData.scheduleDate)
        const [hours, minutes] = formData.scheduleTime.split(':').map(Number)
        scheduledDateTime.setHours(hours, minutes)

        if (scheduledDateTime <= new Date()) {
          addAlert({
            title: 'Invalid Schedule',
            message: 'The scheduled time must be in the future',
            type: 'error'
          })
          return
        }
      }
    }

    try {
      // Set loading state to prevent double submissions
      setIsCreatingParty(true)

      // No need to save username to localStorage as we're using the authenticated user's name

      // Convert Content to format expected by the Pusher API
      const contentObj: PusherContent = {
        id: String(selectedContent.id), // Ensure id is a string
        title: formData.partyName || selectedContent.title || 'Untitled Content', // Use party name as title
        posterPath: selectedContent.posterPath || '/images/content/placeholder.jpg',
        backdropPath: selectedContent.backdropPath || '',
        type: (selectedContent.type === 'show' ? 'show' : 'movie'),
        overview: selectedContent.overview || '',
        year: selectedContent.year
          ? selectedContent.year.toString()
          : new Date().getFullYear().toString(),
        rating: selectedContent.rating || 0,
        genres: selectedContent.genres || []
      }

      console.log(`[WatchParty Creator] Creating party "${formData.partyName}" with content: "${contentObj.title}" (${contentObj.id})`);

      // Create a party using the Pusher implementation with season and episode for TV shows
      let partyId: string;
      if (contentObj.type === 'show') {
        // Default to season 1, episode 1 for TV shows
        const defaultSeason = 1;
        const defaultEpisode = 1;
        console.log(`[WatchParty Creator] Creating TV show party for "${contentObj.title}" with season ${defaultSeason}, episode ${defaultEpisode}`);
        partyId = await createPartyWithPusher(contentObj, defaultSeason, defaultEpisode, formData.isPrivateParty);
      } else {
        console.log(`[WatchParty Creator] Creating movie party for "${contentObj.title}"`);
        partyId = await createPartyWithPusher(contentObj, undefined, undefined, formData.isPrivateParty);
      }

      if (!partyId) {
        throw new Error('No party ID returned from server');
      }

      // Show success toast
      toast({
        title: "Party Created!",
        description: "Your watch party has been created successfully.",
      })

      // Log the successful party creation
      console.log(`[WatchParty Creator] Successfully created party with ID: ${partyId}`);

      // Try to verify that the party shows up in the available parties
      let partyFound = false;
      for (let attempt = 1; attempt <= 3; attempt++) {
        console.log(`[WatchParty Creator] Verification attempt ${attempt}/3: Refreshing parties list...`);
        try {
          // Force fetch the latest parties
          const parties = await fetchAvailableParties(true);

          const found = parties.some((p: any) => p.id === partyId);
          if (found) {
            console.log(`[WatchParty Creator] Verification successful: Party ${partyId} found in available parties!`);
            partyFound = true;
            break;
          } else {
            console.warn(`[WatchParty Creator] Verification warning: Party ${partyId} not found in available parties yet`);
            // Wait a moment before checking again
            await new Promise(resolve => setTimeout(resolve, 500 * attempt));
          }
        } catch (error) {
          console.error(`[WatchParty Creator] Verification error on attempt ${attempt}:`, error);
          // Wait a moment before trying again
          await new Promise(resolve => setTimeout(resolve, 500 * attempt));
        }
      }

      if (!partyFound) {
        console.warn(`[WatchParty Creator] Party ${partyId} created but not found in available parties after verification. Will continue anyway.`);
      }

      // Return to the watch party list after a small delay to allow state updates
      setTimeout(() => {
        console.log(`[WatchParty Creator] Navigating back to party list view with newly created party: ${partyId}`);
        // Instead of directly navigating to the party page, which might be causing
        // React hook issues, let's go back to the dashboard and let it handle displaying
        // the newly created party
        handleBackToList();
      }, 500);

    } catch (error) {
      console.error('[WatchParty Creator] Error creating party:', error);
      setIsCreatingParty(false); // Only reset loading state on error

      addAlert({
        title: 'Party Creation Failed',
        message: error instanceof Error ? error.message : 'Failed to create watch party',
        type: 'error'
      });
    }
    // Don't reset isCreatingParty here - we want to stay in loading state until redirect
  }

  // Handle party join with validation
  const handleJoinParty = async (formData: { partyCode: string }) => {
    if (!formData.partyCode.trim()) {
      addAlert({
        title: 'Missing Information',
        message: 'Please enter a party code',
        type: 'error'
      })
      return
    }

    try {
      setIsJoiningParty(true)

      const party = availableParties.find((p: WatchPartyState) => p.id === formData.partyCode) as ExtendedParty | undefined

      if (!party) {
        addAlert({
          title: 'Party Not Found',
          message: 'The party code you entered is invalid or the party no longer exists',
          type: 'error'
        })
        setIsJoiningParty(false)
        return
      }

      // Call join party function - use authenticated user's name
      joinParty(formData.partyCode)

      toast({
        title: 'Party Joined',
        description: 'You have successfully joined the watch party',
      })

      // Navigate after successful join
      setTimeout(() => {
        router.push(`/watch-party/${formData.partyCode}`)
      }, 1000)
    } catch (error) {
      console.error('Error joining party:', error)
      addAlert({
        title: 'Join Failed',
        message: 'Something went wrong while joining the watch party',
        type: 'error'
      })
    } finally {
      setIsJoiningParty(false)
    }
  }

  // Memoized content selector
  const MemoizedContentSelector = useMemo(() => (
    <ContentSelector
      onSelectContent={handleContentSelect}
    />
  ), [handleContentSelect])

  return (
    <div className={`${className} space-y-6 p-6`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-r from-blue-600 to-teal-500 p-2 rounded-lg shadow-md">
            <Play className="h-4 w-4 text-white" fill="white" />
          </div>
          <h2 className="text-xl font-semibold text-white">Create Watch Party</h2>
        </div>
        <Button
          variant="outline"
          onClick={onBackToList}
          className="border-blue-300/20 text-blue-100 hover:bg-blue-500/20"
        >
          <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
          Back to Parties
        </Button>
      </div>

      <Tabs defaultValue="create" className="w-full">
        <TabsList className="bg-black/30 border border-blue-500/10 rounded-lg p-1 mb-6">
          <TabsTrigger
            value="create"
            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
          >
            Create Party
          </TabsTrigger>
          <TabsTrigger
            value="join"
            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
          >
            Join Party
          </TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6 mt-2">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="lg:col-span-1 space-y-6">
              <PartyForm
                selectedContent={selectedContent}
                onCreateParty={async (formData) => {
                  await handleCreateParty(formData);
                }}
              />
            </div>

            <div className="lg:col-span-3">
              <ContentSelector onSelectContent={handleContentSelect} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="join" className="space-y-6 mt-2">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="lg:col-span-1">
              <JoinForm onJoinParty={handleJoinParty} />
            </div>

            <div className="lg:col-span-3 bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/10">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-400" />
                  Joining a Watch Party
                </h3>

                <p className="text-vista-light/80">
                  To join an existing watch party, you need the party code from the host. Ask them to share it with you, then enter it along with your display name.
                </p>

                <div className="bg-black/30 p-4 rounded-lg border border-blue-500/10 mt-4">
                  <h4 className="text-blue-400 font-medium mb-2">What to expect:</h4>
                  <ul className="space-y-2 text-vista-light/70">
                    <li className="flex items-start gap-2">
                      <Play className="h-4 w-4 text-blue-400 mt-1 flex-shrink-0" />
                      <span>Synchronized playback with everyone in the party</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <MessageSquare className="h-4 w-4 text-blue-400 mt-1 flex-shrink-0" />
                      <span>Live chat during the viewing experience</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Users className="h-4 w-4 text-blue-400 mt-1 flex-shrink-0" />
                      <span>See who's watching and their status</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}