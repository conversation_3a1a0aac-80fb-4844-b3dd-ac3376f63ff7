import { WatchPartyMember } from '@/lib/watch-party'

export type MessageType = 'text' | 'emoji' | 'gif' | 'system' | 'reaction' | 'chat'

export interface ChatMessage {
  id: string
  type: MessageType
  content: string
  sender: WatchPartyMember
  timestamp: string
  metadata?: {
    replyTo?: string // ID of the message being replied to
    mentions?: string[] // Array of mentioned user IDs
    reactions?: {
      [emoji: string]: string[] // emoji -> array of user IDs who reacted
    }
    edited?: boolean
  }
}

export interface TypingIndicator {
  userId: string
  userName: string
  timestamp: number
}

export interface ChatState {
  messages: ChatMessage[]
  typingUsers: TypingIndicator[]
  unreadCount: number
  lastReadTimestamp: string
}

export interface EmojiPickerProps {
  onSelect: (emoji: string) => void
  onClose: () => void
}

export interface ChatInputProps {
  onSendMessage: (message: string) => void
  onStartTyping: () => void
  onStopTyping: () => void
  disabled?: boolean
  placeholder?: string
}

export interface ChatMessageProps {
  message: ChatMessage
  currentUserId: string
  onReaction?: (messageId: string, emoji: string) => void
  onReply?: (messageId: string) => void
  onEdit?: (messageId: string, newContent: string) => void
  onDelete?: (messageId: string) => void
} 