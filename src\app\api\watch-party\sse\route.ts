import { NextRequest, NextResponse } from 'next/server';
import { pusherServer, WATCH_PARTY_EVENTS } from '@/lib/pusher-server';

// Track active SSE connections
const activeConnections = new Map<string, Set<ReadableStreamController<Uint8Array>>>();

// Message queue for each party - stores messages until they're delivered
const messageQueues = new Map<string, Array<any>>();

// Track controller states to avoid operations on closed controllers
const controllerStates = new WeakMap<ReadableStreamController<Uint8Array>, 'active' | 'closed'>();

// Helper to send an SSE message
function sendMessage(controller: ReadableStreamController<Uint8Array>, data: any) {
  // Check if controller is already closed
  if (controllerStates.get(controller) === 'closed') {
    return false;
  }

  try {
    controller.enqueue(
      new TextEncoder().encode(`data: ${JSON.stringify(data)}\n\n`)
    );
    return true;
  } catch (error) {
    // Mark controller as closed if we get an invalid state error
    if (error instanceof TypeError && error.message.includes('Controller is already closed')) {
      controllerStates.set(controller, 'closed');
    }
    return false;
  }
}

// Helper to create a heartbeat to keep connection alive
function startHeartbeat(controller: ReadableStreamController<Uint8Array>) {
  const interval = setInterval(() => {
    // If sending message fails, clean up the connection
    if (!sendMessage(controller, { type: 'heartbeat', timestamp: Date.now() })) {
      console.log('[SSE] Heartbeat failed, cleaning up connection');
      clearInterval(interval);
      safeCloseController(controller);
    }
  }, 30000); // Send heartbeat every 30 seconds

  return interval;
}

// Safely close a controller
function safeCloseController(controller: ReadableStreamController<Uint8Array>) {
  // Only try to close if not already closed
  if (controllerStates.get(controller) !== 'closed') {
    try {
      controller.close();
      controllerStates.set(controller, 'closed');
    } catch (error) {
      // Already closed or other error
      controllerStates.set(controller, 'closed');
    }
  }
}

// Add message handler to the watch party API
export async function broadcastToParty(partyId: string, message: any, type: string = 'new-message') {
  // Add to message queue for this party
  if (!messageQueues.has(partyId)) {
    messageQueues.set(partyId, []);
  }

  // Create the event data based on type
  const eventData = { type, [type === 'new-message' ? 'message' : 'playback']: message };

  // Add the message to the queue
  messageQueues.get(partyId)?.push(eventData);

  // Limit queue size to prevent memory issues
  const queue = messageQueues.get(partyId);
  if (queue && queue.length > 100) {
    queue.shift(); // Remove oldest message
  }

  // Broadcast to all active connections
  const connections = activeConnections.get(partyId);
  if (connections) {
    // Create a list of controllers to remove (can't modify set during iteration)
    const controllersToRemove = new Set<ReadableStreamController<Uint8Array>>();

    for (const controller of connections) {
      // If sending fails, mark for removal
      if (!sendMessage(controller, eventData)) {
        console.log('[SSE] Failed to send message, marking connection for removal');
        controllersToRemove.add(controller);
      }
    }

    // Remove any failed controllers
    if (controllersToRemove.size > 0) {
      console.log(`[SSE] Removing ${controllersToRemove.size} broken connections`);
      for (const controller of controllersToRemove) {
        connections.delete(controller);
        safeCloseController(controller);
      }

      // Clean up empty party connections
      if (connections.size === 0) {
        activeConnections.delete(partyId);
      }
    }
  }
}

export async function GET(request: NextRequest) {
  const partyId = request.nextUrl.searchParams.get('partyId');

  if (!partyId) {
    return NextResponse.json(
      { error: 'Missing partyId parameter' },
      { status: 400 }
    );
  }

  // Create a stream for SSE
  const stream = new ReadableStream({
    start: async (controller) => {
      console.log(`[SSE] New connection for party ${partyId}`);

      // Add connection to active connections for this party
      if (!activeConnections.has(partyId)) {
        activeConnections.set(partyId, new Set());
      }
      activeConnections.get(partyId)?.add(controller);

      // Mark controller as active
      controllerStates.set(controller, 'active');

      // Send initial connection message
      if (!sendMessage(controller, {
        type: 'connected',
        timestamp: Date.now(),
        message: 'Connected to watch party chat'
      })) {
        console.log('[SSE] Failed to send initial connection message, controller may be closed');
        return; // Exit early if we can't even send the initial message
      }

      // Send any messages in queue to catch up the client
      const queue = messageQueues.get(partyId) || [];
      for (const message of queue) {
        if (!sendMessage(controller, { type: 'new-message', message })) {
          console.log('[SSE] Failed to send cached message, controller may be closed');
          break; // Stop trying to send more messages
        }
      }

      // Start heartbeat to keep connection alive
      const heartbeatInterval = startHeartbeat(controller);

      // Store cleanup function to be called when connection closes
      const cleanup = () => {
        console.log(`[SSE] Cleaning up connection for party ${partyId}`);

        // Clear heartbeat interval
        clearInterval(heartbeatInterval);

        // Mark controller as closed
        controllerStates.set(controller, 'closed');

        // Remove from active connections
        const connections = activeConnections.get(partyId);
        if (connections) {
          connections.delete(controller);
          if (connections.size === 0) {
            console.log(`[SSE] No more connections for party ${partyId}, removing party`);
            activeConnections.delete(partyId);
          }
        }
      };

      // Store cleanup function in controller for access on error
      (controller as any).cleanup = cleanup;
    },
    cancel: (reason) => {
      console.log(`[SSE] Connection cancelled for party ${partyId}:`, reason);

      // Get all controllers for this party
      const connections = activeConnections.get(partyId);
      if (connections) {
        // Find controllers that need cleanup
        for (const controller of connections) {
          // Run cleanup if available
          if ((controller as any).cleanup) {
            try {
              (controller as any).cleanup();
            } catch (error) {
              console.error('[SSE] Error during cleanup:', error);
            }
          } else {
            // If no cleanup function, at least mark as closed
            controllerStates.set(controller, 'closed');
          }
        }
      }
    }
  });

  // Return the SSE stream with appropriate headers
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  });
}