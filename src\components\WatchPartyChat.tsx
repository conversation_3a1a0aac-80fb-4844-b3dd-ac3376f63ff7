'use client';

import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { usePusher } from '@/hooks/usePusher';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Send, Smile, AtSign, Image, Paperclip,
  User, UserCheck, Info, ArrowDown,
  MessageSquare, Clock
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

interface Message {
  id: string;
  userId: string;
  username: string;
  content: string;
  timestamp: number;
  isHost?: boolean;
  type?: 'chat' | 'system' | 'reaction';
}

interface WatchPartyChatProps {
  partyId: string;
  userId: string;
  username: string;
  isHost?: boolean;
  initialMessages?: Message[];
  className?: string;
  showHeader?: boolean;
  height?: string;
  onMessageSent?: (message: Message) => void;
}

export default function WatchPartyChat({
  partyId,
  userId,
  username,
  isHost = false,
  initialMessages = [],
  className = "",
  showHeader = true,
  height = "h-[400px]",
  onMessageSent
}: WatchPartyChatProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const [userIsTyping, setUserIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<{[key: string]: { username: string, timestamp: number }}>({});

  const chatEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const channelName = `watch-party-${partyId}`;
  const messageEventName = 'new-message';
  const typingEventName = 'user-typing';

  // Format timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message groups
  const formatMessageDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const today = new Date();

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    }

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }

    return date.toLocaleDateString();
  };

  // Handle incoming messages from Pusher
  const handleIncomingMessage = useCallback((data: Message) => {
    setMessages((prev) => [...prev, data]);

    // If user was at bottom before new message, scroll to bottom
    if (scrollAreaRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 50;

      if (isAtBottom) {
        setTimeout(() => scrollToBottom(), 100);
      } else {
        setShowScrollButton(true);
      }
    }
  }, []);

  // Handle typing notification
  const handleTypingNotification = useCallback((data: {userId: string, username: string, isTyping: boolean}) => {
    if (data.userId === userId) return; // Don't show own typing indicator

    setTypingUsers(prev => {
      const newState = {...prev};

      if (data.isTyping) {
        newState[data.userId] = {
          username: data.username,
          timestamp: Date.now()
        };
      } else {
        delete newState[data.userId];
      }

      return newState;
    });
  }, [userId]);

  // Initialize Pusher connection for messages
  usePusher({
    channelName,
    eventName: messageEventName,
    callback: handleIncomingMessage,
  });

  // Initialize Pusher connection for typing indicators
  usePusher({
    channelName,
    eventName: typingEventName,
    callback: handleTypingNotification,
  });

  // Clean up typing indicators for users who stopped typing
  useEffect(() => {
    const interval = setInterval(() => {
      setTypingUsers(prev => {
        const now = Date.now();
        const newState = {...prev};
        let hasChanges = false;

        Object.entries(newState).forEach(([userId, data]) => {
          // Remove typing indicator after 3 seconds of inactivity
          if (now - data.timestamp > 3000) {
            delete newState[userId];
            hasChanges = true;
          }
        });

        return hasChanges ? newState : prev;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Typing notification state to track last sent notification
  const lastTypingNotificationRef = useRef<{isTyping: boolean, timestamp: number}>({ isTyping: false, timestamp: 0 });

  // Send a typing notification with throttling
  const sendTypingNotification = useCallback((isTyping: boolean) => {
    const now = Date.now();
    const lastNotification = lastTypingNotificationRef.current;

    // Skip if same state was sent recently (within 3 seconds for typing=true, 1 second for typing=false)
    const minInterval = isTyping ? 3000 : 1000;
    if (lastNotification.isTyping === isTyping && now - lastNotification.timestamp < minInterval) {
      return; // Skip redundant notification
    }

    // Update last notification state
    lastTypingNotificationRef.current = { isTyping, timestamp: now };

    // Send notification through Pusher
    fetch('/api/pusher', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel: channelName,
        event: typingEventName,
        data: {
          userId,
          username,
          isTyping
        },
      }),
    }).catch(error => {
      console.error('Error sending typing notification:', error);
    });
  }, [channelName, userId, username]);

  // Handle input change with typing notification
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);

    // Only send typing notification if there's actual content
    if (e.target.value.trim()) {
      // Only send typing=true notification if state changed
      if (!userIsTyping) {
        setUserIsTyping(true);
        sendTypingNotification(true);
      }

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to clear typing status (longer timeout to reduce messages)
      typingTimeoutRef.current = setTimeout(() => {
        if (userIsTyping) {
          setUserIsTyping(false);
          sendTypingNotification(false);
        }
      }, 4000); // Increased from 2000ms to 4000ms to reduce frequency
    } else if (userIsTyping) {
      // If input is empty and user was typing, clear typing state immediately
      setUserIsTyping(false);
      sendTypingNotification(false);

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    }
  };

  // Send a new message
  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    // Clear typing notification immediately
    if (userIsTyping) {
      setUserIsTyping(false);
      sendTypingNotification(false);

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    }

    const message: Message = {
      id: `${Date.now()}-${userId}`,
      userId,
      username,
      content: newMessage,
      timestamp: Date.now(),
      isHost,
      type: 'chat'
    };

    try {
      setIsLoading(true);

      // Send the message via API
      const response = await fetch('/api/pusher', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel: channelName,
          event: messageEventName,
          data: message,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      setNewMessage('');

      // Focus input again after sending
      inputRef.current?.focus();

      // Notify parent if callback provided
      if (onMessageSent) {
        onMessageSent(message);
      }

      // Scroll to bottom after sending
      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Add emoji to message
  const handleEmojiSelect = (emoji: any) => {
    setNewMessage(prev => prev + emoji.native);
    setIsEmojiPickerOpen(false);
    inputRef.current?.focus();
  };

  // Send reaction
  const sendReaction = async (emoji: string) => {
    const reaction: Message = {
      id: `${Date.now()}-${userId}`,
      userId,
      username,
      content: emoji,
      timestamp: Date.now(),
      isHost,
      type: 'reaction'
    };

    try {
      // Send the reaction via API
      const response = await fetch('/api/pusher', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel: channelName,
          event: messageEventName,
          data: reaction,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send reaction');
      }

      // Notify parent if callback provided
      if (onMessageSent) {
        onMessageSent(reaction);
      }
    } catch (error) {
      console.error('Error sending reaction:', error);
    }
  };

  // Scroll to bottom
  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    setShowScrollButton(false);
  };

  // Handle scroll in chat area
  const handleScroll = () => {
    if (scrollAreaRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 50;

      setShowScrollButton(!isAtBottom);
    }
  };

  // Auto-scroll to bottom on initial load
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, []);

  // Group messages by user and timeframe
  const groupedMessages = useMemo(() => {
    const groups: { userId: string, username: string, messages: Message[], isHost: boolean }[] = [];
    let lastUserId = '';
    let lastTimestamp = 0;

    messages.forEach(message => {
      // Group message if:
      // 1. Same user as last message
      // 2. Within 3 minutes of last message
      // 3. Not a system or reaction message
      const isSameUser = message.userId === lastUserId;
      const isWithinTimeframe = message.timestamp - lastTimestamp < 3 * 60 * 1000;
      const isRegularMessage = message.type !== 'system' && message.type !== 'reaction';

      if (isSameUser && isWithinTimeframe && isRegularMessage && groups.length > 0) {
        // Add to existing group
        groups[groups.length - 1].messages.push(message);
      } else {
        // Create new group
        groups.push({
          userId: message.userId,
          username: message.username,
          messages: [message],
          isHost: message.isHost || false
        });
      }

      lastUserId = message.userId;
      lastTimestamp = message.timestamp;
    });

    return groups;
  }, [messages]);

  return (
    <div className={`flex flex-col bg-vista-dark-secondary rounded-lg ${height} border border-vista-dark-border overflow-hidden ${className}`}>
      {/* Chat header */}
      {showHeader && (
        <div className="p-3 border-b border-vista-dark-border flex justify-between items-center">
          <h3 className="text-lg font-semibold text-vista-light flex items-center">
            <MessageSquare className="w-4 h-4 mr-2" />
            Watch Party Chat
          </h3>
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                    <Info className="h-4 w-4 text-vista-light/70" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Chat with your watch party members in real-time</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      {/* Messages area */}
      <ScrollArea
        className="flex-1 p-4"
        onScroll={handleScroll}
        ref={scrollAreaRef}
      >
        <div className="space-y-6">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[200px] text-vista-light/50">
              <MessageSquare className="h-12 w-12 mb-3 opacity-20" />
              <p className="text-sm">No messages yet</p>
              <p className="text-xs mt-1">Start the conversation!</p>
            </div>
          ) : (
            <>
              {/* Grouped messages */}
              {groupedMessages.map((group, index) => {
                const firstMessage = group.messages[0];

                // Check if message is a system message
                if (firstMessage.type === 'system') {
                  return (
                    <div key={firstMessage.id} className="flex justify-center">
                      <div className="bg-vista-dark-tertiary/30 rounded-full px-3 py-1 text-xs text-vista-light/60">
                        {firstMessage.content}
                      </div>
                    </div>
                  );
                }

                // Check if message is a reaction
                if (firstMessage.type === 'reaction') {
                  return (
                    <motion.div
                      key={firstMessage.id}
                      className="flex justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 260, damping: 20 }}
                    >
                      <div className="flex flex-col items-center">
                        <div className="text-3xl mb-1">{firstMessage.content}</div>
                        <div className="text-xs text-vista-light/50">{firstMessage.username}</div>
                      </div>
                    </motion.div>
                  );
                }

                // Show date separator if needed (first message or different day)
                const showDateSeparator = index === 0 || (
                  new Date(firstMessage.timestamp).toDateString() !==
                  new Date(groupedMessages[index-1].messages[0].timestamp).toDateString()
                );

                // Regular message group
                return (
                  <div key={firstMessage.id}>
                    {showDateSeparator && (
                      <div className="flex items-center my-4">
                        <div className="flex-grow h-px bg-vista-dark-border"></div>
                        <div className="px-3 text-xs text-vista-light/50">
                          {formatMessageDate(firstMessage.timestamp)}
                        </div>
                        <div className="flex-grow h-px bg-vista-dark-border"></div>
                      </div>
                    )}

                    <div
                      className={`flex items-start gap-2 ${
                        group.userId === userId ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      {group.userId !== userId && (
                        <Avatar className="h-8 w-8 mt-1">
                          <AvatarImage src={`https://api.dicebear.com/7.x/bottts/svg?seed=${group.username}`} />
                          <AvatarFallback className={group.isHost ? "bg-vista-blue" : ""}>
                            {group.username.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      )}

                      <div className="flex flex-col">
                        {group.userId !== userId && (
                          <div className="flex items-center mb-1">
                            <p className="text-xs font-semibold text-vista-light mr-1">
                              {group.username}
                            </p>
                            {group.isHost && (
                              <Badge variant="outline" className="text-[0.6rem] px-1 py-0 h-4 bg-vista-blue/20 text-vista-blue border-vista-blue/30">
                                Host
                              </Badge>
                            )}
                          </div>
                        )}

                        <div className="flex flex-col space-y-1">
                          {group.messages.map((message, msgIndex) => (
                            <div
                              key={message.id}
                              className={`max-w-[240px] rounded-lg px-3 py-2 ${
                                group.userId === userId
                                  ? 'bg-vista-blue text-white rounded-tr-none'
                                  : 'bg-vista-dark-tertiary text-vista-light rounded-tl-none'
                              }`}
                            >
                              <p>{message.content}</p>
                              {msgIndex === group.messages.length - 1 && (
                                <p className="text-xs opacity-70 text-right mt-1 flex items-center justify-end">
                                  <Clock className="w-3 h-3 mr-1 opacity-70" />
                                  {formatTime(message.timestamp)}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </>
          )}

          {/* Typing indicators */}
          <AnimatePresence>
            {Object.keys(typingUsers).length > 0 && (
              <motion.div
                className="flex items-center text-vista-light/50 text-xs"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
              >
                <div className="typing-animation mr-2">
                  <span className="dot"></span>
                  <span className="dot"></span>
                  <span className="dot"></span>
                </div>
                {Object.keys(typingUsers).length === 1 ? (
                  <p>{typingUsers[Object.keys(typingUsers)[0]].username} is typing...</p>
                ) : (
                  <p>Multiple people are typing...</p>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          <div ref={chatEndRef} />
        </div>
      </ScrollArea>

      {/* Quick reactions */}
      <div className="px-3 pt-2 flex justify-center space-x-2">
        {["👍", "❤️", "😂", "😮", "👀"].map(emoji => (
          <Button
            key={emoji}
            variant="ghost"
            size="sm"
            className="p-1 h-8 w-8 rounded-full hover:bg-vista-dark/40"
            onClick={() => sendReaction(emoji)}
          >
            <span className="text-lg">{emoji}</span>
          </Button>
        ))}
      </div>

      {/* Input area */}
      <div className="p-3 border-t border-vista-dark-border">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            sendMessage();
          }}
          className="flex gap-2 items-center"
        >
          <Popover open={isEmojiPickerOpen} onOpenChange={setIsEmojiPickerOpen}>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-full"
              >
                <Smile className="h-5 w-5 text-vista-light/70" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 border-vista-dark-border" side="top">
              <Picker
                data={data}
                onEmojiSelect={handleEmojiSelect}
                theme="dark"
                previewPosition="none"
                skinTonePosition="none"
              />
            </PopoverContent>
          </Popover>

          <div className="relative flex-1">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={handleInputChange}
              placeholder="Type your message..."
              className="flex-1 bg-vista-dark-tertiary border-vista-dark-border pr-10"
              disabled={isLoading}
            />
            {isLoading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
            )}
          </div>

          <Button
            type="submit"
            disabled={!newMessage.trim() || isLoading}
            className="bg-vista-blue hover:bg-vista-blue-light text-white"
            size="icon"
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>

      {/* Scroll to bottom button */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.div
            className="absolute bottom-16 right-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              onClick={scrollToBottom}
              size="icon"
              className="h-8 w-8 rounded-full bg-vista-blue text-white shadow-lg"
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* CSS for typing animation */}
      <style jsx global>{`
        .typing-animation {
          display: flex;
          align-items: center;
        }

        .dot {
          display: inline-block;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          margin-right: 3px;
          background: #9ca3af;
          animation: wave 1.3s linear infinite;
        }

        .dot:nth-child(2) {
          animation-delay: -1.1s;
        }

        .dot:nth-child(3) {
          animation-delay: -0.9s;
        }

        @keyframes wave {
          0%, 60%, 100% {
            transform: initial;
          }
          30% {
            transform: translateY(-3px);
          }
        }
      `}</style>
    </div>
  );
}