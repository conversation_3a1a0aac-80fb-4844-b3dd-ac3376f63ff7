"use client"

import { useState, useEffect } from 'react'
import { pusherClient } from '@/lib/pusher-client'

type ConnectionStatus = 'connected' | 'connecting' | 'unavailable' | 'failed' | 'disconnected'

interface PusherConnectionState {
  status: ConnectionStatus
  reconnectIn: number | null
  connectionCount: number
  lastConnected: Date | null
}

export function usePusher() {
  const [connectionState, setConnectionState] = useState<PusherConnectionState>({
    status: 'connecting',
    reconnectIn: null,
    connectionCount: 0,
    lastConnected: null
  })
  
  useEffect(() => {
    // Track connection attempts
    let reconnectInterval: NodeJS.Timeout | null = null
    let connectionCounter = 0
    
    function updateReconnectTimer() {
      if (reconnectInterval) clearInterval(reconnectInterval)
      
      let reconnectSeconds = 30
      setConnectionState(prev => ({
        ...prev,
        reconnectIn: reconnectSeconds
      }))
      
      reconnectInterval = setInterval(() => {
        reconnectSeconds -= 1
        setConnectionState(prev => ({
          ...prev,
          reconnectIn: reconnectSeconds > 0 ? reconnectSeconds : null
        }))
        
        if (reconnectSeconds <= 0 && reconnectInterval) {
          clearInterval(reconnectInterval)
          reconnectInterval = null
        }
      }, 1000)
    }
    
    try {
      // Check if pusherClient is the real Pusher instance (not the fallback)
      if (!pusherClient || !('connection' in pusherClient)) {
        console.warn('[usePusher] Pusher not properly initialized')
        setConnectionState({
          status: 'unavailable',
          reconnectIn: null,
          connectionCount: 0,
          lastConnected: null
        })
        return () => {}
      }
      
      // Use Pusher's built-in connection state instead of creating channels
      const connection = pusherClient.connection
      
      const handleConnected = () => {
        connectionCounter += 1
        
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
          reconnectInterval = null
        }
        
        setConnectionState({
          status: 'connected',
          reconnectIn: null,
          connectionCount: connectionCounter,
          lastConnected: new Date()
        })
        console.log(`[usePusher] Connected to Pusher (attempt ${connectionCounter})`)
      }
      
      const handleDisconnected = () => {
        setConnectionState(prev => ({
          ...prev,
          status: 'disconnected'
        }))
        updateReconnectTimer()
        console.log('[usePusher] Disconnected from Pusher')
      }
      
      const handleUnavailable = () => {
        setConnectionState(prev => ({
          ...prev,
          status: 'unavailable'
        }))
        updateReconnectTimer()
        console.log('[usePusher] Pusher connection unavailable')
      }
      
      const handleFailed = () => {
        setConnectionState(prev => ({
          ...prev,
          status: 'failed'
        }))
        updateReconnectTimer()
        console.log('[usePusher] Pusher connection failed')
      }
      
      const handleConnecting = () => {
        setConnectionState(prev => ({
          ...prev,
          status: 'connecting'
        }))
        console.log('[usePusher] Connecting to Pusher...')
      }
      
      // Bind to Pusher's connection events
      connection.bind('connected', handleConnected)
      connection.bind('disconnected', handleDisconnected)
      connection.bind('unavailable', handleUnavailable)
      connection.bind('failed', handleFailed)
      connection.bind('connecting', handleConnecting)
      
      // Set initial state based on current connection
      const currentState = connection.state
      if (currentState === 'connected') {
        handleConnected()
      } else if (currentState === 'connecting') {
        handleConnecting()
      } else if (currentState === 'disconnected') {
        handleDisconnected()
      } else if (currentState === 'unavailable') {
        handleUnavailable()
      } else if (currentState === 'failed') {
        handleFailed()
      }
      
      // Cleanup function
      return () => {
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
        }
        
        try {
          connection.unbind('connected', handleConnected)
          connection.unbind('disconnected', handleDisconnected)
          connection.unbind('unavailable', handleUnavailable)
          connection.unbind('failed', handleFailed)
          connection.unbind('connecting', handleConnecting)
          console.log('[usePusher] Cleaned up connection event listeners')
        } catch (error) {
          console.error('Error cleaning up Pusher connection listeners:', error)
        }
      }
    } catch (error) {
      // If we can't access the connection, mark as failed
      console.error('Pusher connection error:', error)
      setConnectionState({
        status: 'failed',
        reconnectIn: null,
        connectionCount: 0,
        lastConnected: null
      })
      
      // Try to reconnect
      updateReconnectTimer()
      
      // Return empty cleanup function
      return () => {
        if (reconnectInterval) {
          clearInterval(reconnectInterval)
        }
      }
    }
  }, [])
  
  return connectionState
} 