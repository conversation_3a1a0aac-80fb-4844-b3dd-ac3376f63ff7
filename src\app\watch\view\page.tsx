import { redirect } from 'next/navigation';

// This is a server component that handles the /watch/view route
export default function ViewPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Extract the query parameters
  const id = searchParams.id as string;
  const idType = searchParams.idType as string;
  const type = searchParams.type as string;
  const title = searchParams.title as string;
  const contentId = searchParams.contentId as string;
  const season = searchParams.season as string;
  const episode = searchParams.episode as string;
  
  // Force the page to be dynamic
  console.log('View page parameters:', { id, idType, type, title, contentId, season, episode });
  
  // Redirect to the player page with forcePlay=true to ensure playback
  // Use the URLSearchParams to reconstruct the query string
  const params = new URLSearchParams();
  
  if (id) params.append('id', id);
  if (idType) params.append('idType', idType);
  if (type) params.append('type', type);
  if (title) params.append('title', title);
  if (contentId) params.append('contentId', contentId);
  if (season) params.append('season', season);
  if (episode) params.append('episode', episode);
  
  // Add forcePlay flag to ensure playback
  params.append('forcePlay', 'true');
  
  // Redirect to the main watch page with all the parameters
  return redirect(`/watch?${params.toString()}`);
} 