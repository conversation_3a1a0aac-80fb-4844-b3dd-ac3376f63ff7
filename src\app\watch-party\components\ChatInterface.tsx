'use client'

import { useState, useRef, useEffect } from 'react'
import { MessageCircle, Send, Smile, ImageIcon, PlusCircle, Loader2, Hash } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ChatMessage } from '@/lib/WatchPartyContext'
import { Separator } from '@/components/ui/separator'
import { motion, AnimatePresence } from 'framer-motion'

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  onSendReaction?: (emoji: string) => void;
  currentUserId: string;
  className?: string;
}

// Common emoji reactions
const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '👏', '🔥', '🎬', '⏱️', '🍿'];

export default function ChatInterface({
  messages,
  onSendMessage,
  onSendReaction,
  currentUserId,
  className = '',
}: ChatInterfaceProps) {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle message submit
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!message.trim()) return;

    try {
      setIsSending(true);
      await onSendMessage(message);
      setMessage('');

      // Focus the input again after sending
      if (inputRef.current) {
        inputRef.current.focus();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSending(false);
    }
  };

  // Handle emoji reaction click
  const handleEmojiClick = async (emoji: string) => {
    if (onSendReaction) {
      try {
        await onSendReaction(emoji);
      } catch (error) {
        console.error('Failed to send reaction:', error);
      }
    } else {
      // If no reaction handler, just send as a message
      setMessage(emoji);
      handleSubmit();
    }
  };

  // Group messages by time (within 2 minutes)
  const groupedMessages = messages.reduce((groups: ChatMessage[][], message) => {
    const lastGroup = groups[groups.length - 1];

    if (!lastGroup) {
      return [[message]];
    }

    const lastMessage = lastGroup[lastGroup.length - 1];
    const timeDiff = new Date(message.timestamp).getTime() - new Date(lastMessage.timestamp).getTime();
    const isSameSender = lastMessage.memberId === message.memberId;

    // Group messages if they're from the same sender and within 2 minutes
    if (isSameSender && timeDiff < 120000) {
      lastGroup.push(message);
    } else {
      groups.push([message]);
    }

    return groups;
  }, []);

  // Format timestamp to show time only
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Detect if a message is from today or show full date
  const formatMessageDate = (timestamp: string) => {
    const messageDate = new Date(timestamp);
    const today = new Date();

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    }

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }

    return messageDate.toLocaleDateString();
  };

  return (
    <div className={`flex flex-col h-full wp-glass-card rounded-xl overflow-hidden shadow-lg border border-white/10 ${className}`}>
      <div className="bg-gradient-to-r from-slate-800/80 to-slate-900/80 p-4 border-b border-white/10 flex items-center justify-between">
        <div className="flex items-center">
          <div className="bg-indigo-500/20 p-1.5 rounded-lg mr-3">
            <MessageCircle className="w-4 h-4 text-indigo-400" />
          </div>
          <h3 className="text-sm font-medium text-white">Party Chat</h3>
        </div>
        <Badge variant="outline" className="bg-black/30 text-indigo-300 border-indigo-500/30 px-2.5">
          {messages.length}
        </Badge>
      </div>

      <ScrollArea className="flex-1 wp-scrollbar" ref={scrollRef}>
        <AnimatePresence>
          <div className="p-4 space-y-6">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="bg-indigo-500/10 p-4 rounded-full mb-4">
                  <MessageCircle className="w-10 h-10 text-indigo-400/60" />
                </div>
                <h3 className="text-lg font-medium text-slate-300 mb-1">No Messages Yet</h3>
                <p className="text-slate-400 text-sm max-w-xs">
                  Start the conversation with your watch party friends or react with an emoji
                </p>
              </div>
            ) : (
              groupedMessages.map((group, groupIndex) => {
                const firstMessage = group[0];
                const isCurrentUser = firstMessage.memberId === currentUserId;
                const isSystem = firstMessage.type === 'system';

                return (
                  <div key={`group-${groupIndex}`} className="space-y-1">
                    {(groupIndex === 0 ||
                     formatMessageDate(firstMessage.timestamp) !==
                     formatMessageDate(groupedMessages[groupIndex - 1][0].timestamp)) && (
                      <div className="flex items-center justify-center my-4">
                        <div className="bg-indigo-500/20 text-indigo-300 text-xs px-3 py-1.5 rounded-full font-medium shadow-sm border border-indigo-500/20">
                          {formatMessageDate(firstMessage.timestamp)}
                        </div>
                      </div>
                    )}

                    {isSystem ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex justify-center my-3"
                      >
                        <Badge variant="outline" className="bg-black/30 text-slate-300 text-xs py-1.5 px-3 border-white/10 shadow-sm">
                          {firstMessage.content}
                        </Badge>
                      </motion.div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`flex items-start gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}
                      >
                        {!isCurrentUser && (
                          <Avatar className="w-9 h-9 border-2 border-white/10 shadow-md">
                            <AvatarImage src={`https://avatar.vercel.sh/${firstMessage.memberId}.png`} />
                            <AvatarFallback className="bg-gradient-to-br from-indigo-500 to-purple-600 text-white">
                              {firstMessage.memberName[0]}
                            </AvatarFallback>
                          </Avatar>
                        )}

                        <div className={`flex flex-col gap-1.5 max-w-[80%] ${isCurrentUser ? 'items-end' : 'items-start'}`}>
                          {!isCurrentUser && (
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium text-white">{firstMessage.memberName}</span>
                              <span className="text-xs text-slate-400">{formatTime(firstMessage.timestamp)}</span>
                            </div>
                          )}

                          {group.map((msg, i) => (
                            <div
                              key={msg.id}
                              className={`
                                py-2.5 px-3.5 rounded-lg max-w-full shadow-sm
                                ${isCurrentUser
                                  ? 'bg-gradient-to-r from-indigo-600 to-indigo-500 text-white border border-indigo-400/20'
                                  : 'bg-black/30 text-white border border-white/10'
                                }
                                ${i === 0 ? (isCurrentUser ? 'rounded-tr-none' : 'rounded-tl-none') : ''}
                                ${i === group.length - 1 ? '' : (isCurrentUser ? 'rounded-br-none' : 'rounded-bl-none')}
                              `}
                            >
                              <p className="text-sm break-words">{msg.content}</p>
                            </div>
                          ))}

                          {isCurrentUser && (
                            <span className="text-xs text-slate-400 mt-1">{formatTime(firstMessage.timestamp)}</span>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </AnimatePresence>
      </ScrollArea>

      <div className="p-4 border-t border-white/10 bg-black/20">
        <form onSubmit={handleSubmit} className="flex items-center gap-3">
          {onSendReaction && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/20">
                  <Smile className="h-5 w-5" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-3 bg-slate-900 border border-white/10 rounded-xl" align="start" alignOffset={-40}>
                <div className="flex flex-wrap gap-2.5">
                  {commonEmojis.map(emoji => (
                    <button
                      key={emoji}
                      onClick={() => handleEmojiClick(emoji)}
                      className="text-xl hover:bg-indigo-500/20 p-2 rounded-lg cursor-pointer transition-colors"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          )}

          <div className="relative flex-1">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="bg-black/30 border-white/10 text-white rounded-full py-6 pl-4 pr-12 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              autoComplete="off"
            />
            <Button
              type="submit"
              size="icon"
              disabled={!message.trim() || isSending}
              className="h-8 w-8 bg-gradient-to-r from-indigo-600 to-indigo-500 hover:from-indigo-500 hover:to-indigo-400 rounded-full absolute right-2 top-1/2 -translate-y-1/2 shadow-md"
            >
              {isSending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}