'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Users, Info, ChevronRight, Play } from 'lucide-react';
import { useLanguage } from '@/lib/i18n/LanguageContext';

export default function WatchPartyPrompt() {
  const { t } = useLanguage();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <motion.div
          className="relative overflow-hidden rounded-xl bg-vista-dark-lighter"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Background image with gradient overlay */}
          <div className="relative aspect-[21/9] md:aspect-[3/1] w-full overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1517604931442-7e0c8ed2963c?q=80&w=2070"
              alt="Friends watching movie together"
              fill
              className={`object-cover transition-transform duration-700 ${isHovered ? 'scale-105' : 'scale-100'}`}
            />

            <div className="absolute inset-0 bg-gradient-to-r from-vista-dark via-vista-dark/80 to-transparent z-10" />
          </div>

          {/* Content overlay */}
          <div className="absolute inset-0 z-20 flex flex-col justify-center p-6 md:p-12 lg:max-w-3xl">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-vista-light mb-3">
              Watch Together with Friends
            </h2>

            <p className="text-vista-light/80 text-base md:text-lg mb-6 max-w-2xl">
              Our Watch Party feature lets you stream content with friends in perfect sync. Chat in real-time and share reactions while watching your favorite movies and shows together.
            </p>

            <div className="flex flex-wrap gap-4">
              <Link href="/watch-party">
                <Button className="bg-vista-blue hover:bg-vista-blue/90 gap-2">
                  <Users className="w-5 h-5" />
                  Start Watch Party
                </Button>
              </Link>

              <Link href="/watch-party">
                <Button variant="outline" className="bg-white text-black hover:bg-white/90 gap-2 border-none">
                  <Info className="w-5 h-5" />
                  Learn How It Works
                </Button>
              </Link>
            </div>

            <div className="mt-6">
              <Link href="/watch-party" className="flex items-center text-vista-blue hover:text-vista-blue/80 group">
                <span className="text-sm">Try It Now</span>
                <motion.div
                  animate={{ x: isHovered ? 5 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                </motion.div>
              </Link>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute bottom-6 right-6 z-10 hidden md:block">
            <motion.div
              className="flex items-center justify-center w-16 h-16 rounded-full bg-vista-dark/70 text-vista-light"
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.8, 1, 0.8]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              <Play className="w-6 h-6 ml-1" />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}