'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Users, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { getContentById } from '@/data/content'
import { useWatchParty } from '@/app/watch-party/hook-connector'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface WatchPartyContentProps {
  contentId: string;
}

export default function WatchPartyContent({ contentId }: WatchPartyContentProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  
  const { currentParty } = useWatchParty()
  
  // Redirect to the dedicated watch party page
  useEffect(() => {
    if (currentParty) {
      // Navigate to dedicated watch party page
      router.push(`/watch-party/${currentParty.id}`)
    } else {
      // If no party, redirect to watch party home
      router.push('/watch-party')
    }
  }, [currentParty, router])
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-vista-dark">
      <div className="text-vista-light text-lg">Redirecting to watch party...</div>
    </div>
  )
} 