import {
  getLatestMoviesUrl,
  getLatestShowsUrl,
  getLatestEpisodesUrl,
  VIDSRC_DOMAINS
} from '@/lib/vidsrc-api';
import {
  getPopularMovies,
  getPopularTVShows,
  getTVShowsOnTheAir,
  getTrendingDaily,
  MappedContent
} from '@/lib/tmdb-api';

// Fallback data for when all APIs fail - using TMDB data instead of mock data
async function getFallbackData(contentType: string) {
  try {
    console.log('Using TMDB fallback data for', contentType);
    switch (contentType) {
      case 'movies':
        return await getPopularMovies();
      case 'shows':
        return await getPopularTVShows();
      case 'episodes':
        return await getTVShowsOnTheAir();
      default:
        return [];
    }
  } catch (error) {
    console.error('Error getting fallback data:', error);
    return [];
  }
}

// Check if we're using the export output
const isStaticExport = process.env.NEXT_PHASE === 'phase-production-build' &&
                      process.env.NODE_ENV === 'production';

/**
 * Fetch content data from TMDb/VidSrc or use mock data
 * This is a client-side function that can be used in place of API routes
 * when using static export
 *
 * @param contentType The type of content to fetch ('movies', 'shows', 'episodes')
 * @param page The page number to fetch
 * @param options Additional options for fetching
 * @returns An array of content items
 */
export async function fetchContentData(contentType: string, page: number = 1, options?: {
  useTmdb?: boolean;
  forceDomain?: string;
  useMockData?: boolean;
}) {
  const {
    useTmdb = true,
    forceDomain,
    useMockData = false
  } = options || {};

  // For static export or if mock data is requested, use fallback data from TMDB
  if (isStaticExport || useMockData) {
    console.log('Using fallback data for', contentType);
    return await getFallbackData(contentType);
  }

  // Try to use TMDb if enabled
  if (useTmdb) {
    try {
      console.log(`Using TMDb API for ${contentType}`);

      let results: MappedContent[] = [];

      switch (contentType) {
        case 'movies':
          results = await getPopularMovies(page);
          break;
        case 'shows':
          results = await getPopularTVShows(page);
          break;
        case 'episodes':
          // Episodes are a bit special - we'll get on-air TV shows and treat them as episodes
          results = await getTVShowsOnTheAir(page);

          // Add fake season/episode data for compatibility
          results = results.map((item, index) => ({
            ...item,
            season: 1,
            episode: index + 1,
            showTitle: item.title
          }));
          break;
        default:
          throw new Error('Invalid content type');
      }

      // Transform TMDb results to match expected format for VidSrc client
      const transformedResults = results.map(item => {
        const baseItem = {
          id: item.tmdbId,
          tmdb_id: item.tmdbId,
          title: item.title,
          year: item.year?.toString(),
          poster: item.posterUrl,
          added: item.releaseDate,
          // Including VidSrc embed URLs for direct use
          embed_url_tmdb: `https://vidsrc.xyz/embed/${contentType === 'movies' ? 'movie' : 'tv'}?tmdb=${item.tmdbId}`
        };

        // Add episode-specific fields if needed
        if (contentType === 'episodes') {
          return {
            ...baseItem,
            season: '1',
            episode: '1',
            show_title: item.title
          };
        }

        return baseItem;
      });

      return transformedResults;
    } catch (tmdbError) {
      console.error('Error fetching from TMDb:', tmdbError);
      // Fall back to VidSrc if TMDb fails
    }
  }

  // If TMDb fails or is disabled, try VidSrc API
  // Try each domain until one works
  const errors = [];

  // If a domain is forced, only try that one
  const domainsToTry = forceDomain
    ? [forceDomain]
    : VIDSRC_DOMAINS;

  for (const domain of domainsToTry) {
    try {
      console.log(`Trying VidSrc domain: ${domain} for ${contentType}`);

      // Determine the appropriate URL based on content type
      let url: string;

      switch (contentType) {
        case 'movies':
          url = getLatestMoviesUrl(page, domain);
          break;
        case 'shows':
          url = getLatestShowsUrl(page, domain);
          break;
        case 'episodes':
          url = getLatestEpisodesUrl(page, domain);
          break;
        default:
          throw new Error('Invalid content type');
      }

      console.log(`Fetching from URL: ${url}`);

      // Fetch data from VidSrc API with a timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed with status: ${response.status} ${response.statusText}`);
      }

      // Try to parse the response
      const data = await response.json();

      // If we get here, the request was successful
      console.log(`Successfully fetched data from ${domain}. Response type:`, typeof data);

      // Check if the data is an object with a specific structure (common in APIs)
      // VidSrc often returns { response: [...], pages: number } format
      let responseArray = data;

      if (!Array.isArray(data) && typeof data === 'object' && data !== null) {
        console.log('Received object response, checking for array properties...');
        // Try to find an array in the response
        const possibleArrayKeys = ['results', 'data', 'items', 'response', 'content', 'movies', 'shows', 'episodes'];

        for (const key of possibleArrayKeys) {
          if (Array.isArray(data[key])) {
            console.log(`Found array in response at key: ${key}, length: ${data[key].length}`);
            responseArray = data[key];
            break;
          }
        }

        // If we still don't have an array, extract all items
        if (!Array.isArray(responseArray)) {
          // Convert object values to an array if they look like content items
          const extractedItems = Object.values(data).filter(item =>
            typeof item === 'object' &&
            item !== null &&
            ('id' in item || 'title' in item || 'imdb_id' in item)
          );

          if (extractedItems.length > 0) {
            console.log(`Extracted ${extractedItems.length} items from object response`);
            responseArray = extractedItems;
          }
        }
      }

      // Return the data if it's an array
      if (Array.isArray(responseArray)) {
        console.log(`Final processed data is an array with ${responseArray.length} items`);
        if (responseArray.length > 0) {
          console.log('Sample item:', responseArray[0]);
        }
        return responseArray;
      } else {
        console.log('Unable to extract array from response:', data);
        // Return the original data and let the client handle it
        return data;
      }
    } catch (domainError) {
      // Log the error and continue to the next domain
      const errorMessage = domainError instanceof Error ? domainError.message : String(domainError);
      errors.push(`${domain}: ${errorMessage}`);
      console.error(`Error with domain ${domain}:`, errorMessage);
      // Continue to the next domain
    }
  }

  // If we get here, all domains failed
  console.error('All data sources failed:', errors);

  // Try to get fallback data from TMDB
  console.log('Attempting to get fallback data from TMDB');
  return await getFallbackData(contentType);
}