'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft, Info, AlertCircle, Users, Copy } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import VidSrcPlayer from '@/components/VidSrcPlayer'
import WatchContentClient from './watch-content-client'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { IContent } from '@/data/content'
import { useToast } from '@/components/ui/use-toast'
import { WatchPartyToast } from '@/components/WatchPartyToast'

interface DirectWatchClientProps {
  contentId: string;
}

export default function DirectWatchClient({ contentId }: DirectWatchClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [connectionAttempted, setConnectionAttempted] = useState(false)
  const [partyStatus, setPartyStatus] = useState<'connecting' | 'connected' | 'error' | null>(null)
  const [notificationShown, setNotificationShown] = useState(false)

  // Check if watch party notification has been shown in this session
  const [watchPartyNotificationShownInSession, setWatchPartyNotificationShownInSession] = useState(false)

  // Initialize session storage check on component mount
  useEffect(() => {
    // Check if notification has been shown in this session
    const hasShownNotification = sessionStorage.getItem('watchPartyNotificationShown')
    if (hasShownNotification === 'true') {
      setWatchPartyNotificationShownInSession(true)
    }
  }, [])

  // Get mode and other parameters from searchParams - client side
  const mode = searchParams.get('mode') === 'party' ? 'party' : 'direct'
  const partyId = searchParams.get('partyId')
  const isHost = searchParams.get('isHost') === 'true'
  const forcePlay = searchParams.get('forcePlay') === 'true'
  const showDetails = searchParams.get('showDetails') === 'true'

  // If we're in direct mode (not party), just use the WatchContentClient
  if (mode === 'direct') {
    return <WatchContentClient
      contentId={contentId}
      initialForcePlay={forcePlay}
      initialShowDetails={showDetails}
    />
  }

  // Check if we need to redirect for party setup
  useEffect(() => {
    // Only run once to prevent loops
    if (connectionAttempted) {
      return;
    }

    // If we're in party mode but missing a party ID, redirect to watch parties home
    if (mode === 'party' && !partyId) {
      console.log('[DirectWatchClient] Missing party ID in party mode, redirecting to watch parties home');
      router.push('/watch-party');
      return;
    }

    // Check if user is intentionally exiting the watch party
    const exitingWatchParty = searchParams.get('exiting') === 'true';
    if (exitingWatchParty) {
      console.log('[DirectWatchClient] User is exiting watch party, stopping redirection');
      setConnectionAttempted(true);
      return;
    }

    // If we're in party mode with a party ID, check if we need to connect to the party
    if (mode === 'party' && partyId) {
      // Try to connect to the party and get its status
      const connectToParty = async () => {
        try {
          setConnectionAttempted(true);
          setPartyStatus('connecting');

          // Get username from local storage or generate a random one
          const username = localStorage.getItem('watchPartyUserName') || `Guest-${Math.floor(Math.random() * 10000)}`;

          // Generate a consistent user ID or retrieve from storage
          const userId = localStorage.getItem('watchPartyUserId') || `user-${Math.random().toString(36).substring(2, 9)}`;
          // Store it for future use
          if (!localStorage.getItem('watchPartyUserId')) {
            localStorage.setItem('watchPartyUserId', userId);
          }

          // Call the API to join or check the party
          const response = await fetch('/api/watch-party', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              event: 'join-watch-party',
              data: {
                partyId,
                memberId: userId,
                memberName: username
              }
            }),
          });

          if (!response.ok) {
            console.error(`[DirectWatchClient] Party ${partyId} not found or join failed`);
            setPartyStatus('error');

            // Only show notification if not already shown
            if (!notificationShown) {
              setNotificationShown(true);
              toast({
                title: "Watch Party Not Found",
                description: "The watch party couldn't be found. You'll be returned to the watch party page.",
                variant: "destructive",
                // Use custom toast component
                component: (
                  <WatchPartyToast
                    title="Watch Party Not Found"
                    description="The watch party couldn't be found. You'll be returned to the watch party page."
                    variant="error"
                  />
                )
              });
            }

            // Show error for a moment, then redirect
            setTimeout(() => {
              router.push('/watch-party');
            }, 2000);
            return;
          }

          // Party exists, set connected status
          setPartyStatus('connected');
          console.log(`[DirectWatchClient] Successfully connected to party ${partyId}`);

          // Success toast - only if not already shown in this session
          if (!watchPartyNotificationShownInSession && partyStatus !== 'connected') {
            // Mark as shown in this session
            setWatchPartyNotificationShownInSession(true);
            sessionStorage.setItem('watchPartyNotificationShown', 'true');

            // Set local notification state
            setNotificationShown(true);

            toast({
              title: "Watch Party Connected",
              description: "You are now watching together with others in this party.",
              // Use custom toast component
              component: (
                <WatchPartyToast
                  title="Watch Party Connected"
                  description="You are now watching together with others in this party."
                  variant="success"
                />
              )
            });

            // Reset notification flag after a delay
            setTimeout(() => setNotificationShown(false), 5000);
          }
        } catch (error) {
          console.error('[DirectWatchClient] Error connecting to party:', error);
          setPartyStatus('error');

          // Error toast - only if not already shown
          if (!notificationShown) {
            setNotificationShown(true);
            toast({
              title: "Connection Error",
              description: "There was a problem connecting to the watch party. You can continue watching solo.",
              variant: "destructive",
              // Use custom toast component
              component: (
                <WatchPartyToast
                  title="Connection Error"
                  description="There was a problem connecting to the watch party. You can continue watching solo."
                  variant="error"
                />
              )
            });
          }
        }
      };

      connectToParty();
    }
  }, [mode, partyId, isHost, router, connectionAttempted, toast, notificationShown, searchParams]);

  // Pass watch party info to the WatchContentClient
  return <WatchContentClient
    contentId={contentId}
    initialForcePlay={true} // Force play in party mode
    initialShowDetails={false} // Don't show details in party mode
    partyMode={{
      isPartyMode: searchParams.get('exiting') === 'true' ? false : true,
      partyId: partyId || '',
      isHost: isHost,
      connectionStatus: partyStatus
    }}
    // Add content type info from URL parameters
    // Log the content type for debugging
    contentType={(() => {
      // Ensure we have a valid content type
      const type = searchParams.get('contentType') || (searchParams.get('season') ? 'show' : 'movie');
      console.log(`[DirectWatchClient] Using content type: ${type} for ID: ${contentId}`);
      // Return a default value if type is somehow undefined (shouldn't happen, but just in case)
      return type || 'movie';
    })()}
  />
}