'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BannerAd {
  _id: string;
  title: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  styling: {
    backgroundColor: string;
    textColor: string;
    titleSize: string;
    descriptionSize: string;
    borderRadius: string;
    padding: string;
    animation: 'none' | 'fadeIn' | 'slideIn' | 'bounce' | 'pulse';
    animationDuration: string;
    positions: ('top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections')[];
  };
  priority: number;
}

interface BannerDisplayProps {
  position?: 'top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections';
  className?: string;
}

export default function BannerDisplay({ position = 'top', className = '' }: BannerDisplayProps) {
  const [banners, setBanners] = useState<BannerAd[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [loading, setLoading] = useState(true);
  const [dismissedBanners, setDismissedBanners] = useState<Set<string>>(new Set());

  // Fetch active banners
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        const response = await fetch('/api/banner-ads/active');
        if (response.ok) {
          const data = await response.json();
          // Filter banners by position and exclude dismissed ones
          const filteredBanners = data.banners.filter(
            (banner: BannerAd) =>
              banner.styling.positions?.includes(position) &&
              !dismissedBanners.has(banner._id)
          );
          setBanners(filteredBanners);
        }
      } catch (error) {
        console.error('Error fetching banners:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, [position, dismissedBanners]);

  // Cycle through banners if multiple exist
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 8000); // Change banner every 8 seconds

      return () => clearInterval(interval);
    }
  }, [banners.length]);

  // Track banner view
  const trackView = async (bannerId: string) => {
    try {
      await fetch(`/api/admin/banner-ads/${bannerId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'view' }),
      });
    } catch (error) {
      console.error('Error tracking banner view:', error);
    }
  };

  // Track banner click
  const trackClick = async (bannerId: string) => {
    try {
      await fetch(`/api/admin/banner-ads/${bannerId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'click' }),
      });
    } catch (error) {
      console.error('Error tracking banner click:', error);
    }
  };

  // Handle banner click
  const handleBannerClick = (banner: BannerAd) => {
    trackClick(banner._id);
    if (banner.linkUrl) {
      window.open(banner.linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Handle banner dismiss
  const handleDismiss = (bannerId: string) => {
    setDismissedBanners(prev => new Set([...prev, bannerId]));
    setBanners(prev => prev.filter(banner => banner._id !== bannerId));
  };

  // Track view when banner becomes visible
  useEffect(() => {
    if (banners.length > 0 && isVisible) {
      const currentBanner = banners[currentBannerIndex];
      if (currentBanner) {
        trackView(currentBanner._id);
      }
    }
  }, [banners, currentBannerIndex, isVisible]);

  if (loading || banners.length === 0 || !isVisible) {
    return null;
  }

  const currentBanner = banners[currentBannerIndex];

  // Animation variants
  const getAnimationVariants = (animation: string) => {
    switch (animation) {
      case 'fadeIn':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
      case 'slideIn':
        return {
          initial: { x: position === 'top' ? -100 : 100, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: position === 'top' ? 100 : -100, opacity: 0 }
        };
      case 'bounce':
        return {
          initial: { scale: 0.8, opacity: 0 },
          animate: { scale: 1, opacity: 1, transition: { type: 'spring', bounce: 0.4 } },
          exit: { scale: 0.8, opacity: 0 }
        };
      case 'pulse':
        return {
          initial: { scale: 0.95, opacity: 0 },
          animate: { 
            scale: 1, 
            opacity: 1,
            transition: { 
              scale: { repeat: Infinity, repeatType: 'reverse', duration: 2 },
              opacity: { duration: 0.5 }
            }
          },
          exit: { scale: 0.95, opacity: 0 }
        };
      default:
        return {
          initial: {},
          animate: {},
          exit: {}
        };
    }
  };

  const animationVariants = getAnimationVariants(currentBanner.styling.animation);
  const animationDuration = parseFloat(currentBanner.styling.animationDuration);

  // Special styling for hero overlay position
  const isHeroOverlay = position === 'hero-overlay';
  const overlayStyles = isHeroOverlay ? {
    backgroundColor: `${currentBanner.styling.backgroundColor}CC`, // Add transparency
    backdropFilter: 'blur(8px)',
    border: `1px solid ${currentBanner.styling.textColor}20`
  } : {
    backgroundColor: currentBanner.styling.backgroundColor,
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`${currentBanner._id}-${currentBannerIndex}`}
        className={`relative w-full ${className} ${isHeroOverlay ? 'backdrop-blur-sm' : ''}`}
        initial={animationVariants.initial}
        animate={animationVariants.animate}
        exit={animationVariants.exit}
        transition={{ duration: animationDuration }}
        style={{
          ...overlayStyles,
          borderRadius: currentBanner.styling.borderRadius,
          padding: currentBanner.styling.padding,
        }}
      >
        {/* Close button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 z-10 h-8 w-8 p-0 hover:bg-black/20"
          onClick={() => handleDismiss(currentBanner._id)}
        >
          <X className="h-4 w-4" style={{ color: currentBanner.styling.textColor }} />
        </Button>

        {/* Banner content */}
        <div
          className={`flex items-center gap-4 ${currentBanner.linkUrl ? 'cursor-pointer' : ''}`}
          onClick={() => currentBanner.linkUrl && handleBannerClick(currentBanner)}
        >
          {/* Banner image */}
          <div className="relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded overflow-hidden">
            <Image
              src={currentBanner.imageUrl}
              alt={currentBanner.title}
              fill
              className="object-cover"
              sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, 96px"
              onError={(e) => {
                console.error('Banner image failed to load:', currentBanner.imageUrl);
                console.error('Banner data:', currentBanner);
              }}
              onLoad={() => {
                console.log('Banner image loaded successfully:', currentBanner.imageUrl);
              }}
              unoptimized={true}
            />
          </div>

          {/* Banner text */}
          <div className="flex-1 min-w-0">
            <h3
              className="font-semibold truncate"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.titleSize,
              }}
            >
              {currentBanner.title}
            </h3>
            {currentBanner.description && (
              <p
                className="mt-1 opacity-90 line-clamp-2"
                style={{
                  color: currentBanner.styling.textColor,
                  fontSize: currentBanner.styling.descriptionSize,
                }}
              >
                {currentBanner.description}
              </p>
            )}
          </div>

          {/* Link indicator */}
          {currentBanner.linkUrl && (
            <div className="flex-shrink-0">
              <div
                className="text-sm font-medium px-3 py-1 rounded border opacity-80 hover:opacity-100 transition-opacity"
                style={{
                  color: currentBanner.styling.textColor,
                  borderColor: currentBanner.styling.textColor,
                }}
              >
                Learn More →
              </div>
            </div>
          )}
        </div>

        {/* Multiple banners indicator */}
        {banners.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {banners.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-opacity ${
                  index === currentBannerIndex ? 'opacity-100' : 'opacity-40'
                }`}
                style={{ backgroundColor: currentBanner.styling.textColor }}
              />
            ))}
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}
