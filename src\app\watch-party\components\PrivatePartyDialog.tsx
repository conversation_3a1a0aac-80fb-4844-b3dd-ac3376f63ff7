'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Lock, Key } from 'lucide-react'

interface PrivatePartyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onJoin: (partyId: string, partyCode: string) => void
  partyName: string
  partyId: string
  isLoading: boolean
}

export function PrivatePartyDialog({
  open,
  onOpenChange,
  onJoin,
  partyName,
  partyId,
  isLoading
}: PrivatePartyDialogProps) {
  const [partyCode, setPartyCode] = useState('')
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (partyCode.trim()) {
      onJoin(partyId, partyCode)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onO<PERSON><PERSON>hange}>
      <DialogContent className="bg-vista-dark border-vista-light/10 sm:max-w-md">
        <div className="absolute top-0 right-0 left-0 h-1.5 bg-gradient-to-r from-blue-500/50 via-blue-500 to-blue-600 rounded-t-lg" />
        
        <DialogHeader className="pt-6">
          <div className="mx-auto bg-blue-500/10 p-3 rounded-full mb-4 border border-blue-500/20">
            <Lock className="h-6 w-6 text-blue-500" />
          </div>
          <DialogTitle className="text-xl text-center">Private Watch Party</DialogTitle>
          <DialogDescription className="text-center text-vista-light/70">
            "{partyName}" is a private watch party. Please enter the party code to join.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4 py-4">
            <div className="relative">
              <Input
                id="party-code"
                value={partyCode}
                onChange={(e) => setPartyCode(e.target.value)}
                placeholder="Enter party code"
                className="bg-black/30 border-vista-light/10 text-white pl-10"
                autoFocus
              />
              <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-vista-light/50" />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-vista-light/10 text-vista-light hover:bg-vista-dark-lighter"
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white"
              disabled={!partyCode.trim() || isLoading}
            >
              {isLoading ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Joining...
                </>
              ) : (
                'Join Party'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
