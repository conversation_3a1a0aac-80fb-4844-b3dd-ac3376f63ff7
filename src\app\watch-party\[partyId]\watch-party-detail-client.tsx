"use client"

import { useEffect, useState, useR<PERSON>, use<PERSON><PERSON><PERSON>, useMemo } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { ChevronLeft, Play, MessageSquare, Users, Send, RefreshCw, Copy, Clock, UserCircle, Shield, InfoIcon, Loader2, AlertTriangle, Smile } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useWatchPartyWithPusher, Chat<PERSON>essage, WatchPartyState, IContent } from "@/hooks/useWatchPartyWithPusher"
import { getContentById } from "@/data/content"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { ensureContentImagePaths } from "@/utils/image-utils"
import WatchPartyChat from "@/components/WatchPartyChat"
import dynamic from "next/dynamic"

// Dynamically import Emoji Picker to avoid SSR issues
const Picker = dynamic(() => import("@emoji-mart/react").then((mod) => mod.default), {
  ssr: false,
})

export function WatchPartyDetailClient({ partyId }: { partyId: string }) {
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()
  const messageInputRef = useRef<HTMLInputElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [message, setMessage] = useState("")
  const [content, setContent] = useState<IContent | null>(null)
  const [activeTab, setActiveTab] = useState<string>("chat")
  const [selectedDomain, setSelectedDomain] = useState<string>("vidsrc")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [data, setData] = useState([])
  const [chatContainerRef, setChatContainerRef] = useState(null)
  const [bottomRef, setBottomRef] = useState(null)
  const [messageText, setMessageText] = useState("")
  const [emojiData, setEmojiData] = useState<Record<string, unknown>>({})

  // Check URL parameters
  const isUrlHost = searchParams.get('isHost') === 'true'
  const partyName = searchParams.get('partyName') || undefined
  const directStart = searchParams.get('directStart') === 'true'

  // Use the real watch party hook instead of the mock one
  const {
    currentParty: party,
    isLoading: loading,
    error: hookError,
    sendMessage,
    joinParty,
    leaveParty,
    updatePlayback,
    userId,
    userName,
    setUserName,
    isHost: hookIsHost,
    deleteParty
  } = useWatchPartyWithPusher()

  // Combine host status from both URL and hook
  const isHost = useMemo(() => isUrlHost || hookIsHost, [isUrlHost, hookIsHost])

  // Use a key to track redirections to prevent looping
  const [isRedirectingToPlayer, setIsRedirectingToPlayer] = useState(false)

  // Track notifications to prevent spam
  const [notificationShown, setNotificationShown] = useState(false)

  // Define startWatching function early to avoid dependency issues
  const startWatching = useCallback(async () => {
    if (!content?.id && !party?.contentId) {
      if (!notificationShown) {
        setNotificationShown(true);
        toast({
          title: "Content not found",
          description: "We couldn't find the content for this watch party.",
          variant: "destructive"
        });
      }
      return;
    }

    // Prevent multiple redirects
    if (isRedirectingToPlayer) {
      console.log('[WatchParty] Ignoring start request - already redirecting');
      return;
    }

    // Immediately set redirecting flag to prevent double-clicks
    setIsRedirectingToPlayer(true);

    // Show loading toast for all users
    if (!notificationShown) {
      setNotificationShown(true);
      toast({
        title: party?.startedAt ? "Resuming watch party" : "Starting watch party",
        description: "Please wait while we prepare the viewing experience...",
      });
    }

    // Use content ID from party data if available as fallback
    const contentToUse = content || {
      id: party?.contentId?.toString() || "",
      type: party?.content?.type || "movie"
    };

    // Log details about the party we're joining
    console.log('[WatchParty] Starting/resuming watch party:', {
      partyId,
      contentId: contentToUse.id,
      contentType: contentToUse.type,
      isPartyStarted: !!party?.startedAt,
      isHost
    });

    // When starting as host, update the party state to mark it as started
    if (isHost && party && !party.startedAt) {
      try {
        // Show loading toast - only if not already shown
        if (!notificationShown) {
          setNotificationShown(true);
          toast({
            title: "Starting watch party",
            description: "Please wait while we prepare the viewing experience...",
          });
        }

        // Create a loading overlay to indicate processing
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'watch-party-loading-overlay';
        loadingOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]';
        loadingOverlay.innerHTML = `
          <div class="text-vista-light text-center">
            <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-xl font-medium">Starting Watch Party</h3>
            <p class="text-vista-light/70">Please wait...</p>
          </div>
        `;
        document.body.appendChild(loadingOverlay);

        // Update party state to indicate it's started
        // This will help late joiners to know they should go straight to the player
        console.log('[WatchParty] Updating party status to started...');
        const response = await fetch('/api/watch-party', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            event: 'update-watch-party',
            data: {
              partyId: partyId,
              updates: {
                startedAt: new Date().toISOString(),
                status: 'active'
              }
            }
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update party status');
        }

        // Parse the response to verify the update was successful
        const responseData = await response.json();
        console.log('[WatchParty] Party update response:', responseData);

        // Wait a longer time to ensure the update is processed before redirecting
        // This is critical to ensure the server has time to process and broadcast the update
        console.log('[WatchParty] Waiting for update to propagate...');
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Update the loading overlay to indicate we're now redirecting
        if (loadingOverlay) {
          loadingOverlay.innerHTML = `
            <div class="text-vista-light text-center">
              <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 class="text-xl font-medium">Redirecting to Player</h3>
              <p class="text-vista-light/70">Please wait...</p>
            </div>
          `;
        }

        console.log('[WatchParty] Party marked as started, redirecting to player...');
      } catch (error) {
        console.error('[WatchParty] Error updating party status:', error);
        // Show error toast
        toast({
          title: "Error starting party",
          description: "There was an issue starting the watch party. Please try again.",
          variant: "destructive"
        });
        // Reset redirecting state to allow another attempt
        setIsRedirectingToPlayer(false);
        return; // Don't continue with redirect if there was an error
      }
    } else if (party?.startedAt) {
      // Party is already started, we're resuming
      console.log('[WatchParty] Resuming existing watch party session...');

      if (!notificationShown) {
        setNotificationShown(true);
        toast({
          title: "Resuming watch party",
          description: "Rejoining the watch party in progress...",
        });
      }
    }

    try {
      // Construct the watch URL with all necessary parameters
      let watchUrl = `/watch/${contentToUse.id}?mode=party&partyId=${partyId}&isHost=${isHost}`;

      // Always include content type to ensure correct player is loaded
      watchUrl += `&contentType=${contentToUse.type}`;

      // Add season and episode parameters for TV shows
      if (contentToUse.type === 'show') {
        // Get season and episode from the party data, with strong fallbacks
        // Explicitly check for undefined/null to avoid using 0 as a fallback
        const season = (party?.currentSeason !== undefined && party?.currentSeason !== null)
            ? party.currentSeason
            : (searchParams.get('season') ? parseInt(searchParams.get('season')!) : 1);

        const episode = (party?.currentEpisode !== undefined && party?.currentEpisode !== null)
            ? party.currentEpisode
            : (searchParams.get('episode') ? parseInt(searchParams.get('episode')!) : 1);

        // Debug log the values to help with troubleshooting
        console.log(`[WatchParty] Using TV show parameters - Season: ${season}, Episode: ${episode}`);
        console.log(`[WatchParty] Source: ${party?.currentSeason ? 'party object' : 'default values'}`);

        // Add the parameters to the URL
        watchUrl += `&season=${season}&episode=${episode}`;
      }

      console.log('[WatchParty] Redirecting to player:', watchUrl);

      // Add a loading overlay to prevent the flash (if not already added)
      document.body.classList.add('navigation-in-progress');

      // Use a small delay before navigation to ensure the loading overlay is visible
      // and any pending state updates are completed
      setTimeout(() => {
        // Use window.location.replace instead of href to prevent history entry
        // This helps avoid the back button taking users back to the party page
        // which could cause a refresh loop
        window.location.replace(watchUrl);
      }, 300);
    } catch (navigateError) {
      console.error('[WatchParty] Navigation error:', navigateError);

      // Fallback to window.location if router.push fails
      let fallbackUrl = `/watch/${contentToUse.id}?mode=party&partyId=${partyId}&isHost=${isHost}`;

      // Always include content type in fallback URL as well
      fallbackUrl += `&contentType=${contentToUse.type}`;

      // Add season and episode parameters for TV shows in the fallback URL as well
      if (contentToUse.type === 'show') {
        // Same logic as above to ensure consistency between both navigation methods
        const season = (party?.currentSeason !== undefined && party?.currentSeason !== null)
            ? party.currentSeason
            : (searchParams.get('season') ? parseInt(searchParams.get('season')!) : 1);

        const episode = (party?.currentEpisode !== undefined && party?.currentEpisode !== null)
            ? party.currentEpisode
            : (searchParams.get('episode') ? parseInt(searchParams.get('episode')!) : 1);

        fallbackUrl += `&season=${season}&episode=${episode}`;
      }

      // Add a loading overlay to prevent the flash (if not already added)
      document.body.classList.add('navigation-in-progress');

      // Log the fallback attempt
      console.log('[WatchParty] Fallback navigation to:', fallbackUrl);

      // Use a small delay before navigation to ensure the loading overlay is visible
      setTimeout(() => {
        // Use window.location.replace instead of href to prevent history entry
        window.location.replace(fallbackUrl);
      }, 300);
    }
  }, [content, party, isHost, isRedirectingToPlayer, notificationShown, partyId, searchParams, toast]);

  // Join the party when component mounts
  useEffect(() => {
    if (isDeleting) {
      console.log('[WatchParty Detail] Deletion in progress, skipping initParty.');
      return;
    }

    if (!partyId) return;

    // Handle automatic party joining
    const initParty = async () => {
      try {
        // Safeguard: Check hook's error state first
        if (hookError && hookError.toLowerCase().includes('not found')) {
          console.warn(`[WatchParty Detail] Skipping join attempt for ${partyId} as hook already reported 'not found'.`);
          setError(`Party with ID "${partyId}" was not found. It may have expired or been deleted.`); // Set local error state
          setIsLoading(false);
          return; // Don't proceed with join attempt
        }

        setIsLoading(true);

        // Check if user is coming from creation page (isHost could be either a boolean from the hook or a string from URL params)
        const isCreatorJoin = hookIsHost || isUrlHost;

        console.log(`[WatchParty Detail] Initializing party ${partyId}, isHost: ${isCreatorJoin}, directStart: ${directStart}`);

        if (isCreatorJoin) {
          // If user is the creator, verify the party exists first in case of storage issues
          try {
            // Try to fetch all parties first to ensure our party is in the system
            const response = await fetch('/api/watch-party', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ event: 'get-watch-parties' }),
            });

            if (response.ok) {
              const data = await response.json();
              const partyExists = data.parties?.some((p: { id: string }) => p.id === partyId);

              if (!partyExists) {
                console.error(`[WatchParty Detail] Party ${partyId} not found in initial check`);
                setError(`The party was created but cannot be found. This may be due to a server issue.`);
                return;
              }
            }
          } catch (checkError) {
            console.warn('[WatchParty Detail] Error checking party existence:', checkError);
            // Continue anyway and try to join - the join will fail if the party truly doesn't exist
          }
        }

        // Generate a consistent user ID or retrieve from storage
        let memberId = localStorage.getItem('watchPartyUserId');
        if (!memberId) {
          memberId = `user-${Math.random().toString(36).substring(2, 9)}`;
          localStorage.setItem('watchPartyUserId', memberId);
        }

        // Join the party - the hook will use the userId from localStorage
        await joinParty(partyId);

        // Once joined, update tab state based on query params
        if (activeTab === 'chat' || activeTab === 'members') {
          setActiveTab(activeTab);
        }

        setIsLoading(false);

        // If directStart is true, automatically start the watch party
        if (directStart && isHost) {
          console.log('[WatchParty Detail] Direct start requested, automatically starting watch party');
          // Small delay to ensure the party is fully loaded
          setTimeout(() => {
            // Redirect directly to the watch page
            const contentId = party?.contentId;
            if (contentId) {
              // Determine content type - default to movie if unknown
              const contentType = party.content?.type || (party.currentSeason !== undefined ? 'show' : 'movie');

              // Include content type in URL to ensure correct playback
              const watchUrl = `/watch/${contentId}?mode=party&partyId=${partyId}&isHost=${isHost}&contentType=${contentType}`;

              console.log('[WatchParty] Redirecting to player with URL:', watchUrl);

              // Add a loading overlay to prevent the flash (if not already added)
              document.body.classList.add('navigation-in-progress');

              // Use a small delay before navigation to ensure the loading overlay is visible
              setTimeout(() => {
                // Use window.location.replace instead of href to prevent history entry
                window.location.replace(watchUrl);
              }, 300);
            }
          }, 500);
        }
      } catch (error) {
        console.error('[WatchParty Detail] Error initializing party:', error);
        setIsLoading(false);

        // Custom error message for party not found
        if (error instanceof Error && error.message.includes('not found')) {
          setError(`Party with ID "${partyId}" was not found. It may have expired or been deleted.`);
        } else {
          setError(error instanceof Error ? error.message : 'Failed to join watch party');
        }
      }
    };

    initParty();
  }, [joinParty, partyId, userName, activeTab, hookIsHost, isUrlHost, directStart, isHost, isDeleting, hookError, party?.contentId, party?.content?.type, party?.currentSeason]);

  // Load content details
  useEffect(() => {
    if (party?.contentId) {
      try {
        const contentId = typeof party.contentId === 'string' ? party.contentId : String(party.contentId);

        // First try to get content from local cache
        const contentDetails = getContentById(contentId);

        if (contentDetails) {
          // Use local content with fixed image paths
          setContent(ensureContentImagePaths({
            id: contentId,
            title: contentDetails.title,
            posterPath: contentDetails.posterPath || "",
            backdropPath: contentDetails.backdropPath || "",
            type: contentDetails.type,
            overview: contentDetails.overview || "",
            year: contentDetails.year || "",
            rating: contentDetails.rating || 0,
            genres: contentDetails.genres || []
          }));
        } else if (party.content) {
          // Fallback to using content directly from party data
          setContent(ensureContentImagePaths(party.content));
        } else {
          // If we still don't have content details, try to fetch from API
          console.log('[WatchParty] No local content found, attempting to fetch from API:', contentId);

          // Determine content type - default to movie if unknown
          const contentType = party.content?.type ||
                             (party.currentSeason !== undefined ? 'show' : 'movie');

          // Attempt to fetch from our content API
          fetch(`/api/content?id=${contentId}&type=${contentType}`)
            .then(response => {
              if (!response.ok) {
                throw new Error(`API error ${response.status}`);
              }
              return response.json();
            })
            .then(apiContent => {
              console.log('[WatchParty] Successfully fetched content from API:', apiContent.title);
              setContent(ensureContentImagePaths({
                id: contentId,
                title: apiContent.title,
                posterPath: apiContent.posterPath || "",
                backdropPath: apiContent.backdropPath || "",
                type: apiContent.type || contentType,
                overview: apiContent.overview || "",
                year: apiContent.year || "",
                rating: apiContent.rating || 0,
                genres: apiContent.genres || []
              }));
            })
            .catch(apiError => {
              console.error('[WatchParty] Error fetching content from API:', apiError);
              // Set minimal content info to allow party to function
              setContent({
                id: contentId,
                title: party.title || "Unknown Content",
                posterPath: "",
                backdropPath: "",
                type: contentType as "movie" | "show",
                overview: "",
                year: "",
                rating: 0,
                genres: []
              });
            });
        }

        // Check if user is trying to exit the watch party
        const isExitingParty = searchParams.get('exiting') === 'true';

        // If the party already started and user is not trying to exit, redirect to watch
        if (party.startedAt && !isRedirectingToPlayer && !isExitingParty) {
          console.log('[WatchParty] Party already started, redirecting to player...');
          setIsRedirectingToPlayer(true);

          const contentId = party.contentId;
          if (contentId) {
            // Determine content type - default to movie if unknown
            const contentType = party.content?.type ||
                              (party.currentSeason !== undefined ? 'show' : 'movie');

            // Include content type in URL to ensure correct playback
            const watchUrl = `/watch/${contentId}?mode=party&partyId=${partyId}&isHost=${isHost}&contentType=${contentType}`;

            console.log('[WatchParty] Redirecting to player with URL:', watchUrl);

            // Add a loading overlay to prevent the flash (if not already added)
            document.body.classList.add('navigation-in-progress');

            // Create a loading overlay to indicate processing
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'watch-party-loading-overlay';
            loadingOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]';
            loadingOverlay.innerHTML = `
              <div class="text-vista-light text-center">
                <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 class="text-xl font-medium">Joining Watch Party</h3>
                <p class="text-vista-light/70">Please wait...</p>
              </div>
            `;
            document.body.appendChild(loadingOverlay);

            // Use a small delay before navigation
            setTimeout(() => {
              // Use window.location.replace instead of href to prevent history entry
              // This avoids issues with Next.js router sometimes causing a refresh
              window.location.replace(watchUrl);
            }, 500);
          }
        }
      } catch (err) {
        console.error("Error loading content details:", err);
      }
    }
  }, [party?.contentId, party?.content, party?.startedAt, party?.title, party?.currentSeason, partyId, isHost, router, isRedirectingToPlayer, searchParams, hookError]);

  // Scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current && party?.messages?.length) {
      const scrollTimer = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

      return () => clearTimeout(scrollTimer);
    }
  }, [party?.messages])

  // Generate embed URL based on content information
  const generateEmbedUrl = useCallback(() => {
    if (!content?.id) return ""

    // Convert content type to format expected by embed providers
    const contentType = content.type === 'show' ? 'tv' : 'movie';

    // Different domains for embedding
    const domains: Record<string, string> = {
      vidsrc: `https://vidsrc.to/embed/${contentType}/${content.id}`,
      superembed: `https://multiembed.mov/directstream.php?video_id=${content.id}&${contentType === "tv" ? "tv" : "movie"}=1`,
      netembed: `https://netembed.com/movie/play?id=${content.id}`
    }

    return domains[selectedDomain] || domains.vidsrc
  }, [content, selectedDomain])

  // Handle loading state and navigation overlay
  useEffect(() => {
    // When content is loaded, remove loading state and navigation overlay
    if (!loading && party) {
      setIsLoading(false);

      // Remove any loading overlays
      document.body.classList.remove('navigation-in-progress');

      // Remove any manually created loading overlays
      const loadingOverlay = document.getElementById('watch-party-loading-overlay');
      if (loadingOverlay && loadingOverlay.parentNode) {
        loadingOverlay.parentNode.removeChild(loadingOverlay);
      }

      // Also remove any other loading overlays that might have been created
      const otherLoadingOverlays = document.querySelectorAll('.fixed.inset-0.bg-vista-dark.flex.items-center.justify-center');
      otherLoadingOverlays.forEach(overlay => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      });
    }
  }, [loading, party]);

  // Redirect if no party is found after loading
  useEffect(() => {
    if (!loading && !party && error) {
      // Log the error for debugging
      console.error("Party not found error:", error);

      // Show toast and redirect to the watch party home
      toast({
        title: "Watch Party Not Available",
        description: "Returning to watch party list.",
        variant: "destructive"
      });

      // Immediately redirect to avoid any potential hook issues
      router.push("/watch-party");
    }
  }, [party, loading, error, router, toast]);

  // Handle message submission
  const handleSendMessage = useCallback((e: React.FormEvent) => {
    e.preventDefault(); // Prevent form submission from refreshing the page
    if (message.trim()) {
      try {
        // Call sendMessage and handle the promise properly
        sendMessage(message.trim())
          .then(() => {
            console.log("Message sent successfully");
            // Clear the input field and focus it again only after successful send
            setMessage("");
            messageInputRef.current?.focus();
          })
          .catch(err => {
            console.error("Error sending message:", err);
            toast({
              title: "Failed to send message",
              description: "Your message could not be sent. Please try again.",
              variant: "destructive"
            });
          });
      } catch (error) {
        // Handle any synchronous errors
        console.error("Synchronous error sending message:", error);
        toast({
          title: "Failed to send message",
          description: "Your message could not be sent. Please try again.",
          variant: "destructive"
        });
      }
    }
  }, [message, sendMessage, toast]);

  // Handle tab change
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  // Handle refresh player
  const handleRefreshPlayer = useCallback(() => {
    // Reload content data
    if (party?.contentId) {
      const contentDetails = getContentById(party.contentId.toString());
      if (contentDetails) {
        setContent({
          id: party.contentId.toString(),
          title: contentDetails.title,
          posterPath: contentDetails.posterPath || "",
          backdropPath: contentDetails.backdropPath || "",
          type: contentDetails.type,
          overview: contentDetails.overview || "",
          year: contentDetails.year || "",
          rating: contentDetails.rating || 0,
          genres: contentDetails.genres || []
        });

        toast({
          title: "Content refreshed",
          description: "Content information has been updated."
        });
      }
    }
  }, [party?.contentId, toast, setContent]);

  // Copy party ID to clipboard
  const copyPartyId = useCallback(async () => {
    try {
      // Use modern Clipboard API
      await navigator.clipboard.writeText(partyId);

      // Only show notification if not already shown
      if (!notificationShown) {
        setNotificationShown(true);
        toast({
          title: "Party ID copied",
          description: "Share this ID with friends to invite them to the watch party."
        });
        // Reset notification flag after a delay to allow future notifications
        setTimeout(() => setNotificationShown(false), 1000);
      }
    } catch (err) {
      // Fallback for browsers with restricted clipboard access
      try {
        // Create a temporary input element
        const textArea = document.createElement("textarea");
        textArea.value = partyId;
        textArea.style.position = "fixed";  // Avoid scrolling to bottom
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // Use a more modern approach with Clipboard API if available
        const successful = document.execCommand('copy');

        if (successful) {
          // Only show notification if not already shown
          if (!notificationShown) {
            setNotificationShown(true);
            toast({
              title: "Party ID copied",
              description: "Share this ID with friends to invite them to the watch party."
            });
            // Reset notification flag after a delay to allow future notifications
            setTimeout(() => setNotificationShown(false), 1000);
          }
        } else {
          throw new Error("Copy command was unsuccessful");
        }

        // Clean up
        document.body.removeChild(textArea);
      } catch (fallbackErr) {
        // Both methods failed, show error
        toast({
          title: "Copy failed",
          description: "Please copy the party ID manually: " + partyId,
          variant: "destructive"
        });
      }
    }
  }, [partyId, toast, notificationShown]);

  // Formatted creation time
  const formattedCreationTime = useMemo(() => {
    if (!party?.createdAt) return '';

    try {
      const date = new Date(party.createdAt);
      return date.toLocaleString();
    } catch (e) {
      return '';
    }
  }, [party?.createdAt]);

  // Find the host information
  const hostMember = useMemo(() => {
    if (!party?.members) return null;
    return party.members.find(member => member.isHost);
  }, [party?.members]);

  // Fetch emoji data
  useEffect(() => {
    const fetchEmojiData = async () => {
      try {
        const response = await fetch('https://cdn.jsdelivr.net/npm/@emoji-mart/data')
        const data = await response.json()
        setEmojiData(data)
      } catch (error) {
        console.error('Failed to load emoji data:', error)
      }
    }

    fetchEmojiData()
  }, [])

  // Render members list
  const renderMembersList = party ? (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {party.members.map((member) => (
        <div
          key={member.id}
          className="flex items-center gap-3 p-3 bg-gradient-to-r from-black/40 to-blue-900/10 border border-blue-500/10 rounded-lg"
        >
          <Avatar className="h-10 w-10 border-2 border-black/60">
            <AvatarImage
              src={member.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${member.name}`}
              alt={member.name}
            />
            <AvatarFallback className={member.isHost
              ? "bg-blue-600 text-white"
              : "bg-black/40 text-vista-light"}>
              {member.name[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium text-white truncate">
                {member.name}
              </span>
              {member.isHost && (
                <Badge className="bg-blue-600 text-white text-xs px-2 py-0">Host</Badge>
              )}
              {member.id === userId && (
                <Badge className="bg-vista-blue/20 text-vista-blue/90 border-vista-blue/30 text-xs px-2 py-0">You</Badge>
              )}
            </div>
            <p className="text-xs text-vista-light/70">
              Joined {formattedCreationTime}
            </p>
          </div>
          <div className="flex-shrink-0">
            <div className="h-2.5 w-2.5 rounded-full bg-green-500"></div>
          </div>
        </div>
      ))}
    </div>
  ) : null;

  // Render chat messages
  const renderChatMessages = party ? (
    <>
      <div className="flex-1 overflow-y-auto p-4" ref={chatContainerRef}>
        {party.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="bg-vista-blue/10 p-4 rounded-full mb-4">
              <MessageSquare className="h-8 w-8 text-vista-blue/70" />
            </div>
            <h3 className="text-white font-medium mb-2">No messages yet</h3>
            <p className="text-vista-light/70 max-w-sm">
              Be the first to send a message in this watch party!
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {party.messages.map((message, index) => {
              const isSelf = message.memberId === userId;
              const member = party.members.find((m) => m.id === message.memberId);

              return (
                <div
                  key={index}
                  className={`flex ${isSelf ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex max-w-[80%] ${isSelf ? 'flex-row-reverse' : 'flex-row'} items-end gap-2`}>
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarImage
                        src={member?.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${member?.name || 'User'}`}
                        alt={member?.name || 'User'}
                      />
                      <AvatarFallback className={member?.isHost
                        ? "bg-blue-600 text-white"
                        : "bg-black/40 text-vista-light"}>
                        {member?.name[0].toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>

                    <div className={`rounded-xl py-2 px-3 ${
                      isSelf
                        ? "bg-vista-blue text-white"
                        : "bg-black/30 border border-blue-500/10 text-vista-light"
                    }`}>
                      {!isSelf && (
                        <div className="mb-1 flex items-center gap-1">
                          <span className="font-medium text-sm">
                            {member?.name || 'Unknown user'}
                          </span>
                          {member?.isHost && (
                            <Badge className="ml-1 bg-blue-800/40 text-blue-300 text-[10px] px-1.5 py-0">Host</Badge>
                          )}
                        </div>
                      )}
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      <div className={`text-[10px] mt-1 ${isSelf ? 'text-blue-200/70' : 'text-vista-light/50'}`}>
                        {new Date(message.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={bottomRef} />
          </div>
        )}
      </div>

      <div className="border-t border-blue-500/10 bg-black/20 p-3">
        <form
          onSubmit={(e) => {
            e.preventDefault(); // Explicitly prevent form submission
            handleSendMessage(e);
            return false; // Extra safety to prevent default behavior
          }}
          className="flex space-x-2"
        >
          <div className="relative flex-1">
            <Input
              ref={messageInputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message..."
              className="border-blue-500/20 bg-black/30 text-white focus-visible:ring-vista-blue placeholder:text-vista-light/50 pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 text-vista-light/70 hover:text-vista-light hover:bg-transparent"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <Smile className="h-5 w-5" />
            </Button>
            {showEmojiPicker && (
              <div className="absolute bottom-full right-0 mb-2 z-10">
                <Picker
                  data={emojiData}
                  onEmojiSelect={(emoji: { native: string }) => {
                    setMessage((prev) => prev + emoji.native);
                    setShowEmojiPicker(false);
                  }}
                  theme="dark"
                />
              </div>
            )}
          </div>
          <Button
            type="button" // Changed from submit to button
            className="bg-vista-blue hover:bg-vista-blue/90 text-white"
            disabled={message.trim() === ''}
            onClick={(e) => {
              e.preventDefault();
              handleSendMessage(e);
            }}
          >
            <Send className="h-5 w-5" />
          </Button>
        </form>
      </div>
    </>
  ) : null;

  // Function to end the party (host only)
  const endParty = useCallback(async () => {
    if (!party || !isHost) return;

    // Create a full-screen loading overlay FIRST before any operations
    // This ensures it's visible throughout the entire process
    const loadingOverlay = document.createElement('div')
    loadingOverlay.id = 'watch-party-loading-overlay'
    loadingOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]'
    loadingOverlay.innerHTML = `
      <div class="text-vista-light text-center">
        <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
        <h3 class="text-xl font-medium">Ending Watch Party</h3>
        <p class="text-vista-light/70">Please wait...</p>
      </div>
    `
    document.body.appendChild(loadingOverlay)

    // Set state after overlay is created
    setIsDeleting(true);

    console.log(`[WatchParty] Host ending party: ${partyId}`);

    try {
      // Show a toast to indicate we're ending the party
      toast({
        title: "Ending watch party",
        description: "Please wait while we clean up..."
      });

      // Use the deleteParty function from the hook
      const success = await deleteParty(partyId, () => {
        // onSuccess callback: Navigate away after successful deletion
        console.log(`[WatchParty Detail] Party ${partyId} deleted successfully, navigating to /watch-party`);

        // Update the loading overlay text to indicate we're returning to the watch party list
        const loadingOverlay = document.getElementById('watch-party-loading-overlay');
        if (loadingOverlay) {
          loadingOverlay.innerHTML = `
            <div class="text-vista-light text-center">
              <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 class="text-xl font-medium">Returning to Watch Parties</h3>
              <p class="text-vista-light/70">Please wait...</p>
            </div>
          `;
        }

        // DO NOT remove the loading overlay - let the WatchPartyHome component handle it
        // This ensures a continuous loading state with no flash

        // Add preventRejoin flag to avoid the error
        router.push(`/watch-party?refresh=true&preventRejoin=${partyId}`);
      });

      if (success) {
        // The onSuccess callback handles navigation, toast can be shown here or in the callback
        toast({
          title: "Watch party ended",
          description: "The watch party has been successfully ended."
        });
      } else {
        // Error occurred, the hook should have set an error state or thrown
        // We might not reach here if it throws, but handle just in case
        toast({
          title: "Error ending party",
          description: hookError || "There was a problem ending the watch party.",
          variant: "destructive"
        });

        // Update the loading overlay text to indicate we're returning to the watch party list
        const loadingOverlay = document.getElementById('watch-party-loading-overlay');
        if (loadingOverlay) {
          loadingOverlay.innerHTML = `
            <div class="text-vista-light text-center">
              <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 class="text-xl font-medium">Returning to Watch Parties</h3>
              <p class="text-vista-light/70">Please wait...</p>
            </div>
          `;
        } else {
          // If for some reason the overlay doesn't exist, create a new one
          const newOverlay = document.createElement('div');
          newOverlay.id = 'watch-party-loading-overlay';
          newOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]';
          newOverlay.innerHTML = `
            <div class="text-vista-light text-center">
              <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 class="text-xl font-medium">Returning to Watch Parties</h3>
              <p class="text-vista-light/70">Please wait...</p>
            </div>
          `;
          document.body.appendChild(newOverlay);
        }

        // DO NOT remove the loading overlay - let the WatchPartyHome component handle it
        // This ensures a continuous loading state with no flash

        // Still try to navigate away with the preventRejoin flag
        router.push(`/watch-party?refresh=true&preventRejoin=${partyId}`);
      }

      return success;

    } catch (error) {
      // Catch errors thrown by the hook's deleteParty
      console.error(`[WatchParty] Error ending party:`, error);
      toast({
        title: "Error ending party",
        description: error instanceof Error ? error.message : "There was a problem ending the watch party.",
        variant: "destructive"
      });

      // Update the loading overlay text to indicate we're returning to the watch party list
      const loadingOverlay = document.getElementById('watch-party-loading-overlay');
      if (loadingOverlay) {
        loadingOverlay.innerHTML = `
          <div class="text-vista-light text-center">
            <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-xl font-medium">Returning to Watch Parties</h3>
            <p class="text-vista-light/70">Please wait...</p>
          </div>
        `;
      } else {
        // If for some reason the overlay doesn't exist, create a new one
        const newOverlay = document.createElement('div');
        newOverlay.id = 'watch-party-loading-overlay';
        newOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]';
        newOverlay.innerHTML = `
          <div class="text-vista-light text-center">
            <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 class="text-xl font-medium">Returning to Watch Parties</h3>
            <p class="text-vista-light/70">Please wait...</p>
          </div>
        `;
        document.body.appendChild(newOverlay);
      }

      // DO NOT remove the loading overlay - let the WatchPartyHome component handle it
      // This ensures a continuous loading state with no flash

      // Still try to navigate away with the preventRejoin flag
      router.push(`/watch-party?refresh=true&preventRejoin=${partyId}`);
      return false;
    }
  }, [partyId, party, isHost, toast, deleteParty, router, hookError]);

  // Function to leave the party without ending it
  const leaveAndExitParty = useCallback(async () => {
    console.log(`[WatchParty] Leaving party: ${partyId}`);

    // Mark this party as exiting to prevent auto-redirects
    const url = new URL(window.location.href);
    url.searchParams.set('exiting', 'true');
    window.history.replaceState({}, '', url.toString());

    // Clear notification flags
    setNotificationShown(false);
    setIsRedirectingToPlayer(false);

    try {
      // Show a toast to indicate we're leaving
      toast({
        title: "Leaving watch party",
        description: "Please wait while we clean up..."
      });

      // Add a loading overlay to prevent the flash
      document.body.classList.add('navigation-in-progress');

      // For non-host users, leave the party
      if (party && !isHost) {
        try {
          await leaveParty(); // This is the hook's leaveParty function
          console.log(`[WatchParty] Successfully left party ${partyId}`);
        } catch (leaveError) {
          // Just log the error but continue with navigation
          console.warn(`[WatchParty] Error leaving party, but continuing with navigation:`, leaveError);
        }
      } else {
        // For hosts, we don't need to leave - just navigate away
        console.log(`[WatchParty] Host exiting party without leaving: ${partyId}`);
      }

      // Wait a moment to ensure any requests are processed
      await new Promise(resolve => setTimeout(resolve, 300));

      // Show success toast
      toast({
        title: "Left watch party",
        description: "You have successfully left the watch party."
      });

      // Remove the navigation overlay to ensure the page is interactive
      document.body.classList.remove('navigation-in-progress');

      // Redirect back to the dashboard with a special flag to prevent rejoining
      router.push('/watch-party?refresh=true&preventRejoin=' + partyId);
    } catch (error) {
      console.error(`[WatchParty] Error leaving party:`, error);

      // Show error toast
      toast({
        title: "Error leaving party",
        description: "There was a problem leaving the watch party.",
        variant: "destructive"
      });

      // Remove the navigation overlay to ensure the page is interactive
      document.body.classList.remove('navigation-in-progress');

      // Still try to redirect back to the dashboard with the prevent rejoin flag
      router.push('/watch-party?refresh=true&preventRejoin=' + partyId);
    }
  }, [partyId, router, party, toast, isHost, leaveParty]);

  // Function to exit the party and return to the dashboard
  const exitParty = useCallback(async () => {
    console.log(`[WatchParty] Exiting party: ${partyId}`);

    // Mark this party as exiting to prevent auto-redirects
    const url = new URL(window.location.href);
    url.searchParams.set('exiting', 'true');
    window.history.replaceState({}, '', url.toString());

    // Clear notification flags
    setNotificationShown(false);
    setIsRedirectingToPlayer(false);

    // Simply redirect back to the dashboard without leaving the party
    router.push('/watch-party?refresh=true');
  }, [partyId, router]);

  // Main UI rendering
  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-8 w-20" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Skeleton className="h-[300px] w-full rounded-xl" />
          </div>
          <div>
            <Skeleton className="h-[300px] w-full rounded-xl" />
          </div>
        </div>
      </div>
    )
  }

  if (error || !party) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/watch-party">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back to Parties
            </Link>
          </Button>
        </div>

        <div className="bg-vista-dark-lighter rounded-xl p-6 text-center">
          <h2 className="text-xl font-medium mb-3">Watch Party Not Found</h2>
          <p className="text-vista-light/70 mb-6">
            {error || "The watch party you're looking for doesn't exist or has ended."}
          </p>
          <Button asChild>
            <Link href="/watch-party">
              Browse Available Parties
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container-fluid py-6 px-4 md:px-8 text-white" style={{
      background: "linear-gradient(135deg, rgba(13,20,36,0.7) 0%, rgba(3,14,43,0.7) 100%)",
      backdropFilter: "blur(20px)",
      minHeight: "calc(100vh - 64px)"
    }}>
      {/* Pusher banner removed */}

      <div className="max-w-[1400px] mx-auto">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" size="icon" className="rounded-full h-10 w-10 border-vista-light/20 bg-black/30 hover:bg-black/40" asChild>
            <Link href="/watch-party" onClick={() => {
              // Remove the navigation overlay before navigating
              document.body.classList.remove('navigation-in-progress');

              // Remove any manually created loading overlays
              const loadingOverlay = document.getElementById('watch-party-loading-overlay');
              if (loadingOverlay && loadingOverlay.parentNode) {
                loadingOverlay.parentNode.removeChild(loadingOverlay);
              }

              // Also remove any other loading overlays that might have been created
              const otherLoadingOverlays = document.querySelectorAll('.fixed.inset-0.bg-vista-dark.flex.items-center.justify-center');
              otherLoadingOverlays.forEach(overlay => {
                if (overlay.parentNode) {
                  overlay.parentNode.removeChild(overlay);
                }
              });
            }}>
              <ChevronLeft className="h-5 w-5" />
            </Link>
          </Button>
        </div>

        {isLoading && (
          <div className="flex flex-col items-center justify-center min-h-[600px]">
            <div className="w-20 h-20 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin mb-6"></div>
            <h3 className="text-xl font-medium text-white mb-2">Loading Watch Party</h3>
            <p className="text-vista-light/70">Please wait while we initialize your party experience...</p>
          </div>
        )}

        {error && !isLoading && (
          <div className="bg-black/30 backdrop-blur-sm border border-vista-light/10 rounded-xl py-16 px-8 text-center">
            <div className="flex flex-col items-center max-w-lg mx-auto">
              <div className="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center mb-6">
                <AlertTriangle className="h-8 w-8 text-red-500/70" />
              </div>
              <h3 className="text-xl font-medium text-white mb-3">Watch Party Not Available</h3>
              <p className="text-vista-light/70 mb-8 max-w-md mx-auto">
                {error || "The watch party you're looking for doesn't exist or has ended."}
              </p>

              <div className="flex gap-4">
                <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white font-medium shadow-md hover:shadow-lg hover:shadow-blue-600/20 transition-all duration-300">
                  <RefreshCw className="mr-2 h-4 w-4" /> Refresh
                </Button>
                <Button variant="outline" className="border-vista-light/20 bg-black/30 text-vista-light hover:bg-black/40 hover:border-vista-light/40 transition-all duration-300" asChild>
                  <Link href="/watch-party">Browse Parties</Link>
                </Button>
              </div>
            </div>
          </div>
        )}

        {!isLoading && !error && party && (
          <div className="space-y-8">
            {/* Main Content Area */}
            <div className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-md border border-blue-500/20 rounded-xl overflow-hidden shadow-xl w-full">
              {/* Content Header with Backdrop */}
              <div className="relative aspect-[21/8] w-full overflow-hidden">
                {content?.backdropPath ? (
                  <div className="absolute inset-0">
                    <Image
                      src={`https://image.tmdb.org/t/p/original${content.backdropPath}`}
                      alt={content.title || 'Content poster'}
                      className="w-full h-full object-cover"
                      fill
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent"></div>
                  </div>
                ) : (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20"></div>
                )}

                {/* Content Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-8 z-10">
                  <div className="flex flex-col md:flex-row gap-6 items-end">
                    {/* Poster */}
                    {content?.posterPath && (
                      <div className="hidden md:block">
                        <Image
                          src={`https://image.tmdb.org/t/p/w185${content.posterPath}`}
                          alt={content.title || 'Content poster'}
                          className="rounded-lg shadow-lg border-2 border-white/10 h-48 w-auto object-cover"
                          width={185}
                        />
                      </div>
                    )}

                    {/* Content Info */}
                    <div className="flex-1">
                      <div className="flex flex-wrap gap-2 mb-4">
                        <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                          {content?.type === 'movie' ? 'Movie' : 'TV Show'}
                        </Badge>
                        {content?.year && (
                          <Badge variant="outline" className="border-white/20 text-white/90">
                            {content.year}
                          </Badge>
                        )}
                        {content?.genres?.slice(0, 3).map(genre => (
                          <Badge key={genre} variant="secondary" className="bg-black/40 backdrop-blur-sm text-vista-light">
                            {genre}
                          </Badge>
                        ))}
                      </div>
                      <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 drop-shadow-md">{content?.title || 'Untitled Content'}</h2>
                      <p className="text-vista-light/90 text-sm md:text-base line-clamp-3 max-w-3xl">
                        {content?.overview || 'No description available'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Party Info & Controls Section */}
              <div className="p-6 border-t border-blue-500/10 bg-gradient-to-r from-blue-600/5 to-vista-blue/5">
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                  {/* Party Info */}
                  <div className="w-full md:max-w-md">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex -space-x-2">
                        {party.members.slice(0, 3).map((member, index) => (
                          <Avatar
                            key={index}
                            className="border-2 border-background"
                          >
                            <AvatarImage src={member.avatar || ''} />
                            <AvatarFallback>{member.name?.charAt(0)}</AvatarFallback>
                          </Avatar>
                        ))}
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Hosted by <span className="text-blue-400 font-medium">{hostMember?.name || 'Unknown'}</span>
                        </p>
                        <p className="text-xs text-vista-light/60">
                          {party.members.length} {party.members.length === 1 ? 'member' : 'members'} • Created {formattedCreationTime}
                        </p>
                      </div>
                    </div>

                    <Alert className={`rounded-lg border ${isHost
                      ? "bg-blue-500/10 border-blue-500/30 text-blue-400"
                      : "bg-yellow-500/10 border-yellow-500/30 text-yellow-400"}`}>
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mt-0.5">
                          {isHost ? (
                            <Shield className="h-5 w-5" />
                          ) : (
                            <InfoIcon className="h-5 w-5" />
                          )}
                        </div>
                        <div className="ml-3">
                          <AlertTitle className="font-medium text-sm mb-1">
                            {isHost ? "Host Controls" : "Waiting for Host"}
                          </AlertTitle>
                          <AlertDescription className="text-sm opacity-90">
                            {isHost
                              ? "As the host, you have control over playback. Start the watch party when everyone is ready."
                              : "The host will start the watch party soon. You'll be able to join automatically."}
                          </AlertDescription>
                        </div>
                      </div>
                    </Alert>
                  </div>

                  {/* Controls */}
                  <div className="w-full md:max-w-sm flex flex-col justify-center">
                    <div className="flex flex-col gap-3">
                      <Button
                        size="lg"
                        onClick={(e) => {
                          e.preventDefault(); // Prevent any default behavior
                          e.stopPropagation(); // Stop event propagation
                          startWatching();
                        }}
                        type="button" // Explicitly set type to button to prevent form submission
                        disabled={isRedirectingToPlayer || (!isHost && !party.startedAt)}
                        className={`w-full shadow-md hover:shadow-lg text-lg font-medium py-6 ${isHost
                          ? "bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white"
                          : party.startedAt
                            ? "bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white"
                            : "bg-vista-blue/50 hover:bg-vista-blue/60 text-white/70"} transition-all duration-300`}
                      >
                        {isRedirectingToPlayer ? (
                          <>
                            <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                            {party.startedAt ? "Joining..." : "Starting..."}
                          </>
                        ) : (
                          <>
                            <Play className="mr-2 h-6 w-6" fill={!(!isHost && !party.startedAt) ? "currentColor" : "none"} />
                            {party.startedAt
                              ? (isHost ? "Resume Watching" : "Join Watch Party")
                              : (isHost ? "Start Watch Party" : "Waiting for Host")}
                          </>
                        )}
                      </Button>

                      {/* Action buttons with improved layout */}
                      <div className="flex flex-col gap-3 mt-4">
                        <div className="flex items-center gap-3 mb-3">
                          {party?.id && (
                            <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs px-2 py-0">
                              Party ID: #{party.id.substring(0, 8)}
                            </Badge>
                          )}
                          <Button
                            onClick={copyPartyId}
                            variant="outline"
                            size="sm"
                            className="border-vista-light/20 bg-black/30 text-vista-light hover:bg-black/40 hover:border-vista-light/40 transition-all duration-300"
                          >
                            <Copy className="mr-2 h-3 w-3" />
                            Share ID
                          </Button>
                        </div>

                        <Button
                          variant="outline"
                          onClick={leaveAndExitParty}
                          className="w-full border-vista-light/20 bg-black/30 text-vista-light hover:bg-black/40 hover:border-vista-light/40 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-4 w-4" />
                          {isHost ? 'Exit Party' : 'Leave Party'}
                        </Button>

                        {isHost && (
                          <Button
                            variant="outline"
                            onClick={endParty}
                            className="w-full border-red-500/20 bg-red-500/10 text-red-400 hover:bg-red-500/20 hover:border-red-500/30 transition-all duration-300"
                          >
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            End Party For Everyone
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Chat and Members Section - Full Width */}
            <div className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-md border border-blue-500/20 rounded-xl overflow-hidden shadow-xl">
              <Tabs defaultValue="chat" value={activeTab} onValueChange={handleTabChange} className="h-[500px] flex flex-col">
                <TabsList className="w-full rounded-none border-b border-blue-500/10 bg-black/30 p-0">
                  <TabsTrigger value="chat" className="flex items-center flex-1 rounded-none border-r border-blue-500/10 data-[state=active]:bg-vista-blue/10 data-[state=active]:text-vista-blue">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Chat
                  </TabsTrigger>
                  <TabsTrigger value="members" className="flex items-center flex-1 rounded-none data-[state=active]:bg-vista-blue/10 data-[state=active]:text-vista-blue">
                    <Users className="h-4 w-4 mr-2" />
                    Members ({party.members.length})
                  </TabsTrigger>
                </TabsList>

                <div className="flex-1 overflow-hidden flex flex-col">
                  <TabsContent value="chat" className="flex-1 flex flex-col h-full m-0 p-0">
                    {renderChatMessages}
                  </TabsContent>

                  <TabsContent value="members" className="h-full m-0 p-4 overflow-y-auto">
                    {renderMembersList}
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}