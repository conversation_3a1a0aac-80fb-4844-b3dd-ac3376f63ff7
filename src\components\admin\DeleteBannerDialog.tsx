'use client';

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2, Loader2 } from 'lucide-react';

interface DeleteBannerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  bannerTitle: string;
  isDeleting?: boolean;
}

export default function DeleteBannerDialog({
  isOpen,
  onClose,
  onConfirm,
  bannerTitle,
  isDeleting = false
}: DeleteBannerDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="bg-vista-dark border-vista-light/20">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-vista-light flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-400" />
            Delete Banner Ad
          </AlertDialogTitle>
          <AlertDialogDescription className="text-vista-light/70">
            Are you sure you want to delete the banner ad "{bannerTitle}"? 
            This action cannot be undone and will permanently remove the banner 
            from all display positions.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={onClose}
            disabled={isDeleting}
            className="bg-vista-light/10 text-vista-light border-vista-light/20 hover:bg-vista-light/20"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Banner
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
