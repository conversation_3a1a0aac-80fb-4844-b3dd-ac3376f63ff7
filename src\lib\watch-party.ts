import { IContent } from '@/data/content';

export interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  joinedAt: string;
  isReady: boolean;
}

export interface WatchPartyMessage {
  id: string;
  memberId: string;
  memberName: string;
  content: string;
  timestamp: string;
  type: 'chat' | 'system' | 'reaction';
}

export interface WatchPartyState {
  id: string;
  contentId: string;
  content: IContent;
  hostId: string;
  members: WatchPartyMember[];
  messages: WatchPartyMessage[];
  currentTime: number;
  isPlaying: boolean;
  createdAt: string;
  startedAt?: string;
  endedAt?: string;
}

// Helper to generate unique IDs
function generateId(): string {
  return `wp_${Math.random().toString(36).substring(2, 11)}`;
}

export class WatchParty {
  private state: WatchPartyState;
  private onStateChange?: (state: WatchPartyState) => void;

  constructor(
    content: IContent,
    host: { id: string; name: string; avatar?: string },
    onStateChange?: (state: WatchPartyState) => void
  ) {
    this.state = {
      id: `party_${Math.random().toString(36).substring(2, 9)}`,
      contentId: content.id,
      content,
      hostId: host.id,
      members: [{
        id: host.id,
        name: host.name,
        avatar: host.avatar,
        isHost: true,
        joinedAt: new Date().toISOString(),
        isReady: false
      }],
      messages: [],
      currentTime: 0,
      isPlaying: false,
      createdAt: new Date().toISOString()
    };
    this.onStateChange = onStateChange;
  }

  public getId(): string {
    return this.state.id;
  }

  // Get current state
  getState(): WatchPartyState {
    return { ...this.state };
  }

  // Add member to party
  addMember(member: { id: string; name: string; avatar?: string }): boolean {
    if (this.state.members.some(m => m.id === member.id)) {
      return false;
    }

    this.state.members.push({
      ...member,
      isHost: false,
      joinedAt: new Date().toISOString(),
      isReady: false
    });

    this.addSystemMessage(`${member.name} joined the party`);
    this.notifyStateChange();
    return true;
  }

  // Remove member from party
  removeMember(memberId: string): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member) return false;

    this.state.members = this.state.members.filter(m => m.id !== memberId);
    this.addSystemMessage(`${member.name} left the party`);

    // If host leaves, assign new host
    if (member.isHost && this.state.members.length > 0) {
      this.state.members[0].isHost = true;
      this.state.hostId = this.state.members[0].id;
      this.addSystemMessage(`${this.state.members[0].name} is now the host`);
    }

    this.notifyStateChange();
    return true;
  }

  // Add chat message
  addChatMessage(memberId: string, content: string): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member) return false;

    this.state.messages.push({
      id: generateId(),
      memberId,
      memberName: member.name,
      content,
      timestamp: new Date().toISOString(),
      type: 'chat'
    });

    this.notifyStateChange();
    return true;
  }

  // Add system message
  private addSystemMessage(content: string): void {
    this.state.messages.push({
      id: generateId(),
      memberId: 'system',
      memberName: 'System',
      content,
      timestamp: new Date().toISOString(),
      type: 'system'
    });

    this.notifyStateChange();
  }

  // Add reaction message
  addReaction(memberId: string, reaction: string): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member) return false;

    this.state.messages.push({
      id: generateId(),
      memberId,
      memberName: member.name,
      content: reaction,
      timestamp: new Date().toISOString(),
      type: 'reaction'
    });

    this.notifyStateChange();
    return true;
  }

  // Update playback state
  updatePlayback(memberId: string, time: number, isPlaying: boolean): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member || !member.isHost) return false;

    this.state.currentTime = time;
    this.state.isPlaying = isPlaying;

    if (!this.state.startedAt && isPlaying) {
      this.state.startedAt = new Date().toISOString();
    }

    this.notifyStateChange();
    return true;
  }

  // Set member ready state
  setMemberReady(memberId: string, isReady: boolean): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member) return false;

    member.isReady = isReady;
    this.addSystemMessage(`${member.name} is ${isReady ? 'ready' : 'not ready'}`);

    // Check if all members are ready
    const allReady = this.state.members.every(m => m.isReady);
    if (allReady && !this.state.isPlaying) {
      this.addSystemMessage('Everyone is ready! The host can start the playback.');
    }

    this.notifyStateChange();
    return true;
  }

  // End watch party
  end(memberId: string): boolean {
    const member = this.state.members.find(m => m.id === memberId);
    if (!member || !member.isHost) return false;

    this.state.endedAt = new Date().toISOString();
    this.state.isPlaying = false;
    this.addSystemMessage('Watch party ended by host');

    this.notifyStateChange();
    return true;
  }

  private notifyStateChange(): void {
    if (this.onStateChange) {
      this.onStateChange(this.getState());
    }
  }
} 