import { Metadata } from 'next'
import { Suspense } from 'react'
import Navbar from '@/components/Navbar'
import { WatchPartyWrapper } from './watch-party-wrapper'

// Configure for compatibility with static export
export const preferredRegion = 'auto'
export const dynamic = 'auto'

export const metadata: Metadata = {
  title: 'StreamVista | Watch Party',
  description: 'Watch movies and TV shows together with friends in real-time',
}

export default function WatchPartyLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <WatchPartyWrapper>
      <Navbar />
      <main className="min-h-screen bg-gradient-to-b from-blue-950 to-teal-950 pt-16">
        <Suspense fallback={
          <div className="flex justify-center items-center min-h-screen bg-gradient-to-b from-blue-950 to-teal-950 pt-16">
            <p className="text-vista-light/50">Loading watch party...</p>
          </div>
        }>
          {children}
        </Suspense>
      </main>
    </WatchPartyWrapper>
  )
} 