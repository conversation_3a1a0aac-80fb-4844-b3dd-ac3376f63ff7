'use client'

import WatchPartyHome from './WatchPartyHome'
import { useEffect } from 'react'

// Next.js 15 configuration
export const preferredRegion = 'auto'
export const dynamic = 'auto'

export default function WatchPartyPage() {
  useEffect(() => {
    // Parse and clear the 'exiting' parameter from URL to prevent redirect loops
    const params = new URLSearchParams(window.location.search);
    const isExiting = params.get('exiting') === 'true';
    
    if (isExiting) {
      // Clear the 'exiting' parameter from URL without refreshing the page
      params.delete('exiting');
      const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : '');
      window.history.replaceState({}, '', newUrl);
      
      console.log('[WatchParty page] Cleared exiting parameter');
    }
  }, []);

  return <WatchPartyHome />
} 