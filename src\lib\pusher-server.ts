import Pusher from 'pusher';

// Initialize Pusher server
export const pusherServer = new Pusher({
  appId: process.env.PUSHER_APP_ID!,
  key: process.env.PUSHER_KEY!,
  secret: process.env.PUSHER_SECRET!,
  cluster: process.env.PUSHER_CLUSTER!,
  useTLS: true,
});

// Socket event names for watch parties - these must be strings exactly matching what's used in API and client
export const WATCH_PARTY_EVENTS = {
  // Party management events
  CREATE_PARTY: 'create-watch-party',
  JOIN_PARTY: 'join-watch-party',
  LEAVE_PARTY: 'leave-watch-party',
  GET_PARTIES: 'get-watch-parties',
  
  // Update events
  PARTY_CREATED: 'party-created',
  PARTY_UPDATE: 'party-update',
  PARTY_DELETED: 'party-deleted',
  MEMBER_UPDATE: 'member-update',
  PLAYBACK_UPDATE: 'playback-update',
  NEW_MESSAGE: 'new-message',
  AVAILABLE_PARTIES: 'available-parties'
};

// Debug helper for Pusher
export async function logPusherEvent(channel: string, event: string, data: any) {
  console.log(`[PUSHER:${event}] Broadcasting to ${channel}`);
  try {
    await pusherServer.trigger(channel, event, data);
    console.log(`[PUSHER:${event}] Successfully sent to ${channel}`);
  } catch (error) {
    console.error(`[PUSHER:${event}] Error broadcasting to ${channel}:`, error);
  }
} 