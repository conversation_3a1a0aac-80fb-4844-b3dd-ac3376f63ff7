'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  Film, Tv, Search, Loader2
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ContentCardType, formatTMDbContentForCards } from '@/lib/content-utils'
import { cn } from '@/lib/utils'
import ContentCard from '@/components/ContentCard'
import { useDebounce } from '@/lib/hooks/useDebounce'
import { getPopularMovies, getPopularTVShows, search } from '@/lib/tmdb-api'

interface ContentBrowserProps {
  onSelectContent: (content: ContentCardType) => void
}

export default function ContentBrowser({ onSelectContent }: ContentBrowserProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedContent, setSelectedContent] = useState<ContentCardType | null>(null)
  const [movies, setMovies] = useState<ContentCardType[]>([])
  const [shows, setShows] = useState<ContentCardType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchResults, setSearchResults] = useState<ContentCardType[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const debouncedSearch = useDebounce(searchQuery, 500)

  // Fetch initial content on mount
  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        console.log('[ContentBrowser] Fetching initial content');

        // Fetch popular movies and TV shows from TMDB API
        const [movieResults, showResults] = await Promise.all([
          getPopularMovies(),
          getPopularTVShows()
        ]);

        // Format the results for display in content cards
        const formattedMovies = formatTMDbContentForCards(movieResults);
        const formattedShows = formatTMDbContentForCards(showResults);

        console.log(`[ContentBrowser] Fetched ${formattedMovies.length} movies and ${formattedShows.length} shows`);

        setMovies(formattedMovies);
        setShows(formattedShows);
      } catch (error) {
        console.error('[ContentBrowser] Error fetching content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, []);

  // Handle search with debounce
  useEffect(() => {
    if (!debouncedSearch.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    const performSearch = async () => {
      try {
        setIsSearching(true);
        console.log(`[ContentBrowser] Searching for "${debouncedSearch}"`);

        // Search for movies and TV shows
        const results = await search(debouncedSearch);

        // Format the results for display
        const formattedResults = formatTMDbContentForCards(results);

        console.log(`[ContentBrowser] Found ${formattedResults.length} results for "${debouncedSearch}"`);
        setSearchResults(formattedResults);
      } catch (error) {
        console.error('[ContentBrowser] Error searching content:', error);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearch]);

  // Handle content selection using the main ContentCard's expected structure
  const handleSelectContent = useCallback((content: ContentCardType) => {
    setSelectedContent(content);
    onSelectContent(content);
  }, [onSelectContent]);

  // Determine which content to display based on search state
  const displayMovies = searchQuery ?
    searchResults.filter(item => item.type === 'movies') :
    movies;

  const displayShows = searchQuery ?
    searchResults.filter(item => item.type === 'shows') :
    shows;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="bg-blue-600/20 p-2 rounded-md">
          <Film className="h-4 w-4 text-blue-400" />
        </div>
        <h2 className="text-lg font-medium text-white">Select Content to Watch</h2>
      </div>

      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-vista-light/50" />
        </div>
        <Input
          type="text"
          placeholder="Search for a movie or TV show..."
          className="pl-10 bg-black/20 border-blue-500/20 text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="movies" className="w-full">
        <TabsList className="bg-vista-dark-lighter/50 p-1 rounded-lg">
          <TabsTrigger
            value="movies"
            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
          >
            Movies
          </TabsTrigger>
          <TabsTrigger
            value="shows"
            className="data-[state=active]:bg-teal-600 data-[state=active]:text-white rounded-md px-4 py-2"
          >
            TV Shows
          </TabsTrigger>
        </TabsList>

        <TabsContent value="movies" className="mt-4">
          <div className="bg-black/20 backdrop-blur-sm border border-blue-500/10 rounded-xl p-3">
            {isLoading || isSearching ? (
              <div className="flex flex-col items-center justify-center py-16">
                <Loader2 className="h-8 w-8 text-blue-500 animate-spin mb-2" />
                <p className="text-vista-light/70">
                  {isSearching ? `Searching for "${searchQuery}"...` : 'Loading movies...'}
                </p>
              </div>
            ) : displayMovies.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                {displayMovies.map((movie) => (
                  <div
                    key={movie.id}
                    onClick={() => handleSelectContent(movie)}
                    className={cn(
                      "cursor-pointer transition-transform duration-300",
                      selectedContent?.id === movie.id ? "scale-105 ring-2 ring-blue-500 ring-offset-2 ring-offset-black/20 rounded-lg" : ""
                    )}>
                    <ContentCard
                      {...movie} // Spread movie data matching ContentCardProps
                      index={0} // You might want a proper index for staggered animations if needed
                      onClick={(e) => { e.preventDefault(); handleSelectContent(movie); }} // Prevent default link navigation
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="bg-blue-900/30 p-3 rounded-full inline-flex mb-3">
                  <Film className="h-6 w-6 text-blue-400/80" />
                </div>
                <h3 className="text-lg font-medium text-vista-light mb-2">
                  {searchQuery ? `No movies found for "${searchQuery}"` : 'No movies found'}
                </h3>
                <p className="text-vista-light/60 max-w-md mx-auto">
                  Try adjusting your search to find what you're looking for.
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="shows" className="mt-4">
          <div className="bg-black/20 backdrop-blur-sm border border-blue-500/10 rounded-xl p-3">
            {isLoading || isSearching ? (
              <div className="flex flex-col items-center justify-center py-16">
                <Loader2 className="h-8 w-8 text-teal-500 animate-spin mb-2" />
                <p className="text-vista-light/70">
                  {isSearching ? `Searching for "${searchQuery}"...` : 'Loading TV shows...'}
                </p>
              </div>
            ) : displayShows.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                {displayShows.map((show) => (
                   <div
                    key={show.id}
                    onClick={() => handleSelectContent(show)}
                    className={cn(
                      "cursor-pointer transition-transform duration-300",
                      selectedContent?.id === show.id ? "scale-105 ring-2 ring-blue-500 ring-offset-2 ring-offset-black/20 rounded-lg" : ""
                    )}>
                    <ContentCard
                      {...show} // Spread show data matching ContentCardProps
                      index={0} // You might want a proper index for staggered animations if needed
                      onClick={(e) => { e.preventDefault(); handleSelectContent(show); }} // Prevent default link navigation
                    />
                   </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="bg-teal-900/30 p-3 rounded-full inline-flex mb-3">
                  <Tv className="h-6 w-6 text-teal-400/80" />
                </div>
                <h3 className="text-lg font-medium text-vista-light mb-2">
                  {searchQuery ? `No TV shows found for "${searchQuery}"` : 'No TV shows found'}
                </h3>
                <p className="text-vista-light/60 max-w-md mx-auto">
                  Try adjusting your search to find what you're looking for.
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
