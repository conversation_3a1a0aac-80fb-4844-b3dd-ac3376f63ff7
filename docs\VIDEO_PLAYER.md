# Video Player Service

## Overview

StreamVista's video player service provides a feature-rich, customizable video playback experience with support for multiple video sources, subtitles, watch party integration, and advanced playback controls. The service includes two main components: the core `VideoPlayer` and the specialized `VidSrcPlayer` for external video sources.

## Components

### Core Video Player

```typescript
interface VideoPlayerProps {
  videoSrc: string;
  title: string;
  posterSrc?: string;
  onClose?: () => void;
  autoPlay?: boolean;
  subtitles?: { src: string; label: string; srcLang: string }[];
  qualityOptions?: { label: string; src: string }[];
  content: IContent;
  className?: string;
  contentId: string;
}
```

### VidSrc Player

```typescript
interface VidSrcPlayerProps {
  type: 'movie' | 'show';
  tmdbId: string;
  season?: number;
  episode?: number;
  height?: string | number;
  className?: string;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}
```

## Features

### 1. Playback Controls

- **Basic Controls**
  - Play/Pause toggle
  - Volume control with mute option
  - Progress bar with seek functionality
  - Duration and current time display

- **Advanced Controls**
  - Playback speed adjustment (0.25x to 2x)
  - Forward/Backward skip (10 seconds)
  - Picture-in-Picture mode
  - Theater mode
  - Fullscreen toggle

### 2. Quality Management

```typescript
interface QualityOption {
  label: string;  // e.g., "1080p", "720p"
  src: string;    // Video source URL
}
```

- Dynamic quality switching
- Automatic quality selection based on network conditions
- Manual quality override through settings menu

### 3. Subtitle Support

```typescript
interface Subtitle {
  src: string;     // VTT file URL
  label: string;   // Display name
  srcLang: string; // Language code
}
```

- Multiple subtitle track support
- VTT format compatibility
- On/off toggle
- Language selection

### 4. Watch Party Integration

```typescript
interface WatchPartyState {
  currentTime: number;
  isPlaying: boolean;
  partyId: string;
  hostId: string;
}
```

- Synchronized playback across party members
- Host controls
- Party member status display
- Real-time synchronization

### 5. Keyboard Controls

```typescript
const KeyboardShortcuts = {
  PLAY_PAUSE: ['Space', 'K'],
  SEEK_FORWARD: ['ArrowRight'],
  SEEK_BACKWARD: ['ArrowLeft'],
  VOLUME_UP: ['ArrowUp'],
  VOLUME_DOWN: ['ArrowDown'],
  MUTE: ['M'],
  FULLSCREEN: ['F'],
  PIP: ['P'],
  SEEK_PERCENTAGE: ['0-9']
};
```

## Implementation Details

### 1. State Management

```typescript
interface PlayerState {
  isPlaying: boolean;
  progress: number;
  volume: number;
  isMuted: boolean;
  duration: number;
  currentTime: number;
  showControls: boolean;
  isFullScreen: boolean;
  showVolumeSlider: boolean;
  isBuffering: boolean;
  isPictureInPicture: boolean;
  playbackRate: number;
  activeSubtitle: string | null;
}
```

### 2. Event Handling

```typescript
// Video Events
const videoEvents = {
  timeupdate: handleTimeUpdate,
  loadedmetadata: handleLoadedMetadata,
  ended: handleEnded,
  waiting: handleBuffering,
  playing: handlePlaying,
  error: handleError
};

// Document Events
const documentEvents = {
  fullscreenchange: handleFullscreenChange,
  keydown: handleKeyboardShortcuts
};
```

### 3. Provider Management (VidSrcPlayer)

```typescript
interface Provider {
  id: string;
  name: string;
  domain: string;
  quality: string[];
}

const providerOptions = [
  { id: 'provider1', name: 'Provider 1', domain: 'domain1.com' },
  { id: 'provider2', name: 'Provider 2', domain: 'domain2.com' }
];
```

## Best Practices

1. **Performance**
   - Lazy loading of video sources
   - Adaptive bitrate streaming
   - Efficient state management
   - Memory leak prevention

2. **User Experience**
   - Smooth controls fade in/out
   - Responsive design
   - Loading indicators
   - Error handling with user feedback
   - Keyboard accessibility

3. **Security**
   - Content encryption
   - Cross-origin resource policy
   - Secure iframe sandbox
   - API key protection

4. **Accessibility**
   - ARIA labels
   - Keyboard navigation
   - High contrast controls
   - Screen reader support

5. **Error Handling**
   ```typescript
   const handleError = (error: Error) => {
     console.error('Video playback error:', error);
     showError('Playback Error', 'Unable to play the video. Please try again.');
   };
   ```

## Usage Examples

### Basic Implementation

```typescript
<VideoPlayer
  videoSrc="https://example.com/video.mp4"
  title="Sample Video"
  posterSrc="https://example.com/poster.jpg"
  content={videoContent}
  contentId="video123"
/>
```

### With Watch Party

```typescript
<VideoPlayer
  videoSrc={videoSource}
  title={content.title}
  content={content}
  contentId={content.id}
  onClose={handleClose}
  autoPlay={true}
  subtitles={availableSubtitles}
  qualityOptions={qualityLevels}
/>
```

### External Source Player

```typescript
<VidSrcPlayer
  type="movie"
  tmdbId="123456"
  height="600px"
  onError={handleError}
  onLoad={handleLoad}
/>
```

## Integration Guidelines

1. **Watch Party Integration**
   - Initialize socket connection
   - Handle synchronization events
   - Manage party state
   - Handle host/client roles

2. **Content Service Integration**
   - Fetch video metadata
   - Handle content types
   - Manage streaming sources
   - Track playback progress

3. **UI/UX Integration**
   - Theme consistency
   - Responsive design
   - Loading states
   - Error feedback

4. **Analytics Integration**
   - Track playback events
   - Monitor quality metrics
   - Collect user interactions
   - Report errors

## Development Tools

1. **Testing Utilities**
   ```typescript
   const mockVideoPlayer = {
     play: jest.fn(),
     pause: jest.fn(),
     currentTime: 0,
     duration: 100,
     volume: 1,
     muted: false
   };
   ```

2. **Debug Helpers**
   ```typescript
   const DEBUG_MODE = process.env.NODE_ENV === 'development';
   
   const logPlaybackEvent = (event: string, data: any) => {
     if (DEBUG_MODE) {
       console.log(`[VideoPlayer] ${event}:`, data);
     }
   };
   ```

3. **Performance Monitoring**
   ```typescript
   interface PlaybackMetrics {
     bufferingCount: number;
     bufferingDuration: number;
     qualitySwitches: number;
     averageBitrate: number;
   }
   ``` 