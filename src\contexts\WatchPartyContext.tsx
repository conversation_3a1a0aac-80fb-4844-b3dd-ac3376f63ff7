"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { pusherClient } from "@/lib/pusher-client"
import type { Channel } from 'pusher-js'

export interface WatchPartyState {
  id: string
  contentId: string
  host: string
  members: string[]
  currentTime: number
  isPlaying: boolean
  messages: ChatMessage[]
  reactions: Reaction[]
}

export interface ChatMessage {
  id: string
  sender: string
  content: string
  timestamp: number
  type: "text" | "emoji" | "gif" | "system"
}

export interface Reaction {
  id: string
  sender: string
  emoji: string
  timestamp: number
}

// Define a type for server event data
interface ServerEventData {
  userId?: string
  [key: string]: unknown
}

interface WatchPartyContextType {
  party: WatchPartyState | null
  error: string | null
  createParty: (contentId: string, name: string) => void
  joinParty: (partyId: string, name: string) => void
  leaveParty: () => void
  sendMessage: (content: string, type?: ChatMessage["type"]) => void
  sendReaction: (emoji: string) => void
  updatePlaybackState: (currentTime: number, isPlaying: boolean) => void
}

const WatchPartyContext = createContext<WatchPartyContextType>({
  party: null,
  error: null,
  createParty: () => {},
  joinParty: () => {},
  leaveParty: () => {},
  sendMessage: () => {},
  sendReaction: () => {},
  updatePlaybackState: () => {},
})

export function useWatchParty() {
  return useContext(WatchPartyContext)
}

export function WatchPartyProvider({ children }: { children: React.ReactNode }) {
  const [party, setParty] = useState<WatchPartyState | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [channelName, setChannelName] = useState<string | null>(null)
  const [userId] = useState<string>(`user-${Math.random().toString(36).substring(2, 9)}`)

  // Subscribe to Pusher channel when party changes
  useEffect(() => {
    if (!party || !party.id) return

    // Create channel name based on party ID
    const newChannelName = `watch-party-${party.id}`
    setChannelName(newChannelName)

    // Subscribe to the channel
    const channel = pusherClient.subscribe(newChannelName)

    // Set up event handlers
    channel.bind('party-update', (data: Partial<WatchPartyState>) => {
      setParty(prev => prev ? { ...prev, ...data } : null)
    })

    channel.bind('party-leave', () => {
      setParty(null)
      setChannelName(null)
    })

    channel.bind('error', (data: { message: string }) => {
      setError(data.message)
    })

    // Clean up on unmount
    return () => {
      // Check if the channel is a Pusher Channel which has unbind_all
      if ('unbind_all' in channel) {
        (channel as Channel).unbind_all()
      }
      pusherClient.unsubscribe(newChannelName)
    }
  }, [party])

  // Helper function to trigger server events via API
  const triggerServerEvent = async (event: string, data: ServerEventData) => {
    try {
      const response = await fetch('/api/pusher', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel: channelName,
          event,
          data: {
            ...data,
            userId
          }
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send event to server')
      }
    } catch (err) {
      console.error('Error triggering server event:', err)
      setError('Failed to communicate with server')
    }
  }

  const createParty = (contentId: string, name: string) => {
    // Create party via API
    fetch('/api/watch-party', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contentId,
        hostName: name,
        userId
      }),
    })
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        setError(data.error)
      } else {
        setParty(data)
      }
    })
    .catch(err => {
      console.error('Error creating party:', err)
      setError('Failed to create party')
    })
  }

  const joinParty = (partyId: string, name: string) => {
    // Join party via API
    fetch(`/api/watch-party/${partyId}/join`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        userId
      }),
    })
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        setError(data.error)
      } else {
        setParty(data)
      }
    })
    .catch(err => {
      console.error('Error joining party:', err)
      setError('Failed to join party')
    })
  }

  const leaveParty = () => {
    if (!party) return

    // Leave party via API
    fetch(`/api/watch-party/${party.id}/leave`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId
      }),
    })
    .then(() => {
      setParty(null)
      setChannelName(null)
    })
    .catch(err => {
      console.error('Error leaving party:', err)
      setError('Failed to leave party')
    })
  }

  const sendMessage = (content: string, type: ChatMessage["type"] = "text") => {
    if (!party || !channelName) return

    // Generate a unique message ID
    const messageId = `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

    // Trigger message event
    triggerServerEvent('chat-message', {
      id: messageId,
      content,
      type,
      timestamp: Date.now()
    })
  }

  const sendReaction = (emoji: string) => {
    if (!party || !channelName) return

    // Trigger reaction event
    triggerServerEvent('reaction', {
      emoji,
      timestamp: Date.now()
    })
  }

  const updatePlaybackState = (currentTime: number, isPlaying: boolean) => {
    if (!party || !channelName) return

    // Trigger playback update event
    triggerServerEvent('playback-update', {
      currentTime,
      isPlaying
    })
  }

  return (
    <WatchPartyContext.Provider
      value={{
        party,
        error,
        createParty,
        joinParty,
        leaveParty,
        sendMessage,
        sendReaction,
        updatePlaybackState,
      }}
    >
      {children}
    </WatchPartyContext.Provider>
  )
}