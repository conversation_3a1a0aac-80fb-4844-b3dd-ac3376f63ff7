'use client';

import { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContentCard from '@/components/ContentCard';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { GridIcon, ListIcon, Filter, ChevronDown, Search, Star, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  searchTVShows, 
  discoverTVShows,
  MappedContent, 
  TMDB_GENRE_MAP,
  TMDB_GENRE_NAME_TO_ID_MAP,
  DiscoverTVFilters
} from '@/lib/tmdb-api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import { FilterPanel } from '@/components/FilterPanel';
import { scrollToTop } from '@/utils/scroll-utils';

// Define available genres for filters
const availableGenres = Object.values(TMDB_GENRE_MAP).sort();

// Years for filtering
const yearFilters = [
  { id: 'all', name: 'All Years' },
  { id: '2024', name: '2024' },
  { id: '2023', name: '2023' },
  { id: '2022', name: '2022' },
  { id: '2021', name: '2021' },
  { id: '2020', name: '2020' },
  { id: 'older', name: 'Before 2020' }
];

// Ratings for filtering
const ratingFilters = [
  { id: 'all', name: 'All Ratings' },
  { id: 'high', name: '8+ Rating' },
  { id: 'medium', name: '6-8 Rating' },
  { id: 'low', name: 'Below 6' }
];

// Categories for TV shows
const categories = [
  { id: 'popular', name: 'Popular' },
  { id: 'top_rated', name: 'Top Rated' },
  { id: 'on_the_air', name: 'On The Air' },
  { id: 'trending', name: 'Trending' }
];

// Add Sort Options for TV Shows
const sortOptions = [
  { id: 'popularity.desc', name: 'Popularity Descending' },
  { id: 'popularity.asc', name: 'Popularity Ascending' },
  { id: 'vote_average.desc', name: 'Rating Descending' },
  { id: 'vote_average.asc', name: 'Rating Ascending' },
  { id: 'first_air_date.desc', name: 'First Air Date Descending' },
  { id: 'first_air_date.asc', name: 'First Air Date Ascending' },
];

export default function ShowsPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedRating, setSelectedRating] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('popular');
  const [selectedSortBy, setSelectedSortBy] = useState(sortOptions[0].id); // Default sort
  const [page, setPage] = useState(1);
  const [shows, setShows] = useState<MappedContent[]>([]);
  const [formattedShows, setFormattedShows] = useState<ContentCardType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [totalResults, setTotalResults] = useState(0);

  // Scroll to top when page changes
  useEffect(() => {
    scrollToTop();
  }, [page]);

  // Load TV shows from TMDb API using discover or search
  useEffect(() => {
    const fetchShows = async () => {
      setLoading(true);
      setError(null);

      try {
        let response;
        if (searchQuery.trim().length > 0) {
          // Handle search separately
          console.log(`Searching for TV Shows: ${searchQuery}, Page: ${page}`);
          const searchResults = await searchTVShows(searchQuery, page);
          // Adjust if searchTVShows response structure differs
          response = { results: searchResults, totalPages: 1, totalResults: searchResults.length }; 
        } else {
          // Use discoverTVShows with filters and sorting
          const filters: DiscoverTVFilters = { 
            page, 
            sort_by: selectedSortBy // Use selectedSortBy
          };

          // Add genre filters
          if (selectedGenres.length > 0) {
            filters.with_genres = selectedGenres
              .map(genreName => TMDB_GENRE_NAME_TO_ID_MAP[genreName])
              .filter(Boolean)
              .join(',');
          }

          // Add year filter (use first_air_date_year for TV)
          if (selectedYear !== 'all') {
            if (selectedYear === 'older') {
              console.warn("Filtering TV shows by 'Before 2020' is not directly supported, ignoring year filter.");
              // Could implement date range filtering: filters['first_air_date.lte'] = '2019-12-31';
            } else {
              filters.first_air_date_year = selectedYear;
            }
          }

          // Add rating filter (same logic as movies)
          if (selectedRating !== 'all') {
            switch (selectedRating) {
              case 'high': filters['vote_average.gte'] = '8'; break;
              case 'medium': filters['vote_average.gte'] = '6'; filters['vote_average.lte'] = '7.99'; break;
              case 'low': filters['vote_average.lte'] = '5.99'; break;
            }
          }
          
          // Automatically add minimum vote count filter when sorting by rating
          if (selectedSortBy.startsWith('vote_average.')) {
            filters['vote_count.gte'] = '10'; // Only consider items with at least 10 votes when sorting by rating
          }

          console.log("Discover TV Filters:", filters);
          response = await discoverTVShows(filters);
        }

        // Update state
        setShows(response.results);
        setFormattedShows(formatTMDbContentForCards(response.results));
        setTotalPages(response.totalPages);
        setTotalResults(response.totalResults);

      } catch (err) {
        console.error('Error fetching TV shows:', err);
        setError(err instanceof Error ? err.message : 'Failed to load TV shows. Please try again later.');
        setShows([]);
        setFormattedShows([]);
        setTotalPages(1);
        setTotalResults(0);
      } finally {
        setLoading(false);
      }
    };

    fetchShows();
  }, [page, searchQuery, selectedGenres, selectedYear, selectedRating, selectedSortBy]);

  // Handlers
  const toggleGenre = (genre: string) => {
    setSelectedGenres(prev =>
      prev.includes(genre) ? prev.filter(g => g !== genre) : [...prev, genre]
    );
    setPage(1);
  };

  const handleYearChange = (yearId: string) => {
    setSelectedYear(yearId);
    setPage(1);
  };

  const handleRatingChange = (ratingId: string) => {
    setSelectedRating(ratingId);
    setPage(1);
  };
  
  const handleSortChange = (sortId: string) => {
    setSelectedSortBy(sortId);
    setPage(1);
  };
  
  const handleCategoryChange = (categoryId: string) => {
    setSearchQuery('');
    setPage(1);
    
    let defaultSort = 'popularity.desc';
    if (categoryId === 'top_rated') defaultSort = 'vote_average.desc';
    if (categoryId === 'on_the_air') defaultSort = 'first_air_date.desc'; // Match TV sort options
    
    setSelectedSortBy(defaultSort);
    setSelectedCategory(categoryId);
    setSelectedRating('all');
    setSelectedSortBy(sortOptions[0].id); // Reset sort
    setPage(1);
  };

  const clearFilters = () => {
    setSelectedGenres([]);
    setSelectedYear('all');
    setSelectedRating('all');
    setSelectedSortBy(sortOptions[0].id); // Reset sort
    setPage(1);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
  };
  
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      {/* Hero Section - Reduced mobile height slightly */}
      <div className="relative overflow-hidden h-[50vh] md:h-[500px]">
        {!loading && shows.length > 0 && shows[0].backdropUrl ? (
          <Image
            src={shows[0].backdropUrl}
            alt="Featured TV Show"
            fill
            // Removed brightness, added object-top
            className="object-cover object-top"
            priority
          />
        ) : (
          <div className="absolute inset-0 bg-gradient-to-r from-vista-dark-lighter to-vista-dark"></div>
        )}
        {/* Adjusted gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent" />
        {/* Adjusted padding */}
        <div className="absolute bottom-0 left-0 px-4 sm:px-6 pb-6 md:p-10 w-full">
          <h1 className="text-4xl md:text-5xl font-bold mb-3 text-white">TV Shows</h1>
          <p className="text-lg md:text-xl text-vista-light/90 max-w-xl">
            Explore binge-worthy series, documentaries, and captivating shows.
          </p>
        </div>
      </div>

      {/* Adjusted main top padding */}
      <main className="container mx-auto px-4 pt-8 pb-24">
        {/* Categories */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-vista-light">Browse by Category</h2>
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id && !searchQuery ? "default" : "outline"}
                className={selectedCategory === category.id
                  ? "bg-vista-blue hover:bg-vista-blue/90"
                  : "border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
                }
                onClick={() => handleCategoryChange(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between gap-4 mb-6">
            {/* Search */}
            <form onSubmit={handleSearchSubmit} className="relative w-full md:w-72">
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearchInputChange}
                placeholder="Search TV shows..."
                className="w-full bg-vista-dark-lighter rounded-full py-2.5 pl-10 pr-4 text-vista-light placeholder:text-vista-light/50 focus:outline-none focus:ring-2 focus:ring-vista-blue border border-vista-light/10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-vista-light/50" />
              <button type="submit" className="sr-only">Search</button>
            </form>

            {/* Controls */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                <ChevronDown className={`ml-2 h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
              </Button>

              <div className="flex items-center border border-vista-light/20 rounded-lg overflow-hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  className={`w-9 h-9 rounded-none ${viewMode === 'grid' ? 'bg-vista-light/10 text-vista-light' : 'text-vista-light/50'}`}
                  onClick={() => setViewMode('grid')}
                >
                  <GridIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className={`w-9 h-9 rounded-none ${viewMode === 'list' ? 'bg-vista-light/10 text-vista-light' : 'text-vista-light/50'}`}
                  onClick={() => setViewMode('list')}
                >
                  <ListIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Active Filters - Add Sort By and Min Votes */}
          {(selectedGenres.length > 0 || selectedYear !== 'all' || selectedRating !== 'all' || selectedSortBy !== sortOptions[0].id) && (
            <div className="flex flex-wrap gap-2 mb-4 items-center">
              {selectedGenres.map(genre => (
                <Badge
                  key={genre}
                  variant="accent"
                  className="rounded-full px-3 py-1 bg-vista-blue/20 text-vista-blue"
                >
                  {genre}
                  <button
                    className="ml-1.5 hover:text-white/80"
                    onClick={() => toggleGenre(genre)}
                  >
                    ×
                  </button>
                </Badge>
              ))}

              {selectedYear !== 'all' && (
                <Badge
                  variant="accent"
                  className="rounded-full px-3 py-1 bg-vista-blue/20 text-vista-blue"
                >
                  Year: {yearFilters.find(y => y.id === selectedYear)?.name}
                  <button
                    className="ml-1.5 hover:text-white/80"
                    onClick={() => handleYearChange('all')}
                  >
                    ×
                  </button>
                </Badge>
              )}

              {selectedRating !== 'all' && (
                <Badge
                  variant="accent"
                  className="rounded-full px-3 py-1 bg-vista-blue/20 text-vista-blue"
                >
                  Rating: {ratingFilters.find(r => r.id === selectedRating)?.name}
                  <button
                    className="ml-1.5 hover:text-white/80"
                    onClick={() => handleRatingChange('all')}
                  >
                    ×
                  </button>
                </Badge>
              )}
              
              {selectedSortBy !== sortOptions[0].id && (
                <Badge
                  variant="accent"
                  className="rounded-full px-3 py-1 bg-vista-blue/20 text-vista-blue"
                >
                  Sort: {sortOptions.find(s => s.id === selectedSortBy)?.name}
                  <button
                    className="ml-1.5 hover:text-white/80"
                    onClick={() => handleSortChange(sortOptions[0].id)}
                  >
                    ×
                  </button>
                </Badge>
              )}

              {(selectedGenres.length > 0 || selectedYear !== 'all' || selectedRating !== 'all' || selectedSortBy !== sortOptions[0].id) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-vista-blue hover:text-vista-blue/80 ml-2"
                  onClick={clearFilters}
                >
                  Clear all
                </Button>
              )}
            </div>
          )}

          {/* Filter Panel - Pass new props */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden mb-6"
              >
                <FilterPanel
                  availableGenres={availableGenres}
                  yearFilters={yearFilters}
                  ratingFilters={ratingFilters}
                  sortOptions={sortOptions} // Pass sort options
                  selectedGenres={selectedGenres}
                  selectedYear={selectedYear}
                  selectedRating={selectedRating}
                  selectedSortBy={selectedSortBy} // Pass selected sort
                  onGenreToggle={toggleGenre}
                  onYearChange={handleYearChange}
                  onRatingChange={handleRatingChange}
                  onSortChange={handleSortChange} // Pass sort handler
                  filterType="show"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Error message */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Content Grid */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
            <span className="ml-2 text-vista-light">Loading TV shows...</span>
          </div>
        ) : formattedShows.length > 0 ? (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className={`grid grid-cols-2 ${
              viewMode === 'grid'
                ? 'md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'
                : 'gap-4'
            }`}
          >
            {formattedShows.map((show: ContentCardType, index: number) => (
              <motion.div key={show.id} variants={itemVariants}>
                <ContentCard
                  {...show}
                  index={index}
                  listMode={viewMode === 'list'}
                  showMetadata={viewMode === 'list'}
                  link={`/watch/${show.id}?forcePlay=true&contentType=show`}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="text-center py-20 text-vista-light/70">
            <p className="text-xl mb-4">No TV shows found</p>
            <p>Try adjusting your filters or search terms</p>
            <Button
              variant="outline"
              className="mt-4 border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
              onClick={clearFilters}
            >
              Clear all filters
            </Button>
          </div>
        )}

        {/* Pagination */}
        {!loading && totalPages > 1 && (
          <div className="mt-12 flex justify-center items-center gap-4">
            <Button
              variant="outline"
              className="border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
            >
              Previous Page
            </Button>
            <span className="text-vista-light">
              Page {page}
            </span>
            <Button
              variant="outline"
              className="border-vista-light/20 bg-transparent text-vista-light hover:bg-vista-light/10"
              onClick={() => setPage(page + 1)}
            >
              Next Page
            </Button>
          </div>
        )}

        {/* TMDb Attribution */}
        <div className="mt-12 text-center text-sm text-vista-light/60">
          <p>TV show data provided by The Movie Database (TMDb)</p>
          <div className="mt-2">
            <Image
              src="https://www.themoviedb.org/assets/2/v4/logos/v2/blue_short-8e7b30f73a4020692ccca9c88bafe5dcb6f8a62a4c6bc55cd9ba82bb2cd95f6c.svg"
              alt="TMDb Logo"
              width={154}
              height={20}
              className="h-5 mx-auto"
            />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}