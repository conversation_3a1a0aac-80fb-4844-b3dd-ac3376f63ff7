import ContentCard from "@/components/ContentCard";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { ContentCardType } from "@/lib/content-utils";

interface RelatedContentProps {
  title: string;
  subtitle?: string;
  contents: (ContentCardType & { onClick?: (e: React.MouseEvent) => void })[];
}

export default function RelatedContent({ title, subtitle, contents }: RelatedContentProps) {
  return (
    <div className="py-12 px-8 md:px-16 bg-vista-dark">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-end mb-6">
          <div>
            <h2 className="text-2xl font-semibold text-vista-light">{title}</h2>
            {subtitle && <p className="text-sm text-vista-light/60 mt-1">{subtitle}</p>}
          </div>
        </div>

        <Carousel
          opts={{
            align: "start",
            loop: false,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-4">
            {contents.map((content, index) => (
              <CarouselItem key={index} className="pl-4 basis-[220px] md:basis-[200px] lg:basis-[200px]">
                <ContentCard
                  title={content.title}
                  imagePath={content.imagePath}
                  type={content.type}
                  year={content.year}
                  ageRating={content.ageRating}
                  id={content.id}
                  link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                  onClick={content.onClick}
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex left-2 bg-vista-dark/30 hover:bg-vista-dark/70 border-none text-white" />
          <CarouselNext className="hidden md:flex right-2 bg-vista-dark/30 hover:bg-vista-dark/70 border-none text-white" />
        </Carousel>
      </div>
    </div>
  );
}
