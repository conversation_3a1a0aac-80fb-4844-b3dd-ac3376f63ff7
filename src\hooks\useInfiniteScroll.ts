'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ContentCardType } from '@/lib/content-utils';

interface UseInfiniteScrollOptions {
  initialItems: ContentCardType[];
  fetchMoreItems?: (page: number) => Promise<ContentCardType[]>;
  itemsPerPage?: number;
  maxItems?: number;
  cooldownMs?: number; // Cooldown period between API requests
}

/**
 * Custom hook for implementing infinite scroll functionality
 */
export function useInfiniteScroll({
  initialItems,
  fetchMoreItems,
  itemsPerPage = 10,
  maxItems = 100,
  cooldownMs = 1000 // Default 1 second cooldown between requests
}: UseInfiniteScrollOptions) {
  // Use a smaller initial set for better performance
  const [items, setItems] = useState<ContentCardType[]>(initialItems.slice(0, Math.min(initialItems.length, 8)));
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastItemRef = useRef<HTMLDivElement | null>(null);
  const lastLoadTimeRef = useRef<number>(0); // Track when the last load happened
  const isMobileRef = useRef<boolean>(false); // Track if we're on mobile
  const pendingRequestRef = useRef<boolean>(false); // Track if there's a pending request
  const maxItemsRef = useRef<number>(maxItems); // Store max items in a ref to avoid stale closures

  // Check if we're on mobile when the component mounts
  useEffect(() => {
    // Simple mobile detection
    isMobileRef.current = window.innerWidth < 768 ||
                         /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }, []);

  // Update maxItems ref when the prop changes
  useEffect(() => {
    maxItemsRef.current = maxItems;
  }, [maxItems]);

  // Function to load more items with debouncing and cooldown
  const loadMoreItems = useCallback(async () => {
    // Don't load if already loading, no more items, or no fetch function
    if (isLoading || !hasMore || !fetchMoreItems) return;

    // Check if we're in the cooldown period
    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTimeRef.current;

    if (timeSinceLastLoad < cooldownMs) {
      // If we're in cooldown but don't have a pending request, set one up
      if (!pendingRequestRef.current) {
        pendingRequestRef.current = true;
        setTimeout(() => {
          pendingRequestRef.current = false;
          // Only trigger if we're still not loading and have more items
          if (!isLoading && hasMore) {
            loadMoreItems();
          }
        }, cooldownMs - timeSinceLastLoad);
      }
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      lastLoadTimeRef.current = now; // Update last load time

      const nextPage = page + 1;
      const newItems = await fetchMoreItems(nextPage);

      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        // Use a function to update items to avoid closure issues with stale state
        setItems(prevItems => {
          // Limit the number of items we keep in memory
          // On mobile, keep fewer items to reduce memory usage
          const maxItemsToKeep = isMobileRef.current ? 12 : 24;

          // If we have too many items, remove some from the beginning
          let updatedItems;
          if (prevItems.length + newItems.length > maxItemsToKeep) {
            // Keep the most recent items and add new ones
            const itemsToKeep = prevItems.slice(-Math.max(0, maxItemsToKeep - newItems.length));
            updatedItems = [...itemsToKeep, ...newItems];
          } else {
            updatedItems = [...prevItems, ...newItems];
          }

          // Check if we've reached the maximum number of items
          if (updatedItems.length >= maxItemsRef.current) {
            setHasMore(false);
          }

          return updatedItems;
        });
        setPage(nextPage);
      }
    } catch (err) {
      console.error('Error loading more items:', err);
      setError(err instanceof Error ? err.message : 'Failed to load more items');
    } finally {
      setIsLoading(false);
    }
  }, [fetchMoreItems, hasMore, isLoading, page, cooldownMs]);

  // Set up intersection observer to detect when the last item is visible
  useEffect(() => {
    if (!fetchMoreItems) {
      setHasMore(false);
      return;
    }

    const options = {
      root: null, // Use the viewport as the root
      // Use a larger rootMargin on mobile to start loading earlier
      rootMargin: isMobileRef.current ? '150px' : '100px', // Reduced from 200px to 150px on mobile
      // Lower threshold on mobile for better performance
      threshold: isMobileRef.current ? 0.01 : 0.1 // Even lower threshold on mobile (1% visibility)
    };

    // Add debouncing to prevent multiple rapid calls
    let debounceTimeout: NodeJS.Timeout | null = null;

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMore && !isLoading && !pendingRequestRef.current) {
        // Clear any existing timeout
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }

        // Set a short debounce to prevent multiple calls during rapid scrolling
        debounceTimeout = setTimeout(() => {
          loadMoreItems();
        }, isMobileRef.current ? 150 : 50);
      }
    }, options);

    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [fetchMoreItems, hasMore, isLoading, loadMoreItems]);

  // Observe the last item when it changes
  const setLastItemElement = useCallback((node: HTMLDivElement | null) => {
    if (lastItemRef.current) {
      if (observerRef.current) {
        observerRef.current.unobserve(lastItemRef.current);
      }
    }

    lastItemRef.current = node;

    if (node && observerRef.current && hasMore) {
      observerRef.current.observe(node);
    }
  }, [hasMore]);

  // Function to manually trigger loading more items
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      loadMoreItems();
    }
  }, [hasMore, isLoading, loadMoreItems]);

  return {
    items,
    isLoading,
    hasMore,
    error,
    loadMore,
    setLastItemElement
  };
}
