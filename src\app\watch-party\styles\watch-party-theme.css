/* Watch Party Theme - Modern UI Variables */

:root {
  /* Base colors */
  --wp-bg-primary: #0f172a;
  --wp-bg-secondary: #1e293b;
  --wp-bg-tertiary: #334155;
  --wp-bg-accent: #0ea5e9;
  --wp-bg-accent-hover: #0284c7;
  --wp-bg-card: rgba(30, 41, 59, 0.8);
  --wp-bg-card-hover: rgba(51, 65, 85, 0.9);
  
  /* Text colors */
  --wp-text-primary: #f8fafc;
  --wp-text-secondary: #cbd5e1;
  --wp-text-muted: #94a3b8;
  --wp-text-accent: #38bdf8;
  
  /* Border colors */
  --wp-border-light: rgba(148, 163, 184, 0.2);
  --wp-border-medium: rgba(148, 163, 184, 0.3);
  --wp-border-accent: rgba(14, 165, 233, 0.5);
  
  /* Shadows */
  --wp-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --wp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --wp-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Animations */
  --wp-transition-fast: 150ms ease;
  --wp-transition-normal: 250ms ease;
  --wp-transition-slow: 350ms ease;
  
  /* Gradients */
  --wp-gradient-blue: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  --wp-gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --wp-gradient-card: linear-gradient(180deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.95) 100%);
  
  /* Blur effects */
  --wp-backdrop-blur: blur(8px);
  
  /* Scrollbar */
  --wp-scrollbar-width: 6px;
  --wp-scrollbar-track: rgba(15, 23, 42, 0.3);
  --wp-scrollbar-thumb: rgba(148, 163, 184, 0.3);
  --wp-scrollbar-thumb-hover: rgba(148, 163, 184, 0.5);
  
  /* Border radius */
  --wp-radius-sm: 0.25rem;
  --wp-radius-md: 0.375rem;
  --wp-radius-lg: 0.5rem;
  --wp-radius-xl: 0.75rem;
  --wp-radius-2xl: 1rem;
  --wp-radius-full: 9999px;
}

/* Custom scrollbar styles */
.wp-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--wp-scrollbar-thumb) var(--wp-scrollbar-track);
}

.wp-scrollbar::-webkit-scrollbar {
  width: var(--wp-scrollbar-width);
  height: var(--wp-scrollbar-width);
}

.wp-scrollbar::-webkit-scrollbar-track {
  background: var(--wp-scrollbar-track);
  border-radius: var(--wp-radius-full);
}

.wp-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--wp-scrollbar-thumb);
  border-radius: var(--wp-radius-full);
}

.wp-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--wp-scrollbar-thumb-hover);
}

/* Glass morphism card */
.wp-glass-card {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(148, 163, 184, 0.15);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Animations */
.wp-fade-in {
  animation: wpFadeIn 0.3s ease forwards;
}

.wp-slide-up {
  animation: wpSlideUp 0.3s ease forwards;
}

.wp-slide-down {
  animation: wpSlideDown 0.3s ease forwards;
}

@keyframes wpFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes wpSlideUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes wpSlideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
