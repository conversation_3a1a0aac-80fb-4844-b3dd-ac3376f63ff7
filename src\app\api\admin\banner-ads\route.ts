import { NextRequest, NextResponse } from 'next/server';
import { verifyAdmin } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd, { IBannerAd } from '@/models/BannerAd';

// Helper function to normalize URLs
function normalizeUrl(url: string): string {
  if (!url) return url;

  // If URL doesn't start with http:// or https://, add https://
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }

  return url;
}

/**
 * GET /api/admin/banner-ads
 * Get all banner ads with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    await ensureMongooseConnection();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status'); // 'active', 'inactive', 'expired'
    const search = searchParams.get('search');

    // Build filter object
    const filter: any = {};

    if (status === 'active') {
      filter.isActive = true;
      filter.$or = [
        { endDate: { $exists: false } },
        { endDate: null },
        { endDate: { $gte: new Date() } }
      ];
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: new Date() };
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get banner ads with pagination
    const bannerAds = await BannerAd.find(filter)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ priority: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Handle cases where createdBy user doesn't exist (development mode)
    bannerAds.forEach(banner => {
      if (!banner.createdBy) {
        banner.createdBy = {
          name: 'Development User',
          email: '<EMAIL>'
        };
      }
      if (banner.updatedBy && !banner.updatedBy.name) {
        banner.updatedBy = {
          name: 'Development User',
          email: '<EMAIL>'
        };
      }
    });

    // Get total count for pagination
    const total = await BannerAd.countDocuments(filter);

    return NextResponse.json({
      bannerAds,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching banner ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch banner ads' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/banner-ads
 * Create a new banner ad
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    await ensureMongooseConnection();

    const body = await request.json();
    console.log('Banner ad creation request body:', JSON.stringify(body, null, 2));

    // Validate required fields
    if (!body.title || !body.imageUrl) {
      return NextResponse.json(
        { error: 'Title and image URL are required' },
        { status: 400 }
      );
    }

    // Validate positions array
    if (!body.styling?.positions || !Array.isArray(body.styling.positions) || body.styling.positions.length === 0) {
      return NextResponse.json(
        { error: 'At least one display position must be selected' },
        { status: 400 }
      );
    }

    // Create banner ad data
    const bannerAdData: Partial<IBannerAd> = {
      title: body.title,
      description: body.description,
      imageUrl: body.imageUrl,
      linkUrl: body.linkUrl ? normalizeUrl(body.linkUrl) : undefined,
      isActive: body.isActive !== undefined ? body.isActive : true,
      startDate: new Date(), // Always start immediately
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      duration: body.duration,
      priority: body.priority || 1,
      styling: {
        backgroundColor: body.styling?.backgroundColor || '#1a1a1a',
        textColor: body.styling?.textColor || '#ffffff',
        titleSize: body.styling?.titleSize || '1.5rem',
        descriptionSize: body.styling?.descriptionSize || '1rem',
        borderRadius: body.styling?.borderRadius || '0.5rem',
        padding: body.styling?.padding || '1rem',
        animation: body.styling?.animation || 'fadeIn',
        animationDuration: body.styling?.animationDuration || '0.5s',
        positions: body.styling?.positions || ['top']
      },
      createdBy: adminSession.userId
    };

    // Create the banner ad
    console.log('Creating banner ad with data:', JSON.stringify(bannerAdData, null, 2));
    const bannerAd = await BannerAd.create(bannerAdData);

    // Populate the created banner ad
    const populatedBannerAd = await BannerAd.findById(bannerAd._id)
      .populate('createdBy', 'name email')
      .lean();

    // Handle case where createdBy user doesn't exist (development mode)
    if (populatedBannerAd && !populatedBannerAd.createdBy) {
      populatedBannerAd.createdBy = {
        name: 'Development User',
        email: '<EMAIL>'
      };
    }

    return NextResponse.json(populatedBannerAd, { status: 201 });

  } catch (error) {
    console.error('Error creating banner ad:', error);

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create banner ad', details: error.message },
      { status: 500 }
    );
  }
}
