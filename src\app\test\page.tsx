export default function TestPage() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Turbopack Test Page</h1>
      <p className="mb-2">This page confirms that Turbopack is working correctly.</p>
      <p className="text-green-500 font-semibold">✓ Configuration fixed successfully!</p>
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Summary of Fixes:</h2>
        <ul className="list-disc pl-5 space-y-1">
          <li>Fixed Next.js configuration to better support Turbopack</li>
          <li>Created a fallback context for WatchParty</li>
          <li>Added separate dev scripts for with/without Turbopack</li>
          <li>Fixed module import/resolution issues</li>
        </ul>
      </div>
    </div>
  )
} 