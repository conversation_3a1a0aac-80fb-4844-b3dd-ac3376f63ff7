'use client';

import { useState, useEffect, useRef } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, PictureInPicture2, X, Loader2, Film } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { motion, AnimatePresence } from 'framer-motion';
import { isBrowser, safeWindow, safeDocument } from '@/lib/browser-utils';

// Define the props interface for our custom player
interface CustomVideoPlayerProps {
  videoId?: string;
  title?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  showControls?: boolean;
  thumbnailOnly?: boolean;
  onThumbnailClick?: () => void;
  aspectRatio?: '16/9' | '4/3' | '1/1';
}

// Type definition for YouTube Player API
declare global {
  interface Window {
    YT: {
      Player: new (
        elementId: string,
        options: {
          videoId: string;
          playerVars?: {
            autoplay?: 0 | 1;
            controls?: 0 | 1;
            mute?: 0 | 1;
            rel?: 0 | 1;
            showinfo?: 0 | 1;
            modestbranding?: 0 | 1;
            playsinline?: 0 | 1;
            start?: number;
          };
          events?: {
            onReady?: (event: any) => void;
            onStateChange?: (event: any) => void;
            onError?: (event: any) => void;
          };
        }
      ) => any;
      PlayerState: {
        UNSTARTED: number;
        ENDED: number;
        PLAYING: number;
        PAUSED: number;
        BUFFERING: number;
        CUED: number;
      };
    };
    onYouTubeIframeAPIReady: () => void;
  }
}

/**
 * Custom Video Player Component
 *
 * A custom YouTube video player with our own controls and PiP functionality
 */
export default function CustomVideoPlayer({
  videoId,
  title = 'Video',
  className = '',
  autoPlay = false,
  muted = false,
  showControls = true,
  thumbnailOnly = false,
  onThumbnailClick,
  aspectRatio = '16/9'
}: CustomVideoPlayerProps) {
  // Player state
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(muted ? 0 : 100);
  const [showThumbnail, setShowThumbnail] = useState(thumbnailOnly);
  const [error, setError] = useState<string | null>(null);
  const [controlsVisible, setControlsVisible] = useState(false); // Start with controls hidden
  const [isPiP, setIsPiP] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isApiReady, setIsApiReady] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<HTMLDivElement>(null);
  const playerInstanceRef = useRef<any>(null);
  const pipContainerRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Format time for display (MM:SS)
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Load YouTube API & Set Ready State
  useEffect(() => {
    // Only run in browser environment
    if (!isBrowser()) {
      console.log('Not in browser environment, skipping YT API loading.');
      return;
    }

    const win = safeWindow();
    const doc = safeDocument();

    if (!win || !doc) {
      console.log('Window or document not available, skipping YT API loading.');
      return;
    }

    // Check if API is already loaded
    if (win.YT && win.YT.Player) {
      console.log('YT API already loaded.');
      setIsApiReady(true);
      return;
    }

    console.log('Setting up YT API loading...');
    // Define the callback function
    win.onYouTubeIframeAPIReady = () => {
      console.log('onYouTubeIframeAPIReady callback fired.');
      setIsApiReady(true);
    };

    // Check if script tag already exists
    if (!doc.querySelector('script[src="https://www.youtube.com/iframe_api"]')) {
       console.log('Injecting YT API script tag...');
       const tag = doc.createElement('script');
       tag.src = 'https://www.youtube.com/iframe_api';
       doc.body.appendChild(tag); // Append to body is generally safer
    } else {
       console.log('YT API script tag already exists.');
       // If script exists but API not ready, callback might have been missed.
       // We rely on the isApiReady state check in the other useEffect.
    }

    // Cleanup function
    return () => {
       // Don't nullify the global callback if other instances might need it
       // win.onYouTubeIframeAPIReady = () => {};
       console.log('YT API Loader cleanup (callback not removed globally).');
    };
  }, []); // Run only once on mount

  // Initialize YouTube player only when API is ready, videoId is valid, and target div exists
  useEffect(() => {
    console.log('[initPlayer Effect Check]', { isApiReady, videoId, hasPlayerRef: !!playerRef.current });
    if (isApiReady && videoId && playerRef.current) {
      console.log('Conditions met, calling initPlayer...');
      initPlayer();
    } else {
      console.log('Conditions not met for initPlayer.');
    }

    // Cleanup function to destroy player when videoId changes or component unmounts
    return () => {
      if (playerInstanceRef.current) {
        console.log('[initPlayer Effect Cleanup] Destroying player instance.');
        try {
           playerInstanceRef.current.destroy();
           playerInstanceRef.current = null;
        } catch(e) {
           console.error('Error destroying player in cleanup:', e);
        }
      }
    };
  // Depend on API readiness, videoId, and the ref itself (or its existence)
  }, [isApiReady, videoId, playerRef.current]);

  // Initialize the YouTube player
  const initPlayer = () => {
    if (!videoId || !playerRef.current) {
        console.warn("initPlayer: Aborting, videoId or playerRef missing.");
        return;
    }

    // Ensure the previous instance is destroyed before creating a new one
    if (playerInstanceRef.current) {
        console.log("initPlayer: Destroying previous player instance.");
        try {
            playerInstanceRef.current.destroy();
            playerInstanceRef.current = null;
        } catch (e) {
            console.error("Error destroying previous player instance:", e);
        }
    }

    console.log(`initPlayer: Initializing player for videoId: ${videoId} in element: youtube-player`);
    try {
      // Use the element ID string directly
      playerInstanceRef.current = new window.YT.Player("youtube-player", {
        videoId,
        playerVars: {
          autoplay: autoPlay && !thumbnailOnly ? 1 : 0,
          controls: 0, // We'll use our own controls
          mute: muted ? 1 : 0,
          rel: 0, // Don't show related videos
          modestbranding: 1,
          playsinline: 1
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
          onError: onPlayerError
        }
      });
      console.log("initPlayer: Player instance created.");
    } catch (err) {
      console.error('Error initializing YouTube player:', err);
      setError('Failed to load video player');
      setIsLoading(false);
    }
  };

  // When player is ready
  const onPlayerReady = (event: any) => {
    setIsLoading(false);
    setDuration(event.target.getDuration());

    if (muted) {
      event.target.mute();
      setIsMuted(true);
    } else {
      event.target.unMute();
      setVolume(event.target.getVolume());
      setIsMuted(false);
    }

    // Start playing if autoPlay is true and not in thumbnail mode
    if (autoPlay && !thumbnailOnly) {
      event.target.playVideo();
    }
  };

  // Handle player state changes
  const onPlayerStateChange = (event: any) => {
    const playerState = event.data;
    const isNowPlaying = playerState === window.YT.PlayerState.PLAYING;

    // Update playing state
    setIsPlaying(isNowPlaying);

    // Update time and duration
    if (isNowPlaying) {
      startTimeTracking();
    } else {
      stopTimeTracking();

      // When pausing, check if we should hide controls
      // This helps fix the issue where controls remain visible after pausing
      if (!isPiP) {
        const container = containerRef.current;
        const isHovering = container?.matches(':hover');

        // If not hovering, hide controls after a short delay
        if (!isHovering) {
          setTimeout(() => {
            setControlsVisible(false);
          }, 3000);
        }
      }
    }
  };

  // Handle player errors
  const onPlayerError = (event: any) => {
    console.error('YouTube player error:', event);
    setError('Error playing video. Please try again later.');
    setIsLoading(false);
  };

  // Update current time periodically while playing
  const startTimeTracking = () => {
    if (timeoutRef.current) {
      clearInterval(timeoutRef.current);
    }

    timeoutRef.current = setInterval(() => {
      if (playerInstanceRef.current) {
        const current = playerInstanceRef.current.getCurrentTime();
        setCurrentTime(current);
      }
    }, 1000); // Update every second
  };

  // Stop time tracking when paused
  const stopTimeTracking = () => {
    if (timeoutRef.current) {
      clearInterval(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // Toggle play/pause
  const togglePlay = () => {
    if (!playerInstanceRef.current) return;

    if (isPlaying) {
      playerInstanceRef.current.pauseVideo();
    } else {
      playerInstanceRef.current.playVideo();
    }
  };

  // Toggle mute
  const toggleMute = () => {
    if (!playerInstanceRef.current) return;

    if (isMuted) {
      playerInstanceRef.current.unMute();
      setIsMuted(false);
    } else {
      playerInstanceRef.current.mute();
      setIsMuted(true);
    }
  };

  // Set volume
  const handleVolumeChange = (value: number[]) => {
    if (!playerInstanceRef.current) return;

    const newVolume = value[0];
    setVolume(newVolume);
    playerInstanceRef.current.setVolume(newVolume);

    if (newVolume === 0) {
      playerInstanceRef.current.mute();
      setIsMuted(true);
    } else if (isMuted) {
      playerInstanceRef.current.unMute();
      setIsMuted(false);
    }
  };

  // Seek to specific time
  const handleSeek = (value: number[]) => {
    if (!playerInstanceRef.current) return;

    const seekTime = (value[0] / 100) * duration;
    playerInstanceRef.current.seekTo(seekTime, true);
    setCurrentTime(seekTime);
  };

  // Updated Handle fullscreen
  const toggleFullscreen = () => {
    if (!isBrowser()) return;

    const doc = safeDocument();
    if (!doc || !containerRef.current) return;

    if (!doc.fullscreenElement) {
      containerRef.current.requestFullscreen();
    } else {
      if (doc.exitFullscreen) {
        doc.exitFullscreen();
      }
    }
  };

  // Hook to update fullscreen state
  useFullscreenChange(setIsFullscreen);

  // Refined Toggle Picture-in-Picture mode - Simpler state toggle
  const togglePiP = () => {
    const newPipState = !isPiP;

    if (newPipState) {
      // Entering PiP mode
      console.log('Requesting PiP mode');
      // Exit fullscreen if active before entering PiP
      if (isFullscreen && document.exitFullscreen) {
        document.exitFullscreen();
      }
      // Set state, the useEffect below will handle the move
      setIsPiP(true);
    } else {
      // Exiting PiP mode
      console.log('Requesting exit from PiP mode');
      // Set state, the useEffect below will handle the move
      setIsPiP(false);
      // Only show controls if hovering
      const container = containerRef.current;
      const isHovering = container?.matches(':hover') || false;
      setControlsVisible(isHovering);
    }
  };

  // useEffect to handle the PiP state changes
  useEffect(() => {
    // Get references to the player elements
    const mainPlayerContainer = playerRef.current;
    const pipMotionContainer = pipContainerRef.current;
    const pipIframeTarget = pipMotionContainer?.querySelector('#pip-iframe-target') as HTMLDivElement | null;
    const playerInstance = playerInstanceRef.current;

    // Get the iframe element
    let iframe: HTMLIFrameElement | null = null;
    try {
      if (playerInstance && typeof playerInstance.getIframe === 'function') {
        iframe = playerInstance.getIframe();
      }
    } catch (e) {
      console.error("Error getting iframe:", e);
    }

    // Log the current state for debugging
    console.log('[PiP Effect]', {
      isPiP,
      mainPlayerContainerExists: !!mainPlayerContainer,
      pipMotionContainerExists: !!pipMotionContainer,
      pipIframeTargetExists: !!pipIframeTarget,
      iframeExists: !!iframe,
      iframeParentId: iframe?.parentElement?.id,
      playerInstanceReady: !!playerInstance
    });

    // Check if we have all the required elements
    if (!mainPlayerContainer || !iframe || !playerInstance) {
      console.warn('PiP Effect: Required elements missing');
      return;
    }

    // Save the current playback state and time
    let wasPlaying = false;
    let currentPlayerTime = 0;

    try {
      wasPlaying = playerInstance.getPlayerState() === window.YT.PlayerState.PLAYING;
      currentPlayerTime = playerInstance.getCurrentTime();
      console.log('PiP Effect: Current state -', wasPlaying ? 'playing' : 'paused', 'at time', currentPlayerTime);
    } catch (e) {
      console.error('PiP Effect: Error getting player state:', e);
    }

    // Handle PiP mode toggle
    if (isPiP) {
      // Entering PiP mode - COMPLETELY NEW APPROACH
      // Instead of trying to move the iframe, we'll create a new player instance in the PiP container

      if (!pipIframeTarget) {
        console.warn('PiP Effect: PiP target container not available');
        setIsPiP(false);
        return;
      }

      console.log('PiP Effect: Entering PiP mode with new approach');
      try {
        // Store the current time and state before destroying the player
        let timeBeforeEnter = 0;
        let volumeBeforeEnter = 50;
        let isMutedBeforeEnter = false;
        let wasPlayingBeforeEnter = false;
        let videoId = '';

        try {
          if (playerInstance) {
            // Check if methods exist before calling them
            if (typeof playerInstance.getCurrentTime === 'function') {
              timeBeforeEnter = playerInstance.getCurrentTime() || 0;
            }

            if (typeof playerInstance.getVolume === 'function') {
              volumeBeforeEnter = playerInstance.getVolume() || 50;
            }

            if (typeof playerInstance.isMuted === 'function') {
              isMutedBeforeEnter = playerInstance.isMuted() || false;
            }

            if (typeof playerInstance.getPlayerState === 'function') {
              wasPlayingBeforeEnter = playerInstance.getPlayerState() === window.YT.PlayerState.PLAYING;
            }

            if (typeof playerInstance.getVideoData === 'function') {
              videoId = playerInstance.getVideoData()?.video_id || '';
            }

            console.log('PiP Effect: Captured state before enter -', {
              time: timeBeforeEnter,
              volume: volumeBeforeEnter,
              muted: isMutedBeforeEnter,
              playing: wasPlayingBeforeEnter,
              videoId
            });

            // Destroy the current player instance
            if (typeof playerInstance.destroy === 'function') {
              playerInstance.destroy();
              playerInstanceRef.current = null;
            }
          }
        } catch (e) {
          console.error('PiP Effect: Error capturing state before enter:', e);
        }

        // Clear the main container
        if (mainPlayerContainer) {
          while (mainPlayerContainer.firstChild) {
            mainPlayerContainer.removeChild(mainPlayerContainer.firstChild);
          }
        }

        // Create a new player in the PiP container
        if (videoId) {
          console.log('PiP Effect: Creating new player in PiP container with video ID:', videoId);

          // Clear the PiP container first
          while (pipIframeTarget.firstChild) {
            pipIframeTarget.removeChild(pipIframeTarget.firstChild);
          }

          // Create a new player instance in the PiP container
          setTimeout(() => {
            try {
              playerInstanceRef.current = new window.YT.Player("pip-iframe-target", {
                videoId: videoId,
                playerVars: {
                  autoplay: wasPlayingBeforeEnter ? 1 : 0,
                  start: Math.floor(timeBeforeEnter),
                  controls: 0,
                  mute: isMutedBeforeEnter ? 1 : 0,
                  rel: 0,
                  modestbranding: 1,
                  playsinline: 1
                },
                events: {
                  onReady: (event) => {
                    console.log('PiP Effect: New PiP player ready');
                    event.target.setVolume(volumeBeforeEnter);

                    // Update UI state immediately
                    setIsPlaying(wasPlayingBeforeEnter);
                    // Only show controls if hovering or paused
                    const container = containerRef.current;
                    const isHovering = container?.matches(':hover') || false;
                    setControlsVisible(isHovering);

                    // Make sure the player is at the right position
                    setTimeout(() => {
                      try {
                        event.target.seekTo(timeBeforeEnter, true);

                        if (wasPlayingBeforeEnter) {
                          event.target.playVideo();
                        }

                        console.log('PiP Effect: Successfully restored state in PiP player');
                      } catch (e) {
                        console.error('PiP Effect: Error in delayed seek for PiP:', e);
                      }
                    }, 500);
                  },
                  onStateChange: (event) => {
                    // Update playing state when player state changes
                    console.log('PiP Effect: PiP player state changed:', event.data);
                    setIsPlaying(event.data === window.YT.PlayerState.PLAYING);

                    // Start/stop time tracking based on state
                    if (event.data === window.YT.PlayerState.PLAYING) {
                      startTimeTracking();
                    } else {
                      stopTimeTracking();
                    }
                  },
                  onError: (event) => {
                    console.error('PiP Effect: Error in PiP player:', event.data);
                  }
                }
              });
            } catch (e) {
              console.error('PiP Effect: Error creating PiP player:', e);
              setIsPiP(false);
            }
          }, 100);
        } else {
          console.error('PiP Effect: No video ID available for creating PiP player');
          setIsPiP(false);
        }
      } catch (err) {
        console.error('PiP Effect: Critical error in enter handler:', err);
        setIsPiP(false);
      }
    } else {
      // Exiting PiP mode - COMPLETELY NEW APPROACH
      // Instead of trying to move the iframe, we'll create a new player instance

      console.log('PiP Effect: Exiting PiP mode with new approach');
      try {
        // Store the current time and state before destroying the player
        let timeBeforeExit = 0;
        let volumeBeforeExit = 50;
        let isMutedBeforeExit = false;
        let wasPlayingBeforeExit = false;
        let videoId = '';

        try {
          if (playerInstance) {
            // Check if methods exist before calling them
            if (typeof playerInstance.getCurrentTime === 'function') {
              timeBeforeExit = playerInstance.getCurrentTime() || 0;
            }

            if (typeof playerInstance.getVolume === 'function') {
              volumeBeforeExit = playerInstance.getVolume() || 50;
            }

            if (typeof playerInstance.isMuted === 'function') {
              isMutedBeforeExit = playerInstance.isMuted() || false;
            }

            if (typeof playerInstance.getPlayerState === 'function') {
              wasPlayingBeforeExit = playerInstance.getPlayerState() === window.YT.PlayerState.PLAYING;
            }

            if (typeof playerInstance.getVideoData === 'function') {
              videoId = playerInstance.getVideoData()?.video_id || '';
            }

            console.log('PiP Effect: Captured state before exit -', {
              time: timeBeforeExit,
              volume: volumeBeforeExit,
              muted: isMutedBeforeExit,
              playing: wasPlayingBeforeExit,
              videoId
            });

            // Destroy the current player instance
            if (typeof playerInstance.destroy === 'function') {
              playerInstance.destroy();
              playerInstanceRef.current = null;
            }
          }
        } catch (e) {
          console.error('PiP Effect: Error capturing state before exit:', e);
        }

        // Clear the PiP container
        if (pipIframeTarget) {
          while (pipIframeTarget.firstChild) {
            pipIframeTarget.removeChild(pipIframeTarget.firstChild);
          }
        }

        // Create a new player in the main container
        if (videoId) {
          console.log('PiP Effect: Creating new player in main container with video ID:', videoId);

          // Clear the main container first
          if (mainPlayerContainer) {
            while (mainPlayerContainer.firstChild) {
              mainPlayerContainer.removeChild(mainPlayerContainer.firstChild);
            }
          }

          // Create a new player instance
          setTimeout(() => {
            try {
              playerInstanceRef.current = new window.YT.Player("youtube-player", {
                videoId: videoId,
                playerVars: {
                  autoplay: wasPlayingBeforeExit ? 1 : 0,
                  start: Math.floor(timeBeforeExit),
                  controls: 0,
                  mute: isMutedBeforeExit ? 1 : 0,
                  rel: 0,
                  modestbranding: 1,
                  playsinline: 1
                },
                events: {
                  onReady: (event) => {
                    console.log('PiP Effect: New main player ready');
                    event.target.setVolume(volumeBeforeExit);

                    // Update UI state immediately
                    setIsPlaying(wasPlayingBeforeExit);
                    // Only show controls if hovering or paused
                    const container = containerRef.current;
                    const isHovering = container?.matches(':hover') || false;
                    setControlsVisible(isHovering);

                    // Make sure the player is at the right position
                    setTimeout(() => {
                      try {
                        event.target.seekTo(timeBeforeExit, true);

                        if (wasPlayingBeforeExit) {
                          event.target.playVideo();
                        }

                        console.log('PiP Effect: Successfully restored state in new player');
                      } catch (e) {
                        console.error('PiP Effect: Error in delayed seek:', e);
                      }
                    }, 500);
                  },
                  onStateChange: (event) => {
                    // Update playing state when player state changes
                    console.log('PiP Effect: New player state changed:', event.data);
                    setIsPlaying(event.data === window.YT.PlayerState.PLAYING);

                    // Start/stop time tracking based on state
                    if (event.data === window.YT.PlayerState.PLAYING) {
                      startTimeTracking();
                    } else {
                      stopTimeTracking();
                    }
                  },
                  onError: (event) => {
                    console.error('PiP Effect: Error in new player:', event.data);
                  }
                }
              });
            } catch (e) {
              console.error('PiP Effect: Error creating new player:', e);
              setError('Failed to restore video player');
            }
          }, 100);
        } else {
          console.error('PiP Effect: No video ID available for creating new player');
          setError('Failed to restore video');
        }
      } catch (err) {
        console.error('PiP Effect: Critical error in exit handler:', err);
        setError('Failed to exit picture-in-picture mode');
        // If we can't properly exit, stay in PiP mode
        setIsPiP(true);
      }
    }
  }, [isPiP]);

  // Handle thumbnail click
  const handleThumbnailClick = () => {
    if (thumbnailOnly) {
      setShowThumbnail(false);

      if (onThumbnailClick) {
        onThumbnailClick();
      }
    }
  };

  // Show/hide controls on mouse movement
  useEffect(() => {
    if (!showControls) return;

    let timeout: NodeJS.Timeout;

    const handleMouseMove = () => {
      setControlsVisible(true);

      clearTimeout(timeout);
      timeout = setTimeout(() => {
        // Hide controls after timeout, regardless of playing state
        setControlsVisible(false);
      }, 2000); // Reduced timeout for faster hiding
    };

    const handleMouseLeave = () => {
      clearTimeout(timeout);
      // Always hide controls on mouse leave, regardless of playing state
      setControlsVisible(false);
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    // Force hide controls when player is paused and mouse is not over the player
    // This helps fix the issue where controls remain visible after exiting PiP
    const forceHideTimeout = setTimeout(() => {
      // Always check if we should hide controls, regardless of playing state
      if (!isPiP) {
        const isHovering = container?.matches(':hover');
        if (!isHovering) {
          setControlsVisible(false);
        }
      }
    }, 1500); // Reduced timeout for faster hiding

    return () => {
      clearTimeout(timeout);
      clearTimeout(forceHideTimeout);
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [showControls, isPlaying, isPiP]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearInterval(timeoutRef.current);
      }

      if (playerInstanceRef.current) {
        playerInstanceRef.current.destroy();
      }
    };
  }, []);

  // If no videoId, show placeholder
  if (!videoId) {
    return (
      <div
        className={cn(
          "flex flex-col justify-center items-center bg-vista-dark-lighter rounded-lg overflow-hidden",
          className
        )}
        style={{ aspectRatio }}
      >
        <Film className="w-16 h-16 text-vista-light/30 mb-4" />
        <h3 className="text-xl font-bold text-vista-light mb-2">No Video Available</h3>
        <p className="text-vista-light/70 text-center max-w-md">
          We couldn't find a video for this content.
        </p>
      </div>
    );
  }

  // Get thumbnail URL
  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;

  return (
    <>
      {/* Main Player Container */}
      <div
        ref={containerRef}
        className={cn(
          "relative bg-black rounded-lg overflow-hidden",
          className
        )}
        style={{ aspectRatio }}
        onMouseEnter={() => !thumbnailOnly && !showThumbnail && setControlsVisible(true)}
        onMouseLeave={() => setControlsVisible(false)}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex justify-center items-center bg-vista-dark z-20">
            <Loader2 className="w-12 h-12 text-vista-blue animate-spin" />
          </div>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <div className="absolute inset-0 flex flex-col justify-center items-center bg-vista-dark z-20">
            <Film className="w-16 h-16 text-vista-light/30 mb-4" />
            <h3 className="text-xl font-bold text-vista-light mb-2">Video Error</h3>
            <p className="text-vista-light/70 text-center max-w-md">
              {error}
            </p>
          </div>
        )}

        {/* Thumbnail Mode */}
        {showThumbnail ? (
          <div
            className="relative w-full h-full cursor-pointer group z-10"
            onClick={handleThumbnailClick}
          >
            <img
              src={thumbnailUrl}
              alt={title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="w-16 h-16 rounded-full bg-vista-blue/90 flex justify-center items-center">
                <Play className="h-8 w-8 text-white" />
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* YouTube Player Container - always render but conditionally populate */}
            <div id="youtube-player" ref={playerRef} className="w-full h-full">
              {/* Player iframe gets injected here by YT API initially */}
              {/* And moved here from PiP on exit */}
            </div>
          </>
        )}

        {/* Custom Controls - only show in main player when not in PiP */}
        {showControls && !showThumbnail && !isPiP && (
          <AnimatePresence>
            {controlsVisible && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 z-20"
              >
                {/* Progress Bar */}
                <div className="px-2 mb-1">
                  <Slider
                    value={[duration ? (currentTime / duration) * 100 : 0]}
                    onValueChange={handleSeek}
                    max={100}
                    step={0.1}
                    className="cursor-pointer"
                  />
                </div>

                <div className="flex items-center justify-between px-2">
                  {/* Left Controls: Play/Pause, Volume */}
                  <div className="flex items-center space-x-2">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="text-white h-8 w-8 p-1.5"
                      onClick={togglePlay}
                    >
                      {isPlaying ? <Pause className="h-full w-full" /> : <Play className="h-full w-full" />}
                    </Button>

                    <div className="flex items-center space-x-1">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="text-white h-8 w-8 p-1.5"
                        onClick={toggleMute}
                      >
                        {isMuted ? <VolumeX className="h-full w-full" /> : <Volume2 className="h-full w-full" />}
                      </Button>

                      <div className="w-20 hidden sm:block">
                        <Slider
                          value={[isMuted ? 0 : volume]}
                          onValueChange={handleVolumeChange}
                          max={100}
                          step={1}
                          className="cursor-pointer"
                        />
                      </div>
                    </div>

                    {/* Time Display */}
                    <span className="text-white text-sm">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>

                  {/* Right Controls: Settings, PiP, Fullscreen */}
                  <div className="flex items-center space-x-2">

                    <Button
                      size="icon"
                      variant="ghost"
                      className="text-white h-8 w-8 p-1.5"
                      onClick={togglePiP}
                      title="Picture-in-Picture"
                    >
                      <PictureInPicture2 className="h-full w-full" />
                    </Button>

                    <Button
                      size="icon"
                      variant="ghost"
                      className="text-white h-8 w-8 p-1.5"
                      onClick={toggleFullscreen}
                      title="Fullscreen"
                    >
                      <Maximize className="h-full w-full" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>

      {/* Picture-in-Picture floating window */}
      <AnimatePresence>
        {isPiP && (
          <motion.div
            ref={pipContainerRef}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 50, scale: 0.8 }}
            transition={{ type: "spring", damping: 20, stiffness: 200 }}
            className="fixed bottom-6 right-6 z-[100] shadow-2xl rounded-lg overflow-hidden bg-black"
            style={{ width: "320px", height: "180px" }}
            drag
            dragConstraints={{ left: -800, right: 800, top: -400, bottom: 400 }}
            dragElastic={0.1}
            dragTransition={{ bounceStiffness: 600, bounceDamping: 20 }}
            dragMomentum={false}
          >
            {/* Stable target div for the iframe */}
            <div id="pip-iframe-target" className="w-full h-full absolute inset-0"></div>

            {/* PiP Top Gradient Bar (for drag handle) */}
            <div
              className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-black/80 to-transparent cursor-move z-30 flex items-center justify-center"
            >
              <div className="w-16 h-1 bg-white/30 rounded-full mt-1"></div>
            </div>

            {/* PiP Close Button */}
            <Button
              size="icon"
              variant="ghost"
              className="absolute top-1 right-1 z-40 bg-black/50 hover:bg-black/70 text-white rounded-full h-6 w-6 p-1"
              onClick={togglePiP}
              title="Close Picture-in-Picture"
            >
              <X className="h-full w-full" />
            </Button>

            {/* PiP Controls */}
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 z-40"
              >
                <div className="flex items-center justify-between px-2">
                  {/* Simple Play/Pause Control */}
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-white h-6 w-6 p-1"
                    onClick={togglePlay}
                  >
                    {isPlaying ? <Pause className="h-full w-full" /> : <Play className="h-full w-full" />}
                  </Button>

                  {/* Time Display */}
                  <span className="text-white text-xs">
                    {formatTime(currentTime)}
                  </span>
                </div>
              </motion.div>
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Hook to handle fullscreen change events
function useFullscreenChange(callback: (isFullscreen: boolean) => void) {
  useEffect(() => {
    // Only run in browser environment
    if (!isBrowser()) return;

    const doc = safeDocument();
    if (!doc) return;

    const handleFullscreenChange = () => {
      callback(!!doc.fullscreenElement);
    };

    doc.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      doc.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [callback]);
}