'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  fetchLatestMovies, 
  fetchLatestShows, 
  fetchLatestEpisodes,
  VidSrcLatestItem,
  VidSrcLatestEpisode,
  VIDSRC_DOMAINS
} from '@/lib/vidsrc-api';
import { 
  AlertCircle, 
  RefreshCw,
  Server,
  Database
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

// Extend the VidSrc interfaces to include TMDb ID
declare module '@/lib/vidsrc-api' {
  interface VidSrcLatestItem {
    tmdb_id?: string;
    posterUrl?: string;
    releaseDate?: string;
  }
  
  interface VidSrcLatestEpisode {
    tmdb_id?: string;
    posterUrl?: string;
    releaseDate?: string;
    showTitle?: string;
  }
}

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState('movies');
  const [movies, setMovies] = useState<VidSrcLatestItem[]>([]);
  const [shows, setShows] = useState<VidSrcLatestItem[]>([]);
  const [episodes, setEpisodes] = useState<VidSrcLatestEpisode[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<'tmdb' | 'vidsrc' | 'mock'>('tmdb');
  const router = useRouter();

  useEffect(() => {
    setLoading(true);
    setError(null);
    
    // First try to load real data
    const loadRealData = async () => {
      try {
        await loadContent(dataSource);
      } catch (error) {
        // If real data fails on initial load and we don't have data, try loading mock data
        console.log('First data load failed, trying mock data');
        const currentItems = activeTab === 'movies' 
          ? movies 
          : activeTab === 'shows' 
            ? shows 
            : episodes;
          
        if (currentItems.length === 0) {
          await loadContent('mock');
        }
      }
    };
    
    loadRealData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, page, selectedDomain, dataSource]);

  const loadContent = async (source: 'tmdb' | 'vidsrc' | 'mock' = 'tmdb') => {
    try {
      setLoading(true);
      let apiUrl = `/api/vidsrc?type=${activeTab}&page=${page}`;
      
      // Add domain param if one is manually selected
      if (selectedDomain) {
        apiUrl += `&domain=${selectedDomain}`;
      }
      
      // Add data source params
      if (source === 'mock') {
        apiUrl += `&mock=true`;
      } else if (source === 'vidsrc') {
        apiUrl += `&tmdb=false`;
      }
      
      console.log(`Fetching from API: ${apiUrl}`);
      const response = await fetch(apiUrl);
      
      // If response is not OK, handle the error
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch ${activeTab}: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log(`API response for ${activeTab}:`, responseData);
      
      // Additional error checking for empty or invalid responses
      if (responseData === null || responseData === undefined) {
        throw new Error(`Empty response received for ${activeTab}`);
      }
      
      // Handle both array and object responses
      let dataArray: any[] = [];
      
      if (Array.isArray(responseData)) {
        console.log(`Received array response with ${responseData.length} items`);
        dataArray = responseData;
      } else if (typeof responseData === 'object') {
        // Check if the object contains an array we can use
        console.log('Received object response, looking for array properties...');
        
        // Try common array properties in API responses
        const possibleArrayKeys = ['results', 'data', 'items', 'response', 'content', 'movies', 'shows', 'episodes'];
        
        for (const key of possibleArrayKeys) {
          if (Array.isArray(responseData[key])) {
            console.log(`Found array at key '${key}' with ${responseData[key].length} items`);
            dataArray = responseData[key];
            break;
          }
        }
        
        // If we still don't have an array, extract valid content items from the object
        if (dataArray.length === 0) {
          const contentItems = Object.values(responseData).filter(item => 
            item && typeof item === 'object' && ('id' in item || 'title' in item || 'imdb_id' in item || 'tmdb_id' in item)
          );
          
          if (contentItems.length > 0) {
            console.log(`Extracted ${contentItems.length} content items from object`);
            dataArray = contentItems;
          }
        }
      }
      
      if (dataArray.length === 0) {
        console.log(`No usable data found in response for ${activeTab}`);
        setError(`No ${activeTab} found. Try a different domain or page.`);
        
        if (activeTab === 'movies') setMovies([]);
        else if (activeTab === 'shows') setShows([]);
        else if (activeTab === 'episodes') setEpisodes([]);
        
        return;
      }
      
      console.log(`Processing ${dataArray.length} ${activeTab} items`);
      
      // Process the data array
      const processedData = dataArray.map(item => {
        // Check if each item has required fields
        if (!item || (!item.id && !item.imdb_id && !item.tmdb_id)) {
          console.warn('Item missing required ID field:', item);
          
          // Try to fix missing IDs if possible
          if (item.imdb_id && !item.id) {
            item.id = item.imdb_id;
          } else if (item.tmdb_id && !item.id) {
            item.id = item.tmdb_id.toString();
          }
        }
        
        // Process based on content type
        const id = item.id || item.imdb_id || item.tmdb_id || '';
        const idType = id.startsWith('tt') ? 'imdb' as const : 'tmdb' as const;
        
        if (activeTab === 'episodes') {
          return {
            id: id,
            idType,
            title: item.title || 'Unknown Title',
            year: item.year ? parseInt(item.year) : undefined,
            poster: item.poster || item.posterUrl,
            addedDate: item.added || item.releaseDate,
            season: item.season ? parseInt(item.season) : 1,
            episode: item.episode ? parseInt(item.episode) : 1,
            showTitle: item.show_title || item.showTitle,
            tmdb_id: item.tmdb_id || undefined
          } as VidSrcLatestEpisode;
        } else {
          return {
            id: id,
            idType,
            title: item.title || 'Unknown Title',
            year: item.year ? parseInt(item.year) : undefined,
            poster: item.poster || item.posterUrl,
            addedDate: item.added || item.releaseDate,
            tmdb_id: item.tmdb_id || undefined
          } as VidSrcLatestItem;
        }
      });
      
      console.log(`Processed data for ${activeTab}:`, processedData.slice(0, 2));
      
      // Update the appropriate state based on content type
      if (activeTab === 'movies') {
        setMovies(processedData as VidSrcLatestItem[]);
        if (processedData.length > 0) setError(null);
      } else if (activeTab === 'shows') {
        setShows(processedData as VidSrcLatestItem[]);
        if (processedData.length > 0) setError(null);
      } else if (activeTab === 'episodes') {
        setEpisodes(processedData as VidSrcLatestEpisode[]);
        if (processedData.length > 0) setError(null);
      }
    } catch (error) {
      console.error(`Error loading ${activeTab}:`, error);
      setError(error instanceof Error ? error.message : 'Failed to load content');
      
      // Clear the current data when there's an error
      if (activeTab === 'movies') setMovies([]);
      else if (activeTab === 'shows') setShows([]);
      else if (activeTab === 'episodes') setEpisodes([]);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(1); // Reset to page 1 when changing tabs
  };

  const handleWatchContent = (item: VidSrcLatestItem | VidSrcLatestEpisode) => {
    // For episodes, need to handle the special case with season/episode
    if ('season' in item && 'episode' in item) {
      router.push(`/watch?id=${item.id}&idType=${item.idType}&type=tv&season=${item.season}&episode=${item.episode}`);
    } else {
      router.push(`/watch?id=${item.id}&idType=${item.idType}&type=${activeTab === 'movies' ? 'movie' : 'tv'}`);
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    setPage(page + 1);
  };

  const handleRefresh = () => {
    setError(null);
    loadContent(dataSource);
  };

  const handleMockData = () => {
    setError(null);
    loadContent('mock');
  };

  const handleDomainChange = (domain: string) => {
    setSelectedDomain(domain === 'auto' ? null : domain);
  };

  // For the Select component's value
  const selectValue = selectedDomain || 'auto';

  // Helper to render content cards
  const renderContent = (items: VidSrcLatestItem[] | VidSrcLatestEpisode[]) => {
    if (loading) {
      return Array(10).fill(0).map((_, i) => (
        <Card key={`skeleton-${i}`} className="w-full overflow-hidden">
          <CardHeader>
            <Skeleton className="h-6 w-3/4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[240px] w-full" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-full" />
          </CardFooter>
        </Card>
      ));
    }

    if (items.length === 0) {
      return (
        <div className="col-span-full text-center p-8">
          <p>No content available. Try a different page or domain.</p>
        </div>
      );
    }

    return items.map((item, index) => (
      <Card key={`${item.id}-${index}`} className="w-full overflow-hidden">
        <CardHeader>
          <CardTitle className="text-lg truncate">
            {item.title}
            {item.year && ` (${item.year})`}
          </CardTitle>
          {'showTitle' in item && item.showTitle && (
            <p className="text-sm text-muted-foreground">{item.showTitle}</p>
          )}
          {'season' in item && 'episode' in item && (
            <p className="text-sm text-muted-foreground">S{item.season} E{item.episode}</p>
          )}
        </CardHeader>
        <CardContent>
          {item.poster ? (
            <img 
              src={item.poster} 
              alt={item.title} 
              className="w-full h-[240px] object-cover rounded-md"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-poster.svg';
              }}
            />
          ) : (
            <div className="w-full h-[240px] bg-muted flex items-center justify-center rounded-md">
              <img 
                src="/placeholder-poster.svg" 
                alt="No poster available" 
                className="w-full h-full object-contain" 
              />
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button 
            className="w-full" 
            onClick={() => handleWatchContent(item)}
          >
            Watch Now
          </Button>
          {'tmdb_id' in item && (
            <p className="text-xs text-muted-foreground text-center">
              TMDb ID: {item.tmdb_id}
            </p>
          )}
        </CardFooter>
      </Card>
    ));
  };

  // Content for the current tab
  const currentItems = activeTab === 'movies' 
    ? movies 
    : activeTab === 'shows' 
      ? shows 
      : episodes;

  return (
    <div className="container py-6">
      <h1 className="text-3xl font-bold mb-6">Discover</h1>
      
      {/* VidSrc API Notice */}
      <Alert variant="default" className="mb-6 border-blue-500/30 bg-blue-900/10 text-blue-400">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>About Content Sources</AlertTitle>
        <AlertDescription>
          <p>StreamVista can use both TMDb API (for high-quality metadata) and VidSrc API (for streaming links).</p>
          <p className="mt-2 text-sm">
            Select your preferred data source below:
          </p>
          <ul className="list-disc list-inside mt-1 text-sm space-y-1">
            <li>TMDb: Higher quality posters and metadata (recommended)</li>
            <li>VidSrc: Direct streaming links but less reliable</li>
            <li>Mock: Demo data for testing when APIs are unavailable</li>
          </ul>
        </AlertDescription>
      </Alert>
      
      {/* Controls */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4 justify-between items-start mb-4">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span>Data Source:</span>
            <ToggleGroup 
              type="single" 
              defaultValue="tmdb" 
              value={dataSource} 
              onValueChange={(value) => value && setDataSource(value as 'tmdb' | 'vidsrc' | 'mock')}
              className="border rounded-md"
            >
              <ToggleGroupItem value="tmdb" aria-label="Use TMDb API">TMDb</ToggleGroupItem>
              <ToggleGroupItem value="vidsrc" aria-label="Use VidSrc API">VidSrc</ToggleGroupItem>
              <ToggleGroupItem value="mock" aria-label="Use Mock Data">Mock</ToggleGroupItem>
            </ToggleGroup>
          </div>
          
          {dataSource === 'vidsrc' && (
            <div className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span>VidSrc Domain:</span>
              <Select
                value={selectValue}
                onValueChange={handleDomainChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Auto (Try All)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto (Try All)</SelectItem>
                  {VIDSRC_DOMAINS.map(domain => (
                    <SelectItem key={domain} value={domain}>{domain}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-2"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        
        {error && (
          <Alert variant="default" className="mb-4 border-red-500/30 bg-red-900/10 text-red-400">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error}
              <div className="mt-2">
                <p className="text-sm">Try selecting a different data source or domain.</p>
                <div className="flex gap-2 mt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="border-yellow-500/30 bg-yellow-900/10 text-yellow-400 hover:bg-yellow-900/20"
                    onClick={() => setDataSource('tmdb')}
                  >
                    Try TMDb
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="border-red-500/30 bg-red-900/10 text-red-400 hover:bg-red-900/20"
                    onClick={() => setDataSource('mock')}
                  >
                    Use Mock Data
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </div>
      
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="w-full grid grid-cols-3 mb-6">
          <TabsTrigger value="movies">Movies</TabsTrigger>
          <TabsTrigger value="shows">TV Shows</TabsTrigger>
          <TabsTrigger value="episodes">Episodes</TabsTrigger>
        </TabsList>
        
        <TabsContent value="movies" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {renderContent(movies)}
          </div>
        </TabsContent>
        
        <TabsContent value="shows" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {renderContent(shows)}
          </div>
        </TabsContent>
        
        <TabsContent value="episodes" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {renderContent(episodes)}
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-between items-center mt-6">
        <Button 
          onClick={handlePrevPage} 
          disabled={page <= 1 || loading}
          variant="outline"
        >
          Previous Page
        </Button>
        <span className="text-center">
          Page {page}
          {loading && <span className="ml-2 inline-block animate-pulse">Loading...</span>}
        </span>
        <Button 
          onClick={handleNextPage} 
          disabled={loading || (currentItems.length === 0 && page > 1)}
          variant="outline"
        >
          Next Page
        </Button>
      </div>
      
      {/* Source Credit */}
      <div className="mt-8 text-center text-sm text-muted-foreground">
        <p>
          {dataSource === 'tmdb' && 'Movie data provided by The Movie Database (TMDb)'}
          {dataSource === 'vidsrc' && 'Content data provided by VidSrc API'}
          {dataSource === 'mock' && 'Using mock data for demonstration purposes'}
        </p>
        {dataSource === 'tmdb' && (
          <div className="mt-2">
            <img 
              src="https://www.themoviedb.org/assets/2/v4/logos/v2/blue_short-8e7b30f73a4020692ccca9c88bafe5dcb6f8a62a4c6bc55cd9ba82bb2cd95f6c.svg" 
              alt="TMDb Logo" 
              className="h-5 mx-auto" 
            />
          </div>
        )}
      </div>
    </div>
  );
} 