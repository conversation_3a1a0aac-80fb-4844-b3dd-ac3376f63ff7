"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, MonitorPlay, Users } from "lucide-react"
import Link from "next/link"

// Export proper Next.js 15 configuration
export const preferredRegion = 'auto'
export const dynamic = 'auto'

// Define a proper type for the search results
interface SearchResult {
  id: string
  title: string
  year: number
  type: string
  poster: string
}

export default function CreateWatchPartyPage() {
  const [title, setTitle] = useState("")
  const [contentId, setContentId] = useState("")
  const [contentType, setContentType] = useState("movie")
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()
  const { toast } = useToast()

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!searchQuery.trim()) return

    setLoading(true)
    try {
      // This would be a real API call in production
      // Mock search results for demo
      const mockResults: SearchResult[] = [
        { id: "tt1375666", title: "Inception", year: 2010, type: "movie", poster: "https://m.media-amazon.com/images/M/MV5BMjAxMzY3NjcxNF5BMl5BanBnXkFtZTcwNTI5OTM0Mw@@._V1_SX300.jpg" },
        { id: "tt0816692", title: "Interstellar", year: 2014, type: "movie", poster: "https://m.media-amazon.com/images/M/MV5BZjdkOTU3MDktN2IxOS00OGEyLWFmMjktY2FiMmZkNWIyODZiXkEyXkFqcGdeQXVyMTMxODk2OTU@._V1_SX300.jpg" },
        { id: "tt0468569", title: "The Dark Knight", year: 2008, type: "movie", poster: "https://m.media-amazon.com/images/M/MV5BMTMxNTMwODM0NF5BMl5BanBnXkFtZTcwODAyMTk2Mw@@._V1_SX300.jpg" },
      ]
      setSearchResults(mockResults)
    } catch (error) {
      toast({
        title: "Search failed",
        description: "Failed to search for content. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const selectContent = (result: SearchResult) => {
    setContentId(result.id)
    setTitle(`Watch Party: ${result.title}`)
  }

  const createWatchParty = () => {
    if (!contentId || !title) {
      toast({
        title: "Missing information",
        description: "Please select content and provide a party title",
        variant: "destructive",
      })
      return
    }

    // In a real app, you would make an API call to create the watch party
    // For now, we'll simulate it with a mock party ID
    const partyId = "party_" + Math.random().toString(36).substring(2, 9)
    
    toast({
      title: "Watch party created!",
      description: "Redirecting to your new watch party...",
    })
    
    // Redirect to the watch party page
    router.push(`/watch-party/${partyId}?contentId=${contentId}&type=${contentType}`)
  }

  return (
    <div className="container mx-auto py-10">
      <Link href="/dashboard" className="mb-6 flex items-center text-sm text-muted-foreground hover:text-primary">
        ← Back to Dashboard
      </Link>
      
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Create a Watch Party</CardTitle>
          <CardDescription>
            Search for a movie or TV show and invite friends to watch together
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="search" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="search">
                <Search className="mr-2 h-4 w-4" />
                Search Content
              </TabsTrigger>
              <TabsTrigger value="party">
                <Users className="mr-2 h-4 w-4" />
                Party Details
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="search" className="space-y-4 pt-4">
              <form onSubmit={handleSearch} className="flex gap-2">
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Search for movies or TV shows..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button type="submit" disabled={loading}>
                  {loading ? "Searching..." : "Search"}
                </Button>
              </form>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                {searchResults.map((result: SearchResult) => (
                  <div
                    key={result.id}
                    className={`border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md ${
                      contentId === result.id ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => selectContent(result)}
                  >
                    <div className="aspect-[2/3] overflow-hidden rounded-md mb-2">
                      <img
                        src={result.poster}
                        alt={result.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="font-medium">{result.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {result.year} • {result.type}
                    </p>
                  </div>
                ))}
                
                {searchResults.length === 0 && searchQuery && !loading && (
                  <div className="col-span-full text-center py-10">
                    <MonitorPlay className="mx-auto h-8 w-8 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-medium">No results found</h3>
                    <p className="text-muted-foreground">
                      Try searching for a different title
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="party" className="space-y-4 pt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="party-title">Party Title</Label>
                  <Input
                    id="party-title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter a name for your watch party"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Content Type</Label>
                  <div className="flex gap-4">
                    <Button 
                      type="button" 
                      variant={contentType === "movie" ? "default" : "outline"}
                      onClick={() => setContentType("movie")}
                    >
                      Movie
                    </Button>
                    <Button 
                      type="button" 
                      variant={contentType === "show" ? "default" : "outline"}
                      onClick={() => setContentType("show")}
                    >
                      TV Show
                    </Button>
                  </div>
                </div>
                
                {!contentId && (
                  <div className="bg-muted/50 rounded-lg p-4 text-center">
                    <p className="text-muted-foreground">
                      Please select content from the search tab first
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/dashboard')}>
            Cancel
          </Button>
          <Button onClick={createWatchParty} disabled={!contentId || !title}>
            Create Watch Party
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 