/**
 * Domain Health Monitoring Utility
 * 
 * Provides functions to check the health of VidSrc domains and determine
 * the most optimal domain to use at any given time.
 */

// Define the valid VidSrc domains
export const VIDSRC_DOMAINS = ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net']

// Health check timeout in milliseconds
const HEALTH_CHECK_TIMEOUT = 3000

// Cache keys for localStorage
const DOMAIN_HEALTH_CACHE_KEY = 'vidsrc:domain:health'
const LAST_CHECK_CACHE_KEY = 'vidsrc:domain:last_check'

// Cache duration in milliseconds - 10 minutes
const CACHE_DURATION = 10 * 60 * 1000

interface DomainHealth {
  domain: string
  status: 'ok' | 'error'
  responseTime: number
  lastChecked: number
}

/**
 * Helper function to get cached data from localStorage
 */
function getCache<T>(key: string): T | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const value = localStorage.getItem(key);
    if (!value) return null;
    return JSON.parse(value) as T;
  } catch (e) {
    console.warn(`Error reading from cache: ${e}`);
    return null;
  }
}

/**
 * Helper function to set cached data in localStorage
 */
function setCache<T>(key: string, value: T): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    console.warn(`Error writing to cache: ${e}`);
  }
}

/**
 * Check the health of a single domain
 * 
 * @param domain Domain to check
 * @returns Promise with domain health information
 */
export async function checkDomainHealth(domain: string): Promise<DomainHealth> {
  const startTime = Date.now()
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), HEALTH_CHECK_TIMEOUT)

  try {
    // Use a test route that's likely to exist on all domains
    const url = `https://${domain}/embed/movie?imdb=tt0111161` // Shawshank Redemption
    
    // First attempt a HEAD request as it's faster
    const headResponse = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      cache: 'no-store',
    })
    
    // If HEAD request is successful, we're good
    const endTime = Date.now()
    clearTimeout(timeoutId)
    
    if (headResponse.ok) {
      return {
        domain,
        status: 'ok',
        responseTime: endTime - startTime,
        lastChecked: Date.now(),
      }
    }
    
    // If HEAD fails, try a GET request with a short timeout
    const getController = new AbortController()
    const getTimeoutId = setTimeout(() => getController.abort(), 2000)
    
    try {
      const getResponse = await fetch(url, {
        method: 'GET',
        signal: getController.signal,
        cache: 'no-store',
      })
      
      const endGetTime = Date.now()
      clearTimeout(getTimeoutId)
      
      return {
        domain,
        status: getResponse.ok ? 'ok' : 'error',
        responseTime: endGetTime - startTime,
        lastChecked: Date.now(),
      }
    } catch (err) {
      clearTimeout(getTimeoutId)
      throw new Error(`GET request to ${domain} failed: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  } catch (error) {
    clearTimeout(timeoutId)
    
    return {
      domain,
      status: 'error',
      responseTime: HEALTH_CHECK_TIMEOUT, // Max time
      lastChecked: Date.now(),
    }
  }
}

/**
 * Check the health of all VidSrc domains
 * 
 * @param forceRefresh Force a refresh of the cache
 * @returns Promise with an array of domain health information
 */
export async function checkAllDomains(forceRefresh = false): Promise<DomainHealth[]> {
  // Check if we have a recent cache
  try {
    if (!forceRefresh) {
      const lastCheck = getCache<number>(LAST_CHECK_CACHE_KEY);
      const cachedHealth = getCache<DomainHealth[]>(DOMAIN_HEALTH_CACHE_KEY);

      if (lastCheck && cachedHealth && Date.now() - lastCheck < CACHE_DURATION) {
        console.log(`Using cached domain health from ${new Date(lastCheck).toISOString()}`);
        return cachedHealth;
      }
    }
  } catch (err) {
    console.warn('Error accessing cache for domain health:', err)
    // Continue with fresh check on cache error
  }

  try {
    // Check all domains in parallel
    const healthChecks = await Promise.allSettled(
      VIDSRC_DOMAINS.map(domain => checkDomainHealth(domain))
    )

    // Process the results
    const results: DomainHealth[] = healthChecks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        // Return error result for rejected promises
        return {
          domain: VIDSRC_DOMAINS[index],
          status: 'error',
          responseTime: HEALTH_CHECK_TIMEOUT,
          lastChecked: Date.now(),
        }
      }
    })

    // Cache the results
    try {
      setCache(DOMAIN_HEALTH_CACHE_KEY, results);
      setCache(LAST_CHECK_CACHE_KEY, Date.now());
    } catch (err) {
      console.warn('Error caching domain health results:', err)
    }

    return results
  } catch (error) {
    console.error('Error checking domain health:', error)
    
    // Return a fallback with all domains marked as error
    return VIDSRC_DOMAINS.map(domain => ({
      domain,
      status: 'error' as const,
      responseTime: HEALTH_CHECK_TIMEOUT,
      lastChecked: Date.now(),
    }))
  }
}

/**
 * Get the best domain based on health checks
 * This uses a combination of cached results and reliability history
 * 
 * @returns Promise with the best domain
 */
export async function getBestDomain(): Promise<string> {
  try {
    // Prioritize domains known to work well for initial loads
    const reliableDomains = ['vidsrc.in', 'vidsrc.xyz'];
    
    // First, try a quick check of the most reliable domains
    for (const domain of reliableDomains) {
      try {
        const quickCheck = await checkDomainHealth(domain);
        if (quickCheck.status === 'ok') {
          console.log(`Quick check found working domain: ${domain}`);
          return domain;
        }
      } catch (e) {
        // Continue to next domain
      }
    }
    
    // If quick checks fail, do a full health check
    const healthResults = await checkAllDomains(true)

    // Filter only 'ok' status domains
    const healthyDomains = healthResults.filter(result => result.status === 'ok')

    if (healthyDomains.length === 0) {
      console.log('No healthy domains found, using default')
      return 'vidsrc.in' // Default to a reliable domain when none are healthy
    }

    // Sort by response time (fastest first)
    healthyDomains.sort((a, b) => a.responseTime - b.responseTime)

    // Return the domain with the fastest response time
    console.log(`Best domain found: ${healthyDomains[0].domain} (${healthyDomains[0].responseTime}ms)`)
    return healthyDomains[0].domain
  } catch (error) {
    console.error('Error getting best domain:', error)
    return 'vidsrc.in' // Default to a reliable domain
  }
}

/**
 * Get the next best domain (excluding the current one)
 * 
 * @param currentDomain Current domain to exclude
 * @returns Promise with the next best domain
 */
export async function getNextBestDomain(currentDomain: string): Promise<string> {
  try {
    const healthResults = await checkAllDomains(false)

    // Filter only 'ok' status domains and exclude current domain
    const healthyDomains = healthResults
      .filter(result => result.status === 'ok' && result.domain !== currentDomain)

    if (healthyDomains.length === 0) {
      // If no healthy domains, return any domain that's not the current one
      const nextDomain = VIDSRC_DOMAINS.find(d => d !== currentDomain) || VIDSRC_DOMAINS[0]
      console.log(`No healthy alternative domains found, using ${nextDomain}`)
      return nextDomain
    }

    // Sort by response time (fastest first)
    healthyDomains.sort((a, b) => a.responseTime - b.responseTime)

    // Return the domain with the fastest response time
    console.log(`Next best domain: ${healthyDomains[0].domain} (${healthyDomains[0].responseTime}ms)`)
    return healthyDomains[0].domain
  } catch (error) {
    console.error('Error getting next best domain:', error)
    
    // Fallback to simple rotation
    const currentIndex = VIDSRC_DOMAINS.findIndex(d => d === currentDomain)
    const nextIndex = (currentIndex + 1) % VIDSRC_DOMAINS.length
    return VIDSRC_DOMAINS[nextIndex]
  }
} 