/** @type {import('next').NextConfig} */

// Detect Windows platform
const isWindows = process.platform === 'win32';

const nextConfig = {
  // For local development, don't use static export
  output: process.env.NODE_ENV === 'production' ? undefined : undefined,
  // Use a standard directory name for development
  distDir: '.next',
  // Disable server-side rendering for specific paths
  serverRuntimeConfig: {
    // Will only be available on the server side
    noSSR: ['details'],
  },
  publicRuntimeConfig: {
    // Will be available on both server and client
    isServerSide: typeof window === 'undefined',
  },
  // Configuration for Windows compatibility
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'image.tmdb.org',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'm.media-amazon.com',
        pathname: '**',
      },
    ],
    domains: [
      'image.tmdb.org',
      'res.cloudinary.com',
      'lh3.googleusercontent.com',
      'api.vidit.tvs',
      'localhost',
      'placehold.co',
      'images.pexels.com'
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Performance optimizations
  reactStrictMode: true,
  // Increase memory limit for faster builds
  experimental: {
    // Enable server actions
    serverActions: {
      allowedOrigins: ['localhost:3000'],
    },
    // Optimize Next.js concurrency
    workerThreads: false, // Disable experimental worker threads
    // Optimize CSS handling
    optimizeCss: false, // Disable experimental CSS optimization
  },
  // Increase cache effectiveness
  onDemandEntries: {
    // Keep unused pages in memory longer
    maxInactiveAge: 60 * 60 * 1000, // 1 hour
    // Cache more pages in development
    pagesBufferLength: 5,
  },
  // Handle Node.js modules that MongoDB is trying to use
  webpack: (config, { isServer, webpack }) => {
    if (!isServer) {
      // Don't resolve 'fs', 'net', etc. on the client to prevent errors
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        'fs/promises': false,
        'timers/promises': false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        path: false,
        os: false,
        util: false,
      };

      // Define browser-specific globals
      config.plugins.push(
        new webpack.DefinePlugin({
          'process.browser': true,
          'global.BROWSER': true,
        })
      );
    }

    // Windows-specific configuration to prevent EINVAL errors
    if (isWindows) {
      // Disable symlinks in the webpack configuration
      config.resolve.symlinks = false;

      // Add specific handling for edge runtime on Windows
      if (config.name === 'edge-runtime-webpack') {
        config.output.enabledLibraryTypes = ['module'];
      }
    }

    // Add this block to ignore optional MongoDB dependencies
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^(kerberos|@mongodb-js\/zstd|@aws-sdk\/credential-providers|gcp-metadata|snappy|socks|aws4|mongodb-client-encryption)$/,
      })
    );

    return config;
  },
};

module.exports = nextConfig;
