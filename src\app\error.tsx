'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  const router = useRouter();

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Error boundary caught error:', error);
  }, [error]);

  const handleReset = () => {
    try {
      // For session errors, try to clear local storage
      if (error.message.includes('session') || error.message.includes('Critical error')) {
        console.log('Clearing client-side session data before reset...');
        localStorage.removeItem('user');
        localStorage.removeItem('userId');
        localStorage.removeItem('watchPartyUserId');
      }
      
      // Then attempt the normal reset
      reset();
    } catch (resetError) {
      console.error('Error during reset:', resetError);
      // If reset fails, try reloading the page
      window.location.reload();
    }
  };

  const handleSignOut = () => {
    try {
      // Clear all auth-related state and redirect to login
      localStorage.removeItem('user');
      localStorage.removeItem('userId');
      localStorage.removeItem('watchPartyUserId');
      
      // Redirect to login page
      router.push('/auth');
    } catch (signOutError) {
      console.error('Error during sign out:', signOutError);
      // If redirect fails, reload the page
      window.location.href = '/auth';
    }
  };

  // Check if it's a session validation error
  const isSessionError = error.message.includes('session') || 
                         error.message.includes('verification') || 
                         error.message.includes('Critical error');

  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-background">
      <Card className="mx-auto max-w-md">
        <CardHeader>
          <CardTitle>Something went wrong</CardTitle>
          <CardDescription>
            {isSessionError 
              ? "We're having trouble verifying your session." 
              : "We've encountered an unexpected error."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground mb-4">
            <p>Error details: {error.message || "Unknown error"}</p>
            {error.digest && <p className="text-xs text-muted-foreground mt-1">Error ID: {error.digest}</p>}
          </div>
          
          {isSessionError && (
            <div className="p-3 bg-amber-50 dark:bg-amber-950 rounded-md text-amber-800 dark:text-amber-300 text-sm mb-4">
              <p>Your session may have expired or encountered a validation issue. You can try:</p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Refreshing the page</li>
                <li>Signing in again</li>
                <li>Clearing your browser cache</li>
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleReset}>
            Try again
          </Button>
          
          {isSessionError ? (
            <Button onClick={handleSignOut}>
              Sign in again
            </Button>
          ) : (
            <Button onClick={() => router.push('/')}>
              Return home
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
