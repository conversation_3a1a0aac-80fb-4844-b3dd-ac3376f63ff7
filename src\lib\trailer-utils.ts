/**
 * Utility functions for handling trailers
 */

/**
 * Extracts YouTube trailer video ID from TMDB videos response
 * 
 * @param videos The videos array from TMDB API response
 * @returns The YouTube video ID or undefined if no trailer found
 */
export function extractTrailerFromTMDB(videos: any): string | undefined {
  if (!videos || !Array.isArray(videos.results) || videos.results.length === 0) {
    return undefined;
  }

  // First, try to find an official trailer
  const officialTrailer = videos.results.find(
    (video: any) => 
      video.site === 'YouTube' && 
      video.type === 'Trailer' && 
      video.official === true &&
      video.key
  );

  if (officialTrailer) {
    return officialTrailer.key;
  }

  // If no official trailer, try to find any trailer
  const anyTrailer = videos.results.find(
    (video: any) => 
      video.site === 'YouTube' && 
      video.type === 'Trailer' &&
      video.key
  );

  if (anyTrailer) {
    return anyTrailer.key;
  }

  // If no trailer, try to find a teaser
  const teaser = videos.results.find(
    (video: any) => 
      video.site === 'YouTube' && 
      video.type === 'Teaser' &&
      video.key
  );

  if (teaser) {
    return teaser.key;
  }

  // If no trailer or teaser, just return the first YouTube video
  const anyYouTubeVideo = videos.results.find(
    (video: any) => 
      video.site === 'YouTube' &&
      video.key
  );

  return anyYouTubeVideo ? anyYouTubeVideo.key : undefined;
}

/**
 * Extracts YouTube video ID from a YouTube URL
 * 
 * @param url The YouTube URL
 * @returns The YouTube video ID or undefined if not a valid YouTube URL
 */
export function extractYouTubeId(url: string): string | undefined {
  if (!url) return undefined;

  // Match YouTube URL patterns
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);

  return (match && match[2].length === 11)
    ? match[2]
    : undefined;
}

/**
 * Gets the YouTube thumbnail URL for a video ID
 * 
 * @param videoId The YouTube video ID
 * @param quality The thumbnail quality (default, medium, high, standard, maxres)
 * @returns The thumbnail URL
 */
export function getYouTubeThumbnail(
  videoId: string, 
  quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'maxres'
): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
}
