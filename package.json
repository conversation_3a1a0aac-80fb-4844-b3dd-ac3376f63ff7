{"name": "stream-vista", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "dev:turbo": "next dev -H 0.0.0.0 --turbo", "prebuild": "node scripts/netlify-build-mongo-check.js", "build": "next build", "export": "next export -o build", "start": "next start", "lint": "eslint --config eslint.config.cjs . --ext .js,.jsx,.ts,.tsx --ignore-pattern *.mjs --ignore-pattern *.cjs", "static": "npx serve build", "mongo:check": "node scripts/check-mongo-connection.js", "clean": "rimraf .next", "resize-logo": "node src/scripts/resize-logo.mjs"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.4", "@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.17.16", "@types/next-auth": "^3.13.0", "@types/react-window": "^1.8.8", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "esm": "^3.2.25", "framer-motion": "^12.9.2", "geist": "^1.3.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "mongodb": "^6.5.0", "mongoose": "^8.13.2", "next": "^15.2.0", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "nodemailer": "^6.10.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-window": "^1.8.11", "recharts": "^2.15.2", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "rimraf": "^6.0.1", "serve": "^14.2.1", "sharp": "^0.34.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}