'use client'

import { useState, useEffect } from 'react'
import { Search, Play, Star, Film, Tv, Calendar, Loader2, RefreshCcw } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { useDebounce } from '@/lib/hooks/useDebounce'
import { getPopularMovies, getPopularShows, searchContent } from '@/services/tmdb'
import { IContent } from '@/data/content'
import { ensureContentImagePaths } from '@/utils/image-utils'

// Types for content
export interface Content {
  id: string;
  title: string;
  description?: string;
  posterPath?: string;
  backdropPath?: string;
  rating?: number;
  releaseDate?: string;
  type: 'movie' | 'show';
  tmdbId?: string;
  genres?: string[];
}

interface ContentSelectorProps {
  onSelectContent: (content: Content | null) => void;
  initialSelectedContent?: Content | null;
  className?: string;
}

// Convert IContent to Content format for the selector
const convertToSelectorContent = (content: IContent): Content => {
  // Create the content object with all fields
  const convertedContent: Content = {
    id: content.id.toString(),
    title: content.title,
    description: content.overview,
    posterPath: content.posterPath,
    backdropPath: content.backdropPath,
    rating: content.rating,
    releaseDate: content.year ? `${content.year}-01-01` : undefined,
    type: content.type as 'movie' | 'show',
    tmdbId: content.tmdbId,
    genres: content.genres || []
  };

  // Make sure all image paths have proper URLs
  const result = ensureContentImagePaths(convertedContent);

  // Debug log for image paths
  console.log(`[ContentSelector] Converted content: ${content.title}`);
  console.log(`[ContentSelector] Original posterPath: ${content.posterPath?.substring(0, 50)}...`);
  console.log(`[ContentSelector] Processed posterPath: ${result.posterPath?.substring(0, 50)}...`);

  return result;
};



export default function ContentSelector({
  onSelectContent,
  initialSelectedContent,
  className = '',
}: ContentSelectorProps) {
  const [selectedContent, setSelectedContent] = useState<Content | null>(initialSelectedContent || null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [popularMovies, setPopularMovies] = useState<Content[]>([]);
  const [popularShows, setPopularShows] = useState<Content[]>([]);
  const [searchResults, setSearchResults] = useState<Content[]>([]);
  const [apiError, setApiError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [activeTab, setActiveTab] = useState<string>('movies');

  const debouncedSearch = useDebounce(searchQuery, 500);

  // Update selected content when initialSelectedContent changes
  useEffect(() => {
    if (initialSelectedContent) {
      setSelectedContent(initialSelectedContent);
    }
  }, [initialSelectedContent]);

  // Initial content fetch
  useEffect(() => {
    async function fetchContent() {
      console.log('ContentSelector: Fetching initial content');
      setLoading(true);
      setApiError(null);

      try {
        // Fetch one type of content at a time to avoid failing all requests
        let movies: Content[] = [];
        let shows: Content[] = [];

        try {
          console.log('ContentSelector: Fetching popular movies');
          const movieData = await getPopularMovies();
          movies = movieData.map(convertToSelectorContent);
          console.log(`ContentSelector: Fetched ${movies.length} popular movies`, movies);
        } catch (error) {
          console.error('ContentSelector: Error fetching popular movies:', error);
        }

        try {
          console.log('ContentSelector: Fetching popular shows');
          const showData = await getPopularShows();
          shows = showData.map(convertToSelectorContent);
          console.log(`ContentSelector: Fetched ${shows.length} popular shows`, shows);
        } catch (error) {
          console.error('ContentSelector: Error fetching popular shows:', error);
        }

        if (movies.length > 0) {
          console.log('ContentSelector: Setting popular movies state');
          setPopularMovies(movies);
        }

        if (shows.length > 0) {
          console.log('ContentSelector: Setting popular shows state');
          setPopularShows(shows);
        }

        // If we couldn't get any data, show error
        if (movies.length === 0 && shows.length === 0) {
          console.error('ContentSelector: No content received from TMDB API');
          setApiError('Unable to load content. Please try again later.');
        }
      } catch (error) {
        console.error('ContentSelector: Error fetching initial content:', error);
        setApiError('Unable to load content. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    fetchContent();
  }, [retryCount]);

  // Debug state changes
  useEffect(() => {
    console.log('ContentSelector state updated:', {
      popularMoviesCount: popularMovies.length,
      popularShowsCount: popularShows.length,
      isLoading: loading,
      activeTab
    });
  }, [popularMovies, popularShows, loading, activeTab]);

  // Search for content
  useEffect(() => {
    if (!debouncedSearch.trim()) {
      setSearchResults([]);
      return;
    }

    async function performSearch() {
      console.log(`ContentSelector: Searching for "${debouncedSearch}"`);
      setLoading(true);
      setApiError(null);

      try {
        const result = await searchContent(debouncedSearch);

        const formattedResults = [
          ...result.movies.map(convertToSelectorContent),
          ...result.shows.map(convertToSelectorContent)
        ];

        console.log(`ContentSelector: Search found ${formattedResults.length} results`);
        setSearchResults(formattedResults);
      } catch (error) {
        console.error('ContentSelector: Error searching for content:', error);
        setApiError('Search failed. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    performSearch();
  }, [debouncedSearch]);

  // Handle content selection
  const handleSelectContent = (content: Content) => {
    setSelectedContent(content);
    onSelectContent(content);
  };

  const handleRemoveContent = () => {
    setSelectedContent(null);
    onSelectContent(null);
  };

  const handleRetry = () => {
    setRetryCount(prevCount => prevCount + 1);
  };

  // Format release date for display
  const formatReleaseDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.getFullYear();
  };

  // Render selected content prominently
  const renderSelectedContent = () => {
    if (!selectedContent) return null;

    return (
      <div className="mb-6 p-4 bg-vista-dark/40 border border-vista-blue/40 rounded-lg shadow-md shadow-vista-blue/10">
        <div className="flex flex-col sm:flex-row gap-4 items-center">
          <div className="relative w-32 flex-shrink-0">
            <div className="aspect-[2/3] relative rounded-md overflow-hidden ring-2 ring-vista-blue/50">
              {selectedContent.posterPath ? (
                <Image
                  src={selectedContent.posterPath}
                  alt={selectedContent.title}
                  fill
                  sizes="128px"
                  className="object-cover"
                  unoptimized={selectedContent.posterPath.includes('image.tmdb.org')}
                />
              ) : (
                <div className="w-full h-full bg-vista-dark-lighter flex items-center justify-center">
                  {selectedContent.type === 'movie' ? (
                    <Film className="w-10 h-10 text-vista-blue/50" />
                  ) : (
                    <Tv className="w-10 h-10 text-vista-blue/50" />
                  )}
                </div>
              )}

              <div className="absolute top-2 right-2">
                <Badge
                  variant={selectedContent.type === 'movie' ? 'default' : 'secondary'}
                  className="text-xs font-semibold bg-vista-blue/80 text-white"
                >
                  {selectedContent.type === 'movie' ? 'Movie' : 'TV Show'}
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex-grow space-y-2 text-center sm:text-left">
            <h2 className="text-xl font-semibold text-white">{selectedContent.title}</h2>

            <div className="flex flex-wrap gap-3 justify-center sm:justify-start">
              {selectedContent.releaseDate && (
                <span className="text-sm text-vista-light flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {formatReleaseDate(selectedContent.releaseDate)}
                </span>
              )}

              {selectedContent.rating && (
                <span className="text-sm text-vista-light flex items-center">
                  <Star className="w-4 h-4 text-yellow-400 mr-1" />
                  <span>{selectedContent.rating.toFixed(1)}</span>
                </span>
              )}
            </div>

            {selectedContent.description && (
              <p className="text-vista-light text-sm line-clamp-2">{selectedContent.description}</p>
            )}

            <Button
              variant="outline"
              size="sm"
              className="mt-2 border-vista-blue/40 bg-vista-blue/10 hover:bg-vista-blue/20 text-white"
              onClick={handleRemoveContent}
            >
              Change Selection
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Render content card
  const renderContentCard = (content: Content) => {
    const isSelected = selectedContent?.id === content.id;

    // Ensure image path is properly formatted
    const posterPath = content.posterPath || '';
    const isExternalImage = posterPath.includes('image.tmdb.org') || posterPath.includes('http');

    return (
      <motion.div
        key={content.id}
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.98 }}
        animate={{ opacity: 1 }}
        initial={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Card
          className={`overflow-hidden cursor-pointer transition-all border backdrop-blur-sm ${
            isSelected
              ? 'ring-2 ring-vista-blue border-vista-blue bg-vista-blue/10'
              : 'hover:border-vista-blue/40 border-vista-light/20 bg-vista-dark/30 hover:bg-vista-dark/40'
          }`}
          onClick={() => handleSelectContent(content)}
        >
          <div className="aspect-[2/3] relative">
            {posterPath ? (
              <Image
                src={posterPath}
                alt={content.title}
                fill
                unoptimized={isExternalImage}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover"
                referrerPolicy="no-referrer"
                onError={() => console.error(`[ContentSelector] Image error for ${content.title}: ${posterPath}`)}
              />
            ) : (
              <div className="w-full h-full bg-vista-dark-lighter flex items-center justify-center">
                {content.type === 'movie' ? (
                  <Film className="w-16 h-16 text-vista-light/20" />
                ) : (
                  <Tv className="w-16 h-16 text-vista-light/20" />
                )}
              </div>
            )}
            <div className="absolute top-2 right-2">
              <Badge
                variant={content.type === 'movie' ? 'default' : 'secondary'}
                className="text-xs font-semibold"
              >
                {content.type === 'movie' ? 'Movie' : 'TV Show'}
              </Badge>
            </div>
            {content.rating && (
              <div className="absolute bottom-2 left-2 bg-black/70 rounded-full p-1 flex items-center">
                <Star className="w-3 h-3 text-yellow-400 mr-1" />
                <span className="text-xs font-medium text-white">{content.rating.toFixed(1)}</span>
              </div>
            )}
            {isSelected && (
              <div className="absolute inset-0 bg-vista-blue/20 flex items-center justify-center">
                <div className="bg-vista-blue rounded-full p-2">
                  <Play className="w-6 h-6 text-white" fill="white" />
                </div>
              </div>
            )}
          </div>
          <CardContent className="p-3 pb-4">
            <h3 className="font-semibold text-white line-clamp-1">{content.title}</h3>
            <div className="flex items-center mt-1">
              {content.releaseDate && (
                <span className="text-xs text-vista-light flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatReleaseDate(content.releaseDate)}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };



  return (
    <Card className={`bg-transparent backdrop-blur-sm border-vista-blue/30 border overflow-hidden shadow-lg shadow-vista-blue/5 hover:shadow-vista-blue/10 transition-shadow ${className}`}>
      <CardHeader className="border-b border-vista-blue/20 pb-4">
        <CardTitle className="text-xl text-white">Select Content</CardTitle>
        <CardDescription className="text-vista-light">
          Choose a movie or TV show for your watch party
        </CardDescription>

        {/* Show selected content info at the top */}
        {selectedContent && renderSelectedContent()}

        <div className="relative mt-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50" />
          <Input
            type="text"
            placeholder="Search for a movie or TV show..."
            className="pl-9 bg-vista-dark/60 border-vista-blue/30 text-white focus:border-vista-blue"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>

      <CardContent className="p-4 bg-vista-dark/30">
        {loading && (
          <div className="p-6 text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-vista-blue/20 mb-4">
              <Loader2 className="w-8 h-8 text-vista-blue animate-spin" />
            </div>
            <h3 className="text-xl font-medium text-vista-blue mb-2">Loading Content</h3>
            <p className="text-vista-light mb-4">Please wait while we fetch the latest movies and shows...</p>
          </div>
        )}

        {apiError && !loading && (
          <div className="p-6 text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-500/20 mb-4">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
              </svg>
            </div>
            <h3 className="text-xl font-medium text-red-500 mb-2">API Error</h3>
            <p className="text-vista-light mb-4">{apiError}</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={handleRetry}
                className="bg-vista-blue hover:bg-vista-blue/90 text-white flex items-center gap-2"
              >
                <RefreshCcw className="h-4 w-4" />
                Retry Loading Content
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="border-vista-blue/30 text-white hover:bg-vista-blue/20"
              >
                Refresh Page
              </Button>
            </div>
          </div>
        )}

        {!loading && !apiError && (
          <>
            {searchQuery ? (
              <div>
                <h3 className="text-white font-medium mb-3 flex items-center">
                  <Search className="w-4 h-4 mr-2 text-vista-blue" />
                  Search Results
                </h3>
                {searchResults.length > 0 ? (
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {searchResults.map(content => renderContentCard(content))}
                  </div>
                ) : (
                  <div className="py-8 text-center text-vista-light bg-vista-dark/20 rounded-lg border border-vista-blue/20">
                    No results found for "{searchQuery}". Try a different search term.
                  </div>
                )}
              </div>
            ) : (
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="bg-vista-dark/60 mb-4 border border-vista-blue/30 rounded-md">
                  <TabsTrigger value="movies" className="data-[state=active]:bg-vista-blue/80 data-[state=active]:text-white">
                    <Film className="h-4 w-4 mr-2" />
                    Movies
                  </TabsTrigger>
                  <TabsTrigger value="shows" className="data-[state=active]:bg-vista-blue/80 data-[state=active]:text-white">
                    <Tv className="h-4 w-4 mr-2" />
                    TV Shows
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="movies">
                  {popularMovies.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {popularMovies.map(movie => renderContentCard(movie))}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-vista-light bg-vista-dark/20 rounded-lg border border-vista-blue/20">
                      No popular movies available at the moment. Please check back later.
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="shows">
                  {popularShows.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {popularShows.map(show => renderContentCard(show))}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-vista-light bg-vista-dark/20 rounded-lg border border-vista-blue/20">
                      No popular TV shows available at the moment. Please check back later.
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}