'use client'

// Add global type declaration for the updatePlaybackTimeout and tracking variables
declare global {
  interface Window {
    updatePlaybackTimeout: NodeJS.Timeout | null;
    lastReportedTime: number;
  }
}

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Play, Pause, Volume2, VolumeX, MessageCircle, Users, RefreshCw, Send, X, Share2, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useToast } from '@/components/ui/use-toast'
import { WatchPartyProvider, useWatchParty } from '@/lib/WatchPartyContext'
import Image from 'next/image'

interface WatchPartyDetailsProps {
  partyId: string
}

// Wrapper component for provider
export default function WatchPartyDetails({ partyId }: WatchPartyDetailsProps) {
  return (
    <WatchPartyProvider>
      <WatchPartyDetailsContent partyId={partyId} />
    </WatchPartyProvider>
  )
}

function WatchPartyDetailsContent({ partyId }: WatchPartyDetailsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const videoRef = useRef<HTMLIFrameElement>(null)
  const chatScrollRef = useRef<HTMLDivElement>(null)

  // State for the UI elements
  const [embedUrl, setEmbedUrl] = useState('')
  const [message, setMessage] = useState('')
  const [muted, setMuted] = useState(false)
  const [activeTab, setActiveTab] = useState('chat')
  const [copied, setCopied] = useState(false)
  const [userName, setUserName] = useState('')
  const [isVideoReady, setIsVideoReady] = useState(false)

  // Get watch party context
  const {
    currentParty,
    isLoading,
    joinParty,
    leaveParty,
    sendMessage,
    sendReaction,
    updatePlayback
  } = useWatchParty()

  // Get user name from localStorage
  useEffect(() => {
    const savedName = localStorage.getItem('vista-username')
    if (savedName) {
      setUserName(savedName)
    } else {
      // Generate a random name if none is saved
      setUserName(`User${Math.floor(Math.random() * 1000)}`)
    }
  }, [])

  // Join party when component mounts
  useEffect(() => {
    if (partyId && userName) {
      joinParty(partyId, userName)
    }
  }, [partyId, userName, joinParty])

  // Generate embed URL when content changes
  useEffect(() => {
    if (currentParty?.content) {
      let url = ''
      const content = currentParty.content

      // Generate embed URL based on content type
      if (content.type === 'movie') {
        url = `https://vidsrc.to/embed/movie/${content.id}`
      } else if (content.type === 'show') {
        // For shows, we might want to specify season/episode
        const season = currentParty.currentSeason || 1
        const episode = currentParty.currentEpisode || 1
        url = `https://vidsrc.to/embed/tv/${content.id}/${season}/${episode}`
      }

      setEmbedUrl(url)
      // Short delay to ensure the UI updates first
      setTimeout(() => setIsVideoReady(true), 500)
    }
  }, [currentParty])

  // Scroll chat to bottom when new messages arrive
  useEffect(() => {
    if (chatScrollRef.current && currentParty?.messages.length) {
      chatScrollRef.current.scrollTo({
        top: chatScrollRef.current.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, [currentParty?.messages])

  // Initialize window property for playback throttling
  useEffect(() => {
    // Initialize the window property if it doesn't exist
    if (typeof window !== 'undefined') {
      window.updatePlaybackTimeout = null;
      // Add a property to track the last reported time
      window.lastReportedTime = 0;
    }

    return () => {
      // Clean up on unmount
      if (typeof window !== 'undefined' && window.updatePlaybackTimeout) {
        clearTimeout(window.updatePlaybackTimeout);
        window.updatePlaybackTimeout = null;
      }
    };
  }, []);

  // Set up event listeners for the video player with improved reliability
  useEffect(() => {
    if (!videoRef.current || !isVideoReady) return

    // Helper to handle iframe messages from the player
    const handleVideoEvent = (event: MessageEvent) => {
      if (!event.data || typeof event.data !== 'string') return

      try {
        // Parse the event data
        const data = JSON.parse(event.data)

        // Handle player events
        if (data.type === 'playerState') {
          // Log the event for debugging
          console.log('[WatchPartyDetails] Received player state update:', data);

          // Throttle updates to prevent flooding
          // Use a debounce pattern for regular updates but ensure play/pause events are sent immediately
          const isPlayStateChange = currentParty &&
                                   currentParty.isPlaying !== data.isPlaying;

          // Always update immediately for play/pause events
          if (isPlayStateChange) {
            console.log('[WatchPartyDetails] Play state changed, updating immediately');
            updatePlayback(data.currentTime, data.isPlaying);
            // Update the last reported time
            window.lastReportedTime = data.currentTime;
          } else {
            // For other updates (like time changes), use a throttled update with significance check
            if (!window.updatePlaybackTimeout) {
              const lastTime = window.lastReportedTime || 0;
              const timeDiff = Math.abs(lastTime - data.currentTime);

              // Only schedule update if time change is significant (> 3 seconds)
              if (timeDiff > 3) {
                console.log('[WatchPartyDetails] Significant time change detected:', timeDiff);
                window.updatePlaybackTimeout = setTimeout(() => {
                  updatePlayback(data.currentTime, data.isPlaying);
                  window.lastReportedTime = data.currentTime;
                  window.updatePlaybackTimeout = null;
                }, 3000); // Throttle to max 1 update per 3 seconds
              }
            }
          }
        }
      } catch (err) {
        // Ignore parsing errors
      }
    }

    // Add the event listener
    window.addEventListener('message', handleVideoEvent)

    return () => {
      // Clean up event listener and any pending timeouts
      window.removeEventListener('message', handleVideoEvent)
      if (window.updatePlaybackTimeout) {
        clearTimeout(window.updatePlaybackTimeout);
        window.updatePlaybackTimeout = null;
      }
    }
  }, [isVideoReady, updatePlayback, currentParty])

  // Handle leaving party when navigating away
  useEffect(() => {
    return () => {
      if (currentParty) {
        leaveParty()
      }
    }
  }, [currentParty, leaveParty])

  // Redirect if no party is found after loading
  useEffect(() => {
    if (!isLoading && !currentParty) {
      toast({
        title: "Party not found",
        description: "The watch party you're looking for doesn't exist or has ended",
        variant: "destructive"
      })
      router.push('/watch-party')
    }
  }, [isLoading, currentParty, router, toast])

  // Handle sending a message
  const handleSendMessage = () => {
    if (!message.trim()) return

    sendMessage(message)
    setMessage('')
  }

  // Handle sending a reaction
  const handleSendReaction = (reaction: string) => {
    sendReaction(reaction)
  }

  // Handle copying party ID
  const handleCopyPartyId = () => {
    if (!currentParty) return

    navigator.clipboard.writeText(currentParty.id)
    setCopied(true)

    toast({
      title: "Party ID copied",
      description: "Share this ID with friends so they can join your party"
    })

    setTimeout(() => setCopied(false), 2000)
  }

  // Handle leaving the party
  const handleLeaveParty = () => {
    leaveParty()
    router.push('/watch-party')
  }

  // Handle player controls
  const handlePlayPause = () => {
    if (!videoRef.current) return

    // Send message to iframe player
    videoRef.current.contentWindow?.postMessage(
      JSON.stringify({ action: currentParty?.isPlaying ? 'pause' : 'play' }),
      '*'
    )
  }

  // Handle mute/unmute
  const handleMuteToggle = () => {
    if (!videoRef.current) return

    setMuted(!muted)

    // Send message to iframe player
    videoRef.current.contentWindow?.postMessage(
      JSON.stringify({ action: muted ? 'unmute' : 'mute' }),
      '*'
    )
  }

  // Handle refreshing the player
  const handleRefreshPlayer = () => {
    setIsVideoReady(false)

    // Short delay to allow the iframe to reset
    setTimeout(() => {
      setIsVideoReady(true)
    }, 500)

    toast({
      title: "Player refreshed",
      description: "The video player has been refreshed"
    })
  }

  // Loading state
  if (isLoading || !currentParty) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-vista-dark">
        <div className="text-vista-light/70">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin mb-4"></div>
            <p>Connecting to watch party...</p>
          </div>
        </div>
      </div>
    )
  }

  // User is in a host on the page that matches this party
  const isHost = currentParty.members.some(m => m.isHost && m.id === localStorage.getItem('vista-user-id'))

  return (
    <div className="container mx-auto px-4 py-8 pt-20">
      {/* Party Header */}
      <div className="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl md:text-3xl font-bold text-vista-light">
              {currentParty.content?.title || "Watch Party"}
            </h1>
            <Badge variant="outline" className="text-vista-blue border-vista-blue bg-vista-blue/10">
              {currentParty.content?.type === 'movie' ? 'Movie' : 'TV Show'}
            </Badge>
          </div>
          <p className="text-vista-light/70 mt-1">
            Hosted by {currentParty.members.find(m => m.isHost)?.name || "Unknown"}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleCopyPartyId}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                >
                  <Share2 className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                {copied ? "Copied!" : "Copy party ID"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="outline"
            className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
            onClick={handleLeaveParty}
          >
            <X className="w-4 h-4 mr-2" />
            Leave Party
          </Button>

          {isHost && (
            <Button
              variant="destructive"
              onClick={() => {
                leaveParty()
                router.push('/watch-party')
              }}
            >
              End Party
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player Column */}
        <div className="lg:col-span-2">
          <div className="bg-vista-dark-lighter rounded-lg overflow-hidden border border-vista-light/10 mb-4">
            <div className="relative aspect-video bg-black">
              {isVideoReady && embedUrl ? (
                <iframe
                  ref={videoRef}
                  src={embedUrl}
                  className="absolute inset-0 w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              ) : (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-10 h-10 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
                </div>
              )}
            </div>

            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handlePlayPause}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                >
                  {currentParty.isPlaying ? (
                    <Pause className="w-5 h-5" />
                  ) : (
                    <Play className="w-5 h-5" />
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleMuteToggle}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                >
                  {muted ? (
                    <VolumeX className="w-5 h-5" />
                  ) : (
                    <Volume2 className="w-5 h-5" />
                  )}
                </Button>
              </div>

              <div className="text-vista-light/70 text-sm">
                {currentParty.isPlaying ? "Playing" : "Paused"}
                {currentParty.currentTime > 0 && ` • ${Math.floor(currentParty.currentTime / 60)}:${String(Math.floor(currentParty.currentTime % 60)).padStart(2, '0')}`}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefreshPlayer}
                className="text-vista-light/70 hover:text-vista-light"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Content Info */}
          <Card className="bg-vista-dark-lighter border-vista-light/10">
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                {currentParty.content?.posterPath && (
                  <div className="hidden sm:block w-24 h-36 relative flex-shrink-0 rounded overflow-hidden">
                    <Image
                      src={currentParty.content.posterPath}
                      alt={currentParty.content.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}

                <div className="flex-1">
                  <h2 className="text-xl font-bold text-vista-light mb-1">
                    {currentParty.content?.title || "Unknown Title"}
                  </h2>

                  <div className="flex flex-wrap gap-2 mb-2">
                    {currentParty.content?.genres?.map((genre, i) => (
                      <Badge key={i} variant="secondary" className="bg-vista-dark border-vista-light/10">
                        {genre}
                      </Badge>
                    ))}

                    {currentParty.content?.year && (
                      <Badge variant="secondary" className="bg-vista-dark border-vista-light/10">
                        {currentParty.content.year}
                      </Badge>
                    )}

                    {currentParty.content?.rating && (
                      <Badge variant="secondary" className="bg-vista-dark border-vista-light/10">
                        ★ {currentParty.content.rating.toFixed(1)}
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-vista-light/70 line-clamp-3 mb-2">
                    {currentParty.content?.overview || "No description available."}
                  </p>

                  {/* Extra content metadata */}
                  <div className="text-xs text-vista-light/50">
                    {currentParty.content?.type === 'movie' && currentParty.content?.runtime && (
                      <span>{Math.floor(currentParty.content.runtime / 60)}h {currentParty.content.runtime % 60}m</span>
                    )}

                    {currentParty.content?.type === 'show' && (
                      <span>Season {currentParty.currentSeason || 1}, Episode {currentParty.currentEpisode || 1}</span>
                    )}

                    <span className="ml-2">• Party ID: {currentParty.id.slice(0, 8)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat & Members Column */}
        <div>
          <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-2 w-full bg-vista-dark-lighter">
              <TabsTrigger value="chat" className="data-[state=active]:bg-vista-blue">
                <MessageCircle className="w-4 h-4 mr-2" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="members" className="data-[state=active]:bg-vista-blue">
                <Users className="w-4 h-4 mr-2" />
                Members ({currentParty.members.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="mt-3">
              <Card className="bg-vista-dark-lighter border-vista-light/10">
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <ScrollArea
                      ref={chatScrollRef}
                      className="h-[400px] pr-4"
                    >
                      {currentParty.messages.length === 0 ? (
                        <div className="text-center py-8 text-vista-light/50">
                          <MessageCircle className="w-12 h-12 mx-auto mb-2 opacity-20" />
                          <p className="text-sm">No messages yet.</p>
                          <p className="text-xs">Be the first to say something!</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {currentParty.messages.map((msg, i) => (
                            <div key={i} className="flex items-start gap-3">
                              <Avatar className="w-8 h-8 flex-shrink-0">
                                <AvatarImage src={`https://api.dicebear.com/7.x/bottts/svg?seed=${msg.memberName}`} />
                                <AvatarFallback className="text-xs bg-vista-blue">
                                  {msg.memberName.substring(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-baseline gap-2">
                                  <span className="font-medium text-vista-light truncate">
                                    {msg.memberName}
                                    {msg.memberId === currentParty.hostId && (
                                      <Badge className="ml-1.5 py-0 text-[0.6rem] bg-vista-blue/30 text-vista-blue border-vista-blue">
                                        Host
                                      </Badge>
                                    )}
                                  </span>
                                  <span className="text-xs text-vista-light/40">
                                    {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                  </span>
                                </div>

                                {msg.type === 'reaction' ? (
                                  <div className="text-2xl mt-1">{msg.content}</div>
                                ) : msg.type === 'system' ? (
                                  <div className="text-vista-light/50 text-sm italic">{msg.content}</div>
                                ) : (
                                  <div className="text-vista-light/90 break-words">{msg.content}</div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </ScrollArea>

                    <div className="pt-3 border-t border-vista-light/10">
                      <div className="flex gap-2 mb-2">
                        {["👍", "❤️", "😂", "😮", "👏"].map(reaction => (
                          <button
                            key={reaction}
                            className="text-xl hover:scale-125 transition-transform"
                            onClick={() => handleSendReaction(reaction)}
                          >
                            {reaction}
                          </button>
                        ))}
                      </div>

                      <div className="flex gap-2">
                        <Input
                          className="bg-vista-dark border-vista-light/20 flex-1"
                          placeholder="Type a message..."
                          value={message}
                          onChange={e => setMessage(e.target.value)}
                          onKeyDown={e => e.key === 'Enter' && handleSendMessage()}
                        />
                        <Button
                          className="bg-vista-blue hover:bg-vista-blue/90"
                          onClick={handleSendMessage}
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="members" className="mt-3">
              <Card className="bg-vista-dark-lighter border-vista-light/10">
                <CardContent className="p-4">
                  <ScrollArea className="h-[400px] pr-4">
                    <div className="space-y-4">
                      {currentParty.members.map((member, i) => (
                        <div key={i} className="flex items-center gap-3">
                          <Avatar className="w-10 h-10">
                            <AvatarImage src={`https://api.dicebear.com/7.x/bottts/svg?seed=${member.name}`} />
                            <AvatarFallback className="bg-vista-blue">
                              {member.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-vista-light truncate">
                                {member.name}
                              </span>

                              {member.isHost && (
                                <Badge className="bg-vista-blue/30 text-vista-blue border-vista-blue">
                                  Host
                                </Badge>
                              )}

                              {member.isReady && (
                                <Badge className="bg-green-500/20 text-green-400 border-green-500">
                                  Ready
                                </Badge>
                              )}
                            </div>

                            <p className="text-sm text-vista-light/50">
                              {member.joinedAt ? `Joined ${new Date(member.joinedAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` : 'Joining...'}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}