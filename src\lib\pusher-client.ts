import PusherClient from 'pusher-js';

// Initialize Pusher client with error handling
let pusherClientInstance: PusherClient | null = null;

try {
  // Only initialize if we have the required environment variables
  if (process.env.NEXT_PUBLIC_PUSHER_KEY && process.env.NEXT_PUBLIC_PUSHER_CLUSTER) {
    pusherClientInstance = new PusherClient(
      process.env.NEXT_PUBLIC_PUSHER_KEY,
      {
        cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER,
        enabledTransports: ['ws', 'wss'],
      }
    );
  } else {
    console.warn('Pusher not initialized: Missing environment variables');
  }
} catch (error) {
  console.error('Error initializing Pusher client:', error);
  pusherClientInstance = null;
}

// Export a safe version of the Pusher client with fallbacks for methods
export const pusherClient = pusherClientInstance || {
  subscribe: () => ({
    bind: () => {},
    unbind: () => {},
  }),
  unsubscribe: () => {},
  disconnect: () => {},
  connect: () => {},
};

// Helper function to safely disconnect Pusher
export const disconnectPusher = () => {
  try {
    if (pusherClientInstance) {
      pusherClientInstance.disconnect();
    }
  } catch (error) {
    console.error('Error disconnecting Pusher:', error);
  }
};
