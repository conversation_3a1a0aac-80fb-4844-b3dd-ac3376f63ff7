'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DetailBanner from '@/components/DetailBanner';
import EpisodeList from '@/components/EpisodeList';
import RelatedContent from '@/components/RelatedContent';
import { Button } from '@/components/ui/button';
import { Navbar } from '@/components/Navbar'; // Updated import statement
import Footer from '@/components/Footer';
import { ContentCardType } from '@/lib/content-utils';
import { Play, Info } from 'lucide-react';
import { getTVShowDetails, getTVSeasonDetails } from '@/lib/tmdb-api';
import { Episode } from '@/types/index';

// Sample detail data
const detailData = {
  id: 'stranger-things',
  title: 'Stranger Things',
  type: 'shows',
  description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces and one strange little girl.',
  releaseYear: 2016,
  ageRating: 'TV-14',
  creators: 'The Duffer Brothers',
  starring: ['<PERSON><PERSON>', '<PERSON>', 'Finn Wolfhard'], // Changed to array
  genres: ['Sci-Fi', 'Horror', 'Drama'],
  seasons: 4,
  imagePath: 'https://ext.same-assets.com/787489437/5677834534.jpg',
  bannerImage: 'https://ext.same-assets.com/349857/3458973451.jpg',
  duration: '45-60 min', // Added duration for DetailBanner
  imdbId: 'tt4574334', // IMDb ID for Stranger Things

  // OMDB-specific fields
  director: '',
  actors: [],
  awards: '',
  rated: '',
  released: '',
  metascore: 0,
  dataSource: 'tmdb',
  tmdbId: '66732', // TMDb ID for Stranger Things
  seasonData: {
    totalSeasons: 4,
    currentSeason: 1,
    currentEpisode: 1,
    episodeCount: 32
  }
};

// Mock data for related content
const relatedContent: ContentCardType[] = [
  {
    id: 'dark',
    title: 'Dark',
    imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125',
    type: 'shows',
    year: '2017',
    ageRating: 'TV-MA'
  },
  {
    id: 'mind-hunter',
    title: 'Mindhunter',
    imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125',
    type: 'shows',
    year: '2017',
    ageRating: 'TV-MA'
  },
  {
    id: 'the-oa',
    title: 'The OA',
    imagePath: 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125',
    type: 'shows',
    year: '2016',
    ageRating: 'TV-MA'
  }
];

interface DetailClientProps {
  params: {
    type: string;
    id: string;
  }
}

export default function DetailClient({ params }: DetailClientProps) {
  const { type, id } = params;
  const router = useRouter();
  const [data, setData] = useState<typeof detailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [seasons, setSeasons] = useState<{ seasonNumber: number; episodes: Episode[] }[]>([]);
  const [isLoadingSeasons, setIsLoadingSeasons] = useState(true);
  const [seasonCount, setSeasonCount] = useState(0);

  // Fetch data from API
  useEffect(() => {
    const fetchContentDetails = async () => {
      try {
        setLoading(true);

        // Log the received params for debugging
        console.log('DetailClient received params:', { type, id });

        // More robust ID validation
        if (!id || id === 'undefined' || id === 'null' || id === '[object Object]') {
          console.error('Invalid ID detected:', id);
          throw new Error(`Invalid content ID: ${id}`);
        }

        // Fetch content details from our API
        const contentType = type === 'shows' ? 'show' : 'movie';
        console.log(`DetailClient: Fetching content details for ${contentType} with ID: ${id}`);
        const response = await fetch(`/api/content?id=${id}&type=${contentType}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch content details: ${response.status}`);
        }

        const contentData = await response.json();

        // Map API data to our UI format
        setData({
          id: contentData.id.toString(),
          title: contentData.title,
          type: type,
          description: contentData.overview || '',
          releaseYear: contentData.year ? parseInt(contentData.year) : 0,
          ageRating: contentData.rated || (contentType === 'movie' ? 'PG-13' : 'TV-14'),
          creators: contentData.director || contentData.creator || '',
          starring: contentData.actors || [],
          genres: contentData.genres || [],
          seasons: contentData.seasons || 1,
          imagePath: contentData.posterPath || '',
          bannerImage: contentData.backdropPath || contentData.posterPath || '',
          duration: contentData.runtime ? `${contentData.runtime} min` : '45-60 min',
          imdbId: contentData.imdbId || '',
          tmdbId: contentData.tmdbId || '',

          // OMDB-specific fields
          director: contentData.director || '',
          actors: contentData.actors || [],
          awards: contentData.awards || '',
          rated: contentData.rated || '',
          released: contentData.released || '',
          metascore: contentData.metascore || 0,
          dataSource: contentData.dataSource || 'tmdb'
        });
      } catch (error) {
        console.error('Error fetching content details:', error);
        // Set error state
        setError(error instanceof Error ? error.message : 'Failed to load content details');
        // Fallback to sample data
        setData({
          ...detailData,
          id,
          type,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchContentDetails();
  }, [id, type]);

  // Fetch seasons and episodes from TMDB API
  useEffect(() => {
    const fetchSeasonData = async () => {
      if (!data || data.type !== 'shows' || !data.tmdbId) return;

      setIsLoadingSeasons(true);

      try {
        // First get the TV show details to find out how many seasons there are
        const showDetails = await getTVShowDetails(data.tmdbId);
        setSeasonCount(showDetails.number_of_seasons || 0);

        // Fetch the first season initially
        const season1Data = await getTVSeasonDetails(data.tmdbId, 1);

        // Transform the episode data to match our expected format
        const formattedSeason = {
          seasonNumber: 1,
          episodes: season1Data.episodes.map((episode: any) => ({
            id: `${episode.id}`,
            title: episode.name,
            episodeNumber: episode.episode_number,
            seasonNumber: episode.season_number,
            description: episode.overview,
            thumbnail: episode.still_path ? `https://image.tmdb.org/t/p/w780${episode.still_path}` : '',
            runtime: episode.runtime || 0,
            airDate: episode.air_date
          }))
        };

        setSeasons([formattedSeason]);
        setIsLoadingSeasons(false);
      } catch (error) {
        console.error('Error fetching TV show seasons:', error);
        setIsLoadingSeasons(false);
      }
    };

    fetchSeasonData();
  }, [data]);

  if (loading) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !data || !id || id === 'undefined' || id === 'null' || id === '[object Object]') {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center p-8 bg-vista-dark-lighter rounded-lg max-w-md">
          <Info className="h-16 w-16 text-vista-light/30 mx-auto mb-4" />
          <h2 className="text-2xl text-vista-light font-bold">Content not found</h2>
          <p className="text-vista-light/70 mt-2">
            {error || `Unable to load content details. Invalid ID: ${id}`}
          </p>
          <Button
            className="mt-6 bg-vista-blue hover:bg-vista-blue/90 text-white"
            onClick={() => router.push('/')}
          >
            Return Home
          </Button>
        </div>
      </div>
    );
  }

  const isShow = type === 'shows';

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      {/* Detail Banner */}
      <DetailBanner
        title={data.title}
        description={data.description}
        year={data.releaseYear}
        ageRating={data.ageRating}
        duration={data.duration}
        genres={data.genres}
        starring={data.starring}
        creator={data.creators}
        imagePath={data.bannerImage || data.imagePath}
        director={data.director}
        awards={data.awards}
        rated={data.rated}
        released={data.released}
        metascore={data.metascore}
      />

      {/* Content Section */}
      <div className="container mx-auto px-4 py-8">
        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 mb-8">
          <Button
            className="bg-vista-blue hover:bg-vista-blue/90 text-white gap-2 rounded-full px-6"
            onClick={() => {
              // For both movies and shows, redirect to the watch page with the proper ID
              const contentType = type === 'shows' ? 'show' : 'movie';
              const idType = data.imdbId ? 'imdb' : 'tmdb';
              const id = data.imdbId || data.tmdbId;

              if (id) {
                // Use the direct watch route with the ID and forcePlay flag
                router.push(`/watch/${id}?forcePlay=true&contentType=${contentType}`);
              } else {
                // Fallback to content ID based routing if external IDs are not available
                router.push(`/watch/${data.id}?forcePlay=true&contentType=${contentType}`);
              }
            }}
          >
            <Play className="h-4 w-4" />
            {isShow ? 'Watch S1:E1' : 'Watch Now'}
          </Button>
          <Button
            className="bg-vista-accent hover:bg-vista-accent-dim text-white gap-2 rounded-full px-6"
            onClick={() => router.push(`/watch-party/create?content=${data.id}`)}
          >
            Watch Party
          </Button>
          <Button variant="outline" className="border-vista-light/30 text-vista-light hover:bg-vista-light/10 gap-2 rounded-full">
            Add to My List
          </Button>
          <Button variant="outline" className="border-vista-light/30 text-vista-light hover:bg-vista-light/10 gap-2 rounded-full">
            Share
          </Button>
        </div>

        {/* Episode List (only for shows) */}
        {isShow && (
          <section className="mb-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Episodes</h2>
              <div className="flex items-center gap-2">
                <span className="text-sm text-vista-light/70">Season</span>
                <select className="bg-vista-dark-lighter border border-vista-light/20 rounded text-sm py-1 px-2">
                  {Array.from({ length: data.seasons || 1 }, (_, i) => (
                    <option key={i + 1} value={i + 1}>Season {i + 1}</option>
                  ))}
                </select>
              </div>
            </div>
            <EpisodeList
              seasons={seasons}
              showTitle={data.title}
              imdbId={data.imdbId}
              tmdbId={data.tmdbId}
              contentId={data.id}
              seasonCount={seasonCount}
              isLoading={isLoadingSeasons}
            />
          </section>
        )}

        {/* Details Section */}
        <section className="mb-10">
          <h2 className="text-xl font-semibold mb-4">Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-vista-light/70 mb-6 max-w-xl">
                {data.description}
              </p>
              <div className="space-y-2">
                <p><span className="text-vista-light/50 font-medium">Starring:</span> {data.starring.join(', ')}</p>
                {data.director && (
                  <p><span className="text-vista-light/50 font-medium">Director:</span> {data.director}</p>
                )}
                <p><span className="text-vista-light/50 font-medium">Creators:</span> {data.creators}</p>
                <p><span className="text-vista-light/50 font-medium">Genres:</span> {data.genres?.join(', ')}</p>
                {data.released && (
                  <p><span className="text-vista-light/50 font-medium">Released:</span> {data.released}</p>
                )}
                {data.rated && (
                  <p><span className="text-vista-light/50 font-medium">Rated:</span> {data.rated}</p>
                )}
                {data.awards && (
                  <p><span className="text-vista-light/50 font-medium">Awards:</span> {data.awards}</p>
                )}
                {data.metascore > 0 && (
                  <p><span className="text-vista-light/50 font-medium">Metascore:</span> {data.metascore}</p>
                )}
                {data.imdbId && (
                  <p>
                    <span className="text-vista-light/50 font-medium">IMDb:</span>{' '}
                    <a
                      href={`https://www.imdb.com/title/${data.imdbId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-vista-blue hover:underline"
                    >
                      {data.imdbId}
                    </a>
                  </p>
                )}
                {data.tmdbId && (
                  <p><span className="text-vista-light/50 font-medium">TMDb ID:</span> {data.tmdbId}</p>
                )}
                {data.dataSource && (
                  <p><span className="text-vista-light/50 font-medium">Data Source:</span> {data.dataSource}</p>
                )}
              </div>
            </div>
            <div>
              {isShow && (
                <div>
                  <h3 className="text-sm font-medium text-vista-light/70 mb-2">SEASONS</h3>
                  <div className="grid grid-cols-3 gap-4">
                    {Array.from({ length: data.seasons || 1 }, (_, i) => (
                      <Link
                        key={i}
                        href="#"
                        className="block p-4 bg-vista-dark-lighter rounded-lg hover:bg-vista-light/10 transition-colors text-center"
                      >
                        Season {i + 1}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Related Content */}
        <RelatedContent
          title="More Like This"
          contents={relatedContent}
        />
      </div>

      <Footer />
    </div>
  );
}
