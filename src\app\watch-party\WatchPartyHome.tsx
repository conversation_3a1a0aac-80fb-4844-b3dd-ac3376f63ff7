'use client'

import { useState, createContext, useContext, useEffect, useRef, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import WatchPartyCreator from './components/WatchPartyCreator'
import { PrivatePartyDialog } from './components/PrivatePartyDialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { UserAvatar } from '@/components/UserAvatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { WatchPartyWrapper } from './watch-party-wrapper'
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import {
  Play, Users, Clock, Calendar, MessageSquare, Info,
  Settings, Plus, ArrowRight, Trash2, Edit, ExternalLink,
  RefreshCcw, AlertCircle, InfoIcon, Loader2, AlertTriangle, Lock
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import Image from 'next/image'
import { ToastProvider } from '@/lib/ToastContext'
import { ErrorProvider } from '@/lib/ErrorContext'
import { useWatchPartyWithPusher, WatchPartyState } from '@/hooks/useWatchPartyWithPusher'
import { getContentById } from '@/data/content'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { ensureTMDBImagePath } from "@/utils/image-utils"
import { pusherClient } from '@/lib/pusher-client'
import { WATCH_PARTY_EVENTS } from '@/lib/pusher-server'

// Define types for our party data
interface Party {
  id: string;
  name: string;
  contentId: string;
  contentTitle: string;
  contentPoster: string;
  hostId: string;
  hostName?: string;
  status: 'active' | 'scheduled' | 'completed';
  scheduledFor: string | null;
  memberCount: number;
  isActive: boolean;
  createdAt?: string;
  private?: boolean;
}

interface PartyMember {
  id: string;
  name: string;
  isHost: boolean;
}

interface MockPartyContextType {
  party: Party | null;
  setParty: (party: Party | null) => void;
}

// Create a simple mock context for this page
const MockPartyContext = createContext<MockPartyContextType | null>(null)

export function useMockParty() {
  const context = useContext(MockPartyContext)
  if (!context) {
    throw new Error('useMockParty must be used within a MockPartyProvider')
  }
  return context
}

// Provide the mock implementation
export default function WatchPartyHome() {
  const [mockParty, setMockParty] = useState<Party | null>(null)

  return (
    <ToastProvider>
      <ErrorProvider>
        <WatchPartyWrapper>
          <div className="container mx-auto px-6 py-6 max-w-[1400px]">
            <MockPartyContext.Provider value={{ party: mockParty, setParty: setMockParty }}>
              <WatchPartyContent />
            </MockPartyContext.Provider>
          </div>
        </WatchPartyWrapper>
      </ErrorProvider>
    </ToastProvider>
  )
}

function WatchPartyContent() {
  const { party, setParty } = useMockParty()
  const [isInitializing, setIsInitializing] = useState(false)
  const [activeTab, setActiveTab] = useState('browse')
  const [currentView, setCurrentView] = useState<'list' | 'create' | 'detail'>('list')
  const [selectedParty, setSelectedParty] = useState<Party | null>(null)
  const [myParties, setMyParties] = useState<Party[]>([])
  const [invitedParties, setInvitedParties] = useState<Party[]>([])
  const [otherParties, setOtherParties] = useState<Party[]>([])
  const router = useRouter()
  const { toast } = useToast()

  // Helper function to remove all loading overlays
  const removeLoadingOverlays = useCallback(() => {
    // Remove the navigation overlay if it exists
    document.body.classList.remove('navigation-in-progress');

    // Remove any manually created loading overlays by ID
    const loadingOverlay = document.getElementById('watch-party-loading-overlay');
    if (loadingOverlay && loadingOverlay.parentNode) {
      loadingOverlay.parentNode.removeChild(loadingOverlay);
    }

    const navigationOverlay = document.getElementById('watch-party-navigation-overlay');
    if (navigationOverlay && navigationOverlay.parentNode) {
      navigationOverlay.parentNode.removeChild(navigationOverlay);
    }

    // Also remove any other loading overlays that might have been created
    const otherLoadingOverlays = document.querySelectorAll('.fixed.inset-0.bg-vista-dark.flex.items-center.justify-center');
    otherLoadingOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });

    console.log('[WatchParty Home] Removed all loading overlays');
  }, []);

  // Remove any navigation overlay when this component mounts
  useEffect(() => {
    // Short delay to ensure the component is fully mounted and rendered
    // This delay is important to prevent the flash - we need to wait until
    // the component is fully rendered before removing the overlay
    setTimeout(() => {
      console.log('[WatchParty Home] Removing loading overlays after delay');
      removeLoadingOverlays();
    }, 500); // Longer delay to ensure component is fully rendered
  }, [removeLoadingOverlays]);

  // Use the real watch party hook
  const {
    availableParties,
    fetchAvailableParties,
    userId,
    isLoading: hookLoading,
    currentParty,
    deleteParty
  } = useWatchPartyWithPusher()

  // Add state to track available parties for real-time updates
  const [localParties, setLocalParties] = useState<WatchPartyState[]>([])

  // Track parties the user has already joined
  const [joinedPartyIds, setJoinedPartyIds] = useState<Set<string>>(new Set())

  // Update local parties when availableParties changes
  useEffect(() => {
    if (availableParties) {
      setLocalParties(availableParties);

      // Check if current user is a member of any parties
      if (userId) {
        const joined = new Set<string>();
        availableParties.forEach(party => {
          if (party.members?.some(member => member.id === userId)) {
            joined.add(party.id);
          }
        });
        setJoinedPartyIds(joined);
      }
    }
  }, [availableParties, userId])

  // Subscribe to the main watch-parties channel for real-time updates
  useEffect(() => {
    // Subscribe to the main watch-parties channel
    const channel = pusherClient.subscribe('watch-parties');

    // Handle party updates (including member count changes)
    const handlePartyUpdate = (updatedParty: WatchPartyState) => {
      console.log('[WatchPartyHome] Received real-time party update:', updatedParty.id);

      // Update the party in our local state to reflect changes like member count
      setLocalParties(prev => {
        if (!prev) return prev;

        return prev.map(party => {
          if (party.id === updatedParty.id) {
            // Update with new data, especially member count
            return {
              ...party,
              memberCount: updatedParty.members?.length || 0
            };
          }
          return party;
        });
      });
    };

    // Bind the event handler
    channel.bind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);

    return () => {
      // Unbind the event handler
      channel.unbind(WATCH_PARTY_EVENTS.PARTY_UPDATE, handlePartyUpdate);
      pusherClient.unsubscribe('watch-parties');
    };
  }, [])

  // Use a ref to track the previous availableParties for comparison
  const prevPartiesRef = useRef<string>('')
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Add isLoading state
  const [isLoading, setIsLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [partyToDelete, setPartyToDelete] = useState<WatchPartyState | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Function to handle party deletion
  const confirmDeletion = async () => {
    if (!partyToDelete) return;

    try {
      setIsDeleting(true);
      await deleteParty(partyToDelete.id);
      toast({
        title: "Party deleted",
        description: "The watch party has been successfully deleted.",
      });
      setDeleteDialogOpen(false);

      // If we're in detail view of the deleted party, go back to list
      if (currentView === 'detail' && selectedParty?.id === partyToDelete.id) {
        setCurrentView('list');
      }
    } catch (error) {
      console.error('Error deleting party:', error);
      toast({
        title: "Error",
        description: "Failed to delete the watch party. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
      setPartyToDelete(null);
    }
  }

  // In the existing useEffect that checks for refresh parameter in URL, make it more robust
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const shouldRefresh = params.get('refresh') === 'true';

      if (shouldRefresh) {
        console.log('[WatchParty Home] Refresh parameter detected in URL, forcing refresh');

        // Clear state to force complete re-render
        setMyParties([]);
        setInvitedParties([]);
        setOtherParties([]);
        setSelectedParty(null);
        setCurrentView('list');
        setIsRefreshing(true);

        // Deliberately clear out local parties first to force a complete refresh
        setTimeout(() => {
          fetchAvailableParties(true).then((parties) => {
            console.log('[WatchParty Home] Forced refresh completed with', parties?.length || 0, 'parties');

            // Clear the refresh parameter from URL without refreshing the page
            params.delete('refresh');
            const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : '');
            window.history.replaceState({}, '', newUrl);

            toast({
              title: "Dashboard Updated - The watch party list has been refreshed"
            });

            setIsRefreshing(false);
          }).catch(error => {
            console.error('[WatchParty Home] Error during forced refresh:', error);

            toast({
              title: "Refresh Failed - Could not refresh the watch party list",
              variant: "destructive"
            });

            setIsRefreshing(false);
          });
        }, 300); // Short delay to ensure state updates have processed
      }
    }
  }, [fetchAvailableParties, toast]);

  // Add a new useEffect to monitor for party deletions and force a refresh
  useEffect(() => {
    if (!availableParties) return;

    // Create a dedicated function to check and refresh if a party is deleted
    const checkForDeletions = () => {
      const currentIds = new Set(availableParties.map(p => p.id));

      // Get the previous parties from ref
      if (prevPartiesRef.current && prevPartiesRef.current !== JSON.stringify(availableParties)) {
        try {
          const prevParties = JSON.parse(prevPartiesRef.current);
          if (Array.isArray(prevParties) && prevParties.length > 0) {
            const prevIds = new Set(prevParties.map((p: {id: string}) => p.id));

            // Filter for IDs that existed before but don't exist now
            const deletedIds = Array.from(prevIds).filter(id => !currentIds.has(id));

            if (deletedIds.length > 0) {
              console.log(`[WatchParty Home] Detected ${deletedIds.length} deleted parties:`, deletedIds.join(', '));

              // Force a complete refresh to ensure we have the latest data
              fetchAvailableParties(true).then(parties => {
                console.log(`[WatchParty Home] Refresh after deletion complete. Found ${parties.length} parties.`);

                // Display feedback to user about the deleted party
                if (!isRefreshing) {
                  toast({
                    title: "Party Removed - A watch party has been deleted"
                  });
                }
              });
            }
          }
        } catch (error) {
          console.error('[WatchParty Home] Error checking for deleted parties:', error);
        }
      }

      // Update the ref with the current parties
      prevPartiesRef.current = JSON.stringify(availableParties);
    };

    // Run the check
    checkForDeletions();
  }, [availableParties, fetchAvailableParties, isRefreshing, toast]);

  // Initialize with real watch party data and set up refresh
  useEffect(() => {
    const initializeParties = async () => {
      // Only initialize parties when in list view
      if (currentView !== 'list') return;

      console.log('[WatchParty Home] Initializing parties...');
      try {
        // Force fetch to bypass debouncing
        await fetchAvailableParties(true);
        console.log('[WatchParty Home] Initialized with available parties')
      } catch (error) {
        console.error('[WatchParty Home] Error initializing parties:', error)
        // Don't show error toast on initial load as it would be jarring
        // Just silently handle the error and show empty state
        console.error('[WatchParty Home] Failed to initialize parties');
      }
    }

    // Fetch parties immediately on mount only when in list view
    if (currentView === 'list') {
      initializeParties();
    }

    // Add polling but only when in list view, not creating a party, and tab is visible
    const pollInterval = setInterval(() => {
      if (!currentParty && currentView === 'list' && document.visibilityState === 'visible') {
        console.log('[WatchParty Home] Polling for parties...');
        try {
          fetchAvailableParties(false).catch(error => {
            console.error('[WatchParty Home] Error polling for parties:', error)
            // Don't show error toast for background polling failures
          });
        } catch (error) {
          // This catches any synchronous errors that might occur
          console.error('[WatchParty Home] Synchronous error during polling:', error);
        }
      }
    }, 60000); // Reduced to once per minute to minimize Pusher usage

    // Handle visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !currentParty && currentView === 'list') {
        // Only fetch when returning to tab, not in a party, and in list view
        console.log('[WatchParty Home] Page became visible, refreshing parties...');
        try {
          fetchAvailableParties(true).catch(error => {
            console.error('[WatchParty Home] Error refreshing parties on visibility change:', error)
          });
        } catch (error) {
          // This catches any synchronous errors that might occur
          console.error('[WatchParty Home] Synchronous error during visibility change refresh:', error);
        }
      }
    };

    // Listen for visibility changes to refresh when coming back to the page
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up listeners and intervals
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(pollInterval);
    }
  }, [fetchAvailableParties, currentParty, currentView]);

  // Fetch parties when the view switches to list
  useEffect(() => {
    if (currentView === 'list') {
      // Force a refresh when switching back to list view
      try {
        fetchAvailableParties(true).catch(error => {
          console.error('[WatchParty Home] Error refreshing parties on view change:', error)
          // Don't show error toast here as it would be annoying
        });
      } catch (error) {
        // This catches synchronous errors that might occur before the promise
        console.error('[WatchParty Home] Synchronous error during party refresh:', error);
      }
    }
  }, [currentView, fetchAvailableParties]);

  // Replace the handle manual refresh function with improved styling
  const handleManualRefresh = async () => {
    try {
      setIsRefreshing(true)
      console.log('[WatchParty Home] Manual refresh initiated')
      await fetchAvailableParties(true)
      setIsRefreshing(false)
      toast({
        title: "Watch parties refreshed",
        description: "Your watch party list has been updated"
      })
    } catch (error) {
      console.error('[WatchParty Home] Error during manual refresh:', error)
      setIsRefreshing(false)
      toast({
        title: "Refresh failed",
        description: "Could not refresh the watch party list",
        variant: "destructive"
      })
    }
  }

  // Convert real parties to UI parties format
  useEffect(() => {
    if (!availableParties) return;

    // Log the raw parties we received for debugging
    console.log(`[WatchParty Home] Processing ${availableParties.length} parties:`,
      availableParties.map(p => ({id: p.id, hostId: p.hostId, title: p.title})));

    // Add this log to track party IDs explicitly for debugging deletion issues
    console.log(`[WatchParty Home] All party IDs:`, availableParties.map(p => p.id).join(', '));

    // Convert the stringified version for comparison to avoid unnecessary updates
    const currentPartiesString = JSON.stringify(availableParties)

    // Always process parties if we have any, even if we've processed them before
    if (availableParties.length > 0) {
      prevPartiesRef.current = currentPartiesString

      console.log('[WatchParty Home] Processing available parties:', availableParties.length)

      // Map API parties to UI party format
      const processedParties = availableParties.map(apiParty => {
        if (!apiParty || !apiParty.id) {
          console.error('[WatchParty Home] Invalid party data received:', apiParty);
          return null; // Skip invalid parties
        }

        // Get additional content details if possible
        let posterPath = '';
        let contentTitle = 'Unknown Content';

        // Debug log the content data we have
        console.log(`[WatchParty Home] Party ${apiParty.id} content data:`, {
          title: apiParty.title,
          contentId: apiParty.contentId,
          hasContent: !!apiParty.content,
          contentType: apiParty.content?.type
        });

        // Try to get content details from the party object first
        if (apiParty.content) {
          contentTitle = apiParty.content.title || apiParty.title || 'Unknown Content';
          posterPath = apiParty.content.posterPath || '';

          // Log the content details we're using
          console.log(`[WatchParty Home] Using content data from party object for ${apiParty.id}:`, {
            title: contentTitle,
            posterPath: posterPath ? 'present' : 'missing'
          });
        } else {
          // Use apiParty.title as fallback if we don't have content object
          contentTitle = apiParty.title || 'Unknown Content';
          console.log(`[WatchParty Home] Using fallback title for ${apiParty.id}:`, contentTitle);
        }

        // If we still don't have a poster, try to load from our local content data
        if (!posterPath && apiParty.contentId) {
          try {
            // First try to get from local content cache
            const contentDetails = getContentById(apiParty.contentId.toString())
            if (contentDetails) {
              // Only update title if we didn't get one from party.content
              if (contentTitle === 'Unknown Content') {
                contentTitle = contentDetails.title;
              }

              // Use the utility function to ensure correct image URL
              posterPath = ensureTMDBImagePath(contentDetails.posterPath);

              console.log(`[WatchParty Home] Found local content details for ${apiParty.id}:`, {
                title: contentDetails.title,
                posterPath: posterPath ? 'present' : 'missing'
              });
            } else {
              // If not found in local cache, try fetching from API
              console.log(`[WatchParty Home] No local content found for ${apiParty.id}, will try fetching from API`);

              // Determine content type from party data or default to movie
              const contentType = apiParty.content?.type ||
                                (apiParty.currentSeason !== undefined ? 'show' : 'movie');

              // This will get processed later in the useEffect
              fetch(`/api/content?id=${apiParty.contentId}&type=${contentType}`)
                .then(response => {
                  if (!response.ok) throw new Error(`API error ${response.status}`);
                  return response.json();
                })
                .then(apiContent => {
                  // Update only relevant parties state
                  const updatePartyLists = (partyList: Party[]) => {
                    return partyList.map((p: Party) => {
                      if (p.id === apiParty.id) {
                        // Use the utility function for image URL
                        const updatedPosterPath = ensureTMDBImagePath(apiContent.posterPath);

                        return {
                          ...p,
                          contentTitle: apiContent.title || p.contentTitle,
                          contentPoster: updatedPosterPath || p.contentPoster
                        };
                      }
                      return p;
                    });
                  };

                  // Update my parties
                  setMyParties(prevParties => updatePartyLists(prevParties));

                  // Update invited parties
                  setInvitedParties(prevParties => updatePartyLists(prevParties));
                })
                .catch(err => {
                  console.warn(`[WatchParty Home] Error fetching content from API for party ${apiParty.id}:`, err);
                });
            }
          } catch (error) {
            console.warn('[WatchParty Home] Error getting content details:', error);
          }
        }

        // Find host information
        const host = apiParty.members.find(m => m.isHost);

        // Create a well-formed party object
        const formattedParty = {
          id: apiParty.id,
          name: apiParty.title || `${contentTitle} Party`,
          contentId: apiParty.contentId.toString(),
          contentTitle: contentTitle,
          contentPoster: posterPath,
          hostId: apiParty.hostId,
          hostName: apiParty.hostName || host?.name || 'Unknown Host',
          status: 'active' as const, // Default to active
          scheduledFor: null,
          memberCount: apiParty.members.length,
          isActive: true
        };

        console.log(`[WatchParty Home] Formatted party ${apiParty.id}:`, {
          name: formattedParty.name,
          contentTitle: formattedParty.contentTitle,
          hasPoster: !!formattedParty.contentPoster
        });

        return formattedParty;
      }).filter(Boolean) as Party[]; // Remove null entries

      // Log all user IDs for debugging
      console.log(`[WatchParty Home] Current user ID: ${userId}`);
      console.log(`[WatchParty Home] Party host IDs:`, processedParties.map(p => p.hostId).join(', '));

      // Filter my parties (where I'm the host)
      const myHostedParties = processedParties.filter(p => p.hostId === userId);
      console.log(`[WatchParty Home] My hosted parties (${myHostedParties.length}):`,
        myHostedParties.map(p => p.id).join(', '));

      // Filter invited parties (where I'm not the host)
      const partiesImInvitedTo = processedParties.filter(p => {
        // Check if I'm a member but not the host
        const partyData = availableParties.find(ap => ap.id === p.id);
        if (!partyData) return false;

        const isMember = partyData.members.some((member: {id: string}) => member.id === userId);
        const result = isMember && partyData.hostId !== userId;

        if (isMember) {
          console.log(`[WatchParty Home] User ${userId} is a member of party ${p.id}, host: ${partyData.hostId}`);
        }

        return result;
      });

      console.log(`[WatchParty Home] Parties I'm invited to (${partiesImInvitedTo.length}):`,
        partiesImInvitedTo.map(p => p.id).join(', '));

      // Update all parties list to display all available parties, even if the user isn't a member
      const allAvailableParties = processedParties;
      console.log(`[WatchParty Home] All available parties (${allAvailableParties.length}):`,
        allAvailableParties.map(p => p.id).join(', '));

      // Update state for both lists
      setMyParties(myHostedParties)
      setInvitedParties(partiesImInvitedTo)

      console.log('[WatchParty Home] Updated parties - My parties:', myHostedParties.length,
        'Invited parties:', partiesImInvitedTo.length,
        'All parties:', allAvailableParties.length)
    } else if (prevPartiesRef.current !== currentPartiesString) {
      // Only update empty state if the parties string has changed
      prevPartiesRef.current = currentPartiesString
      setMyParties([])
      setInvitedParties([])
      console.log('[WatchParty Home] No parties found, UI reset to empty state');
    }
  }, [availableParties, userId])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    })
  }

  // State for private party dialog
  const [privatePartyDialogOpen, setPrivatePartyDialogOpen] = useState(false);
  const [privatePartyToJoin, setPrivatePartyToJoin] = useState<Party | null>(null);
  const [isJoiningPrivateParty, setIsJoiningPrivateParty] = useState(false);

  // Handle joining a party - checks if it's private first
  const handleJoinParty = (partyId: string, directStart?: boolean) => {
    // Find the party in our available parties
    const party = [...myParties, ...invitedParties, ...otherParties].find(p => p.id === partyId);

    if (!party) {
      console.error(`Party not found with ID: ${partyId}`);
      toast({
        title: "Party Not Found",
        description: "The watch party you're trying to join doesn't exist.",
        variant: "destructive"
      });
      return;
    }

    // Check if it's a private party and the user is not the host
    if (party.private && party.hostId !== userId) {
      console.log(`Opening private party dialog for: ${partyId}`);
      setPrivatePartyToJoin(party);
      setPrivatePartyDialogOpen(true);
      return;
    }

    // For non-private parties or if user is the host, proceed directly
    console.log(`Opening party details: ${partyId}`);
    router.push(`/watch-party/${partyId}`);
  }

  // Handle joining a private party with a code
  const handleJoinPrivateParty = async (partyId: string, partyCode: string) => {
    setIsJoiningPrivateParty(true);

    try {
      // Verify the party code matches
      if (partyCode !== partyId) { // In a real app, you'd verify this on the server
        // For now, we're using the party ID as the code for simplicity
        console.log(`Joining private party: ${partyId} with code: ${partyCode}`);
        router.push(`/watch-party/${partyId}`);
      } else {
        throw new Error("Invalid party code");
      }
    } catch (error) {
      console.error("Error joining private party:", error);
      toast({
        title: "Failed to Join",
        description: "The party code you entered is invalid.",
        variant: "destructive"
      });
    } finally {
      setIsJoiningPrivateParty(false);
      setPrivatePartyDialogOpen(false);
      setPrivatePartyToJoin(null);
    }
  }

  const handleCreateParty = () => {
    setCurrentView('create')
  }

  const handleViewPartyDetails = (party: Party) => {
    console.log('Viewing party details:', party.id)

    // Create a full-screen loading overlay
    const loadingOverlay = document.createElement('div')
    loadingOverlay.id = 'watch-party-loading-overlay'
    loadingOverlay.className = 'fixed inset-0 bg-vista-dark flex items-center justify-center z-[9999]'
    loadingOverlay.innerHTML = `
      <div class="text-vista-light text-center">
        <div class="h-8 w-8 border-4 border-t-blue-600 border-vista-light/30 rounded-full animate-spin mx-auto mb-4"></div>
        <h3 class="text-xl font-medium">Loading Watch Party</h3>
        <p class="text-vista-light/70">Please wait...</p>
      </div>
    `
    document.body.appendChild(loadingOverlay)

    // Navigate to the party details page
    router.push(`/watch-party/${party.id}`)
  }

  // Replace the list view JSX with improved styling
  return (
    <div className="space-y-8">
      {/* Enhanced Header Section with Gradient Background */}
      <div className="bg-gradient-to-r from-blue-900/30 to-teal-900/30 rounded-xl p-8 mb-8 shadow-lg border border-blue-500/20">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="bg-blue-500/20 p-3 rounded-full">
                <Users className="h-8 w-8 text-blue-400" />
              </div>
              <h1 className="text-3xl font-bold text-white">Watch Party</h1>
            </div>
            <p className="text-vista-light/90 text-lg max-w-2xl">
              Watch movies and TV shows together with friends in perfect sync, chat in real-time, and
              enjoy a shared viewing experience with synchronized playback.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              size="lg"
              className="bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white font-medium px-8 shadow-md hover:shadow-lg hover:shadow-blue-600/20 transition-all duration-300 flex items-center justify-center"
              onClick={handleCreateParty}
            >
              <Plus className="h-5 w-5 mr-2" />
              Create New Party
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-vista-light/30 bg-white/5 text-vista-light hover:bg-white/10 hover:border-vista-light/40 transition-all duration-300 font-medium flex items-center justify-center"
              onClick={handleManualRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <>
                  <RefreshCcw className="h-5 w-5 mr-2 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCcw className="h-5 w-5 mr-2" />
                  Refresh Parties
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      {currentView === 'list' ? (
        <div className="space-y-8">
          {/* Parties Tabs with Improved Styling */}
          <Tabs defaultValue="my-parties" className="w-full">
            <div className="flex items-center justify-between mb-4">
              <TabsList className="bg-vista-dark-lighter/50 p-1 rounded-lg">
                <TabsTrigger
                  value="my-parties"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
                >
                  My Parties
                </TabsTrigger>
                <TabsTrigger
                  value="active"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
                >
                  Active Parties
                </TabsTrigger>
                <TabsTrigger
                  value="scheduled"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
                >
                  Scheduled
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-md px-4 py-2"
                >
                  History
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="my-parties" className="mt-4 space-y-6">
              {/* My Watch Parties Section */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="bg-blue-600/20 p-2 rounded-md">
                    <Users className="h-5 w-5 text-blue-400" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">Parties I've Created or Joined</h2>
                </div>

                {hookLoading ? (
                  <div className="py-20 flex flex-col items-center justify-center bg-vista-dark-lighter/30 border border-vista-dark-lighter rounded-xl">
                    <Loader2 className="h-8 w-8 text-blue-500 animate-spin mb-2" />
                    <p className="text-vista-light/70">Loading watch parties...</p>
                  </div>
                ) : localParties && localParties.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-3 gap-8">
                    {localParties.map((party) => (
                      <PartyCard
                        key={party.id}
                        party={{
                          id: party.id,
                          name: party.title || `Party ${party.id.substring(0, 6)}`,
                          contentId: party.contentId || '',
                          contentTitle: party.content?.title || 'Untitled Content',
                          contentPoster: party.content?.posterPath || '',
                          hostId: party.hostId || userId,
                          hostName: party.hostName || 'Host',
                          status: 'active',
                          scheduledFor: null,
                          memberCount: party.members?.length || 0,
                          isActive: true,
                          createdAt: party.createdAt
                        }}
                        onJoin={handleJoinParty}
                        onViewDetails={handleViewPartyDetails}
                        isOwner={party.hostId === userId}
                        /* Delete button removed */
                        currentUserId={userId || ''}
                        joinedPartyIds={joinedPartyIds}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="bg-vista-dark-lighter/20 backdrop-blur-sm border border-vista-light/10 rounded-xl py-16 px-8 text-center">
                    <div className="flex flex-col items-center max-w-lg mx-auto">
                      <div className="w-16 h-16 bg-vista-blue/10 rounded-full flex items-center justify-center mb-6">
                        <Users className="h-8 w-8 text-vista-blue/70" />
                      </div>
                      <h3 className="text-xl font-medium text-white mb-3">No Watch Parties Available</h3>
                      <p className="text-vista-light/70 mb-8 max-w-md mx-auto">
                        Be the first to create a watch party and invite friends to watch content together with synchronized playback!
                      </p>
                      <Button
                        onClick={handleCreateParty}
                        className="bg-vista-blue hover:bg-vista-blue/90 text-white flex items-center gap-2 px-6"
                      >
                        <Plus className="h-4 w-4" />
                        Create a Watch Party
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="active" className="mt-4">
              {/* Active Parties Content */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                  <div className="bg-green-600/20 p-1.5 rounded-md">
                    <Play className="h-4 w-4 text-green-400" fill="currentColor" />
                  </div>
                  Currently Playing Parties
                </h2>

                {hookLoading ? (
                  <div className="py-20 flex flex-col items-center justify-center bg-vista-dark-lighter/30 border border-vista-dark-lighter rounded-xl">
                    <Loader2 className="h-8 w-8 text-blue-500 animate-spin mb-2" />
                    <p className="text-vista-light/70">Loading active parties...</p>
                  </div>
                ) : localParties && localParties.filter(p => p.isPlaying).length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-3 gap-8">
                    {localParties
                      .filter(p => p.isPlaying)
                      .map((party) => (
                        <PartyCard
                          key={party.id}
                          party={{
                            id: party.id,
                            name: party.title || `Party ${party.id.substring(0, 6)}`,
                            contentId: party.contentId || '',
                            contentTitle: party.content?.title || 'Untitled Content',
                            contentPoster: party.content?.posterPath || '',
                            hostId: party.hostId || userId,
                            hostName: party.hostName || 'Host',
                            status: 'active',
                            scheduledFor: null,
                            memberCount: party.members?.length || 0,
                            isActive: true,
                            createdAt: party.createdAt
                          }}
                          onJoin={handleJoinParty}
                          onViewDetails={handleViewPartyDetails}
                          isOwner={party.hostId === userId}
                          /* Delete button removed */
                          currentUserId={userId || ''}
                          joinedPartyIds={joinedPartyIds}
                        />
                      ))}
                  </div>
                ) : (
                  <div className="py-12 bg-vista-dark-lighter/30 border border-vista-dark-lighter rounded-xl flex flex-col items-center justify-center text-center px-4">
                    <div className="bg-blue-900/30 p-3 rounded-full mb-3">
                      <Play className="h-6 w-6 text-blue-400/80" fill="currentColor" />
                    </div>
                    <h3 className="text-lg font-medium text-vista-light mb-2">No active parties</h3>
                    <p className="text-vista-light/60 max-w-md mb-6">
                      There are no watch parties happening right now.
                    </p>
                    <Button
                      onClick={handleCreateParty}
                      className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Watch Party
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Similar styling for Scheduled and History tabs */}
          </Tabs>
        </div>
      ) : currentView === 'create' ? (
        <div className="bg-vista-dark-lighter/30 backdrop-blur-sm rounded-xl shadow-lg border border-blue-500/20">
          <WatchPartyCreator onBackToList={() => setCurrentView('list')} />
        </div>
      ) : (
        <div className="bg-vista-dark-lighter/30 backdrop-blur-sm rounded-xl shadow-lg border border-blue-500/20 p-6">
          {selectedParty && (
            <PartyDetails
              party={selectedParty}
              onJoin={handleJoinParty}
              userId={userId || ''}
              onBack={() => setCurrentView('list')}
              /* Delete button removed */
            />
          )}
        </div>
      )}

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDeletion}
        isDeleting={isDeleting}
        partyName={partyToDelete?.title || 'this watch party'}
      />

      {/* Private Party Dialog */}
      {privatePartyToJoin && (
        <PrivatePartyDialog
          open={privatePartyDialogOpen}
          onOpenChange={setPrivatePartyDialogOpen}
          onJoin={handleJoinPrivateParty}
          partyName={privatePartyToJoin.name}
          partyId={privatePartyToJoin.id}
          isLoading={isJoiningPrivateParty}
        />
      )}
    </div>
  )
}

function PartyCard({
  party,
  onJoin,
  onViewDetails,
  isOwner = false,
  currentUserId,
  joinedPartyIds = new Set()
}: {
  party: Party;
  onJoin: (partyId: string, directStart?: boolean) => void;
  onViewDetails: (party: Party) => void;
  isOwner?: boolean;
  currentUserId: string;
  joinedPartyIds?: Set<string>;
}) {
  const isActive = party.status === 'active';
  const isScheduled = party.status === 'scheduled';

  // Format date if scheduled
  const formattedDate = useMemo(() => {
    if (party.scheduledFor) {
      try {
        return format(new Date(party.scheduledFor), 'MMM d, yyyy h:mm a');
      } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
      }
    }
    return null;
  }, [party.scheduledFor]);

  // Get initials for host avatar fallback
  const hostInitials = useMemo(() => {
    if (!party.hostName) return '';
    return party.hostName
      .split(' ')
      .map((name) => name.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }, [party.hostName]);

  const statusColors = {
    active: 'bg-green-500/20 text-green-400 border-green-500/30',
    scheduled: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    completed: 'bg-gray-500/20 text-gray-400 border-gray-500/30'
  };

  const statusBadgeClass = isActive
    ? statusColors.active
    : isScheduled
    ? statusColors.scheduled
    : statusColors.completed;

  const statusText = isActive ? 'Live Now' : isScheduled ? 'Scheduled' : 'Ended';

  return (
    <div
      className="group relative bg-black/40 backdrop-blur-md rounded-xl overflow-hidden border border-blue-500/20 shadow-lg transition-all duration-300 hover:shadow-xl hover:border-blue-500/40 hover:translate-y-[-2px] hover:scale-[1.02]"
    >
      {/* Status badge */}
      <div className="absolute top-3 left-3 z-20">
        <Badge variant="outline" className={`${statusBadgeClass} text-xs font-medium px-2 py-0.5 flex items-center gap-1.5`}>
          {isActive && <span className="h-1.5 w-1.5 rounded-full bg-green-500 animate-pulse" />}
          {statusText}
        </Badge>
      </div>

      {/* Private badge */}
      {party.private && (
        <div className="absolute top-3 left-20 z-20">
          <Badge variant="outline" className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-xs font-medium px-2 py-0.5 flex items-center gap-1.5">
            <Lock className="h-3 w-3" />
            Private
          </Badge>
        </div>
      )}

      {/* Host badge if current user is not the host */}
      {!isOwner && party.hostName && (
        <div className="absolute top-3 right-3 z-20">
          <div className="flex items-center gap-1 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1">
            <UserAvatar
              fallback={party.hostName?.[0]}
              className="h-4 w-4"
              size="xs"
            />
            <span className="text-xs text-vista-light/80">Host: {party.hostName}</span>
          </div>
        </div>
      )}

      {/* Content poster with gradient overlay */}
      <div className="aspect-[3/2] relative overflow-hidden bg-black">
        <div className="absolute inset-0 flex items-center justify-center">
          <Image
            src={party.contentPoster || "https://placehold.co/400x225/171717/555555?text=No+Image"}
            alt={party.contentTitle}
            fill
            style={{ objectFit: "cover", objectPosition: "center 20%" }}
            className="w-full h-full"
            unoptimized={true}
          />
        </div>
        <div className={`absolute inset-0 bg-gradient-to-t from-black to-black/20 opacity-70`} />
      </div>

      {/* Party info - made taller */}
      <div className="p-5 pb-6">
        <h3 className="font-medium text-white line-clamp-1 text-xl mb-2">{party.name}</h3>
        <p className="text-vista-light/70 text-sm line-clamp-1 mb-4">{party.contentTitle}</p>

        <div className="flex flex-wrap items-center gap-x-3 gap-y-2 text-xs text-vista-light/60 mb-4">
          {party.memberCount > 0 && (
            <div className="flex items-center gap-1">
              <Users className="h-3.5 w-3.5 text-blue-400" />
              <span>{party.memberCount} {party.memberCount === 1 ? 'viewer' : 'viewers'}</span>
            </div>
          )}

          {formattedDate && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3.5 w-3.5 text-teal-400" />
              <span>{formattedDate}</span>
            </div>
          )}

          {isOwner && (
            <Badge variant="outline" className="border-blue-500/30 bg-blue-500/10 text-blue-400 text-[10px] px-1.5 py-0">
              Owner
            </Badge>
          )}
        </div>

        {/* Buttons row - now always visible instead of only on hover */}
        <div className="flex gap-2 mt-4">
          {/* For owners, we show Open button */}
          {isOwner ? (
            <Button
              size="sm"
              className="bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white w-full font-medium shadow-md hover:shadow-lg hover:shadow-blue-600/20 transition-all duration-300 flex items-center justify-center px-2 py-5"
              onClick={(e) => {
                e.stopPropagation();
                // For hosts, go to the details page
                onViewDetails(party);
              }}
            >
              <Play className="h-3.5 w-3.5 mr-1" fill="currentColor" />
              Open Party
            </Button>
          ) : (
            <Button
              size="sm"
              className="bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white w-full font-medium shadow-md hover:shadow-lg hover:shadow-blue-600/20 transition-all duration-300 flex items-center justify-center px-2 py-5"
              onClick={(e) => {
                e.stopPropagation();
                onViewDetails(party); // Now we always go to the details page first
              }}
            >
              <Play className="h-3.5 w-3.5 mr-1" fill="currentColor" />
              {joinedPartyIds.has(party.id) ? 'Resume Party' : 'Join Party'}
            </Button>
          )}

          {/* Delete button removed - hosts can delete from the details page */}
        </div>
      </div>
    </div>
  );
}

// ==========================================================================
// Party Details Component
// ==========================================================================
function PartyDetails({
  party,
  onJoin,
  userId: currentUserId,
  onBack
}: {
  party: Party;
  onJoin: (partyId: string, directStart?: boolean) => void;
  userId: string;
  onBack: () => void;
}) {
  const { toast } = useToast();
  const router = useRouter();
  const { deleteParty } = useWatchPartyWithPusher();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Format date function for created dates
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Check if user is the host
  const isHost = party.hostId === currentUserId;

  const handleDeleteParty = async () => {
    if (!isHost) {
      toast({
        title: "Permission Denied",
        description: "Only the host can delete this watch party",
        variant: "destructive"
      });
      return;
    }

    // Open the delete confirmation dialog
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      setIsDeleting(true);

      // First delete the party
      const success = await deleteParty(party.id);

      if (success) {
        // Add a small delay to allow state updates to propagate
        // before redirecting to the dashboard
        await new Promise(resolve => setTimeout(resolve, 300));

        toast({
          title: "Party Deleted",
          description: "The watch party has been deleted successfully"
        });

        // Navigate back to the watch party list with a query parameter to trigger a refresh
        router.push('/watch-party?refresh=true');
      } else {
        throw new Error("Failed to delete party");
      }
    } catch (error) {
      console.error("Error deleting party:", error);
      toast({
        title: "Delete Failed",
        description: "There was an error deleting the watch party",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  return (
    <div className="space-y-8">
      <Card className="bg-vista-dark border-vista-light/10 overflow-hidden shadow-lg rounded-xl">
        <div className="relative h-80 w-full bg-black">
          <div className="absolute inset-0 flex items-center justify-center">
            <Image
              src={party.contentPoster || '/images/content/placeholder.jpg'}
              alt={party.contentTitle}
              fill
              style={{ objectFit: "cover", objectPosition: "center 20%" }}
              unoptimized={party.contentPoster?.includes('image.tmdb.org')}
              className="w-full h-full"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/70 to-transparent" />

          <div className="absolute top-4 right-4">
            <Badge
              variant={party.status === 'active' ? 'default' : 'secondary'}
              className={`${party.status === 'active' ? 'bg-green-600/90 text-white' : 'bg-vista-dark-lighter text-vista-light/70'}
              backdrop-blur-sm shadow-lg px-3 py-1 text-sm`}
            >
              {party.status === 'active' ? (
                <span className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-400 animate-pulse" />
                  Active
                </span>
              ) : 'Scheduled'}
            </Badge>
          </div>

          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="flex justify-between items-end">
              <div>
                <h2 className="text-4xl font-bold text-white mb-3 drop-shadow-md">{party.name}</h2>
                <p className="text-white/80 text-xl font-medium drop-shadow-md">{party.contentTitle}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-8">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-vista-light mb-4">Party Details</h3>

              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-vista-blue" />
                  <div>
                    <span className="text-vista-light">Hosted by:</span>{' '}
                    <span className="text-white font-medium">{party.hostName || 'Unknown'}</span>
                  </div>
                </div>

                {party.memberCount > 0 && (
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-vista-blue" />
                    <div>
                      <span className="text-vista-light">Current viewers:</span>{' '}
                      <span className="text-white font-medium">{party.memberCount}</span>
                    </div>
                  </div>
                )}

                {party.createdAt && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-teal-400" />
                    <div>
                      <span className="text-vista-light">Created:</span>{' '}
                      <span className="text-white font-medium">{formatDate(party.createdAt)}</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="py-4">
                <div className="flex gap-3">
                  <Button
                    className="bg-gradient-to-r from-vista-blue to-blue-600 hover:from-vista-blue/90 hover:to-blue-700 text-white font-medium px-6 flex-1 shadow-md hover:shadow-lg hover:shadow-blue-600/20 transition-all duration-300 flex items-center justify-center gap-2"
                    onClick={() => onJoin(party.id, isHost)}
                  >
                    <Play className="h-4 w-4" fill="currentColor" />
                    {isHost ? 'Start Watching' : 'Join Party'}
                  </Button>

                  <Button
                    variant="outline"
                    className="border-vista-light/20 bg-white/5 text-vista-light hover:bg-vista-light/10 hover:border-vista-light/40 transition-all duration-300 flex items-center gap-2"
                    onClick={onBack}
                  >
                    <ArrowRight className="h-4 w-4 rotate-180" />
                    Back to List
                  </Button>
                </div>
              </div>
            </div>

            <div className="w-full md:w-80">
              <Card className="bg-vista-dark-lighter/50 border-vista-light/10 backdrop-blur-sm shadow-md">
                <CardHeader className="pb-4 border-b border-vista-light/5">
                  <CardTitle className="text-vista-light flex items-center text-lg">
                    <Settings className="h-5 w-5 mr-3 text-vista-blue" />
                    Party Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-6">
                    <div className="group">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 rounded-full bg-vista-dark-lighter flex items-center justify-center mr-3 group-hover:bg-vista-blue/10 transition-colors duration-300">
                          <MessageSquare className="h-4 w-4 text-vista-blue" />
                        </div>
                        <h4 className="font-medium text-vista-light">Chat</h4>
                      </div>
                      <div className="pl-11">
                        <p className="text-sm text-vista-light/70">Chat is enabled for this party</p>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 rounded-full bg-vista-dark-lighter flex items-center justify-center mr-3 group-hover:bg-vista-blue/10 transition-colors duration-300">
                          <Users className="h-4 w-4 text-vista-blue" />
                        </div>
                        <h4 className="font-medium text-vista-light">Visibility</h4>
                      </div>
                      <div className="pl-11">
                        <p className="text-sm text-vista-light/70">
                          {party.private ?
                            "Private - Only users with the party code can join" :
                            "Public - Anyone can join this party"}
                        </p>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 rounded-full bg-vista-dark-lighter flex items-center justify-center mr-3 group-hover:bg-vista-blue/10 transition-colors duration-300">
                          <Play className="h-4 w-4 text-vista-blue" />
                        </div>
                        <h4 className="font-medium text-vista-light">Playback Control</h4>
                      </div>
                      <div className="pl-11">
                        <p className="text-sm text-vista-light/70">Host only</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="flex justify-between items-center border-t border-vista-light/5 pt-6 mt-8">
            <Button
              variant="outline"
              className="border-red-500/30 bg-red-500/5 text-red-500 hover:bg-red-900/10 hover:text-red-400 hover:border-red-500/50 transition-all duration-300 flex items-center gap-2"
              onClick={handleDeleteParty}
              disabled={!isHost || isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Party
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>

      <DeleteConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={confirmDelete}
        isDeleting={isDeleting}
        partyName={party.name}
      />
    </div>
  )
}

// High quality delete confirmation dialog
function DeleteConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  isDeleting,
  partyName
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
  partyName: string;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-vista-dark border-vista-light/10 sm:max-w-md">
        <div className="absolute top-0 right-0 left-0 h-1.5 bg-gradient-to-r from-red-500/50 via-red-500 to-red-600 rounded-t-lg" />

        <DialogHeader className="pt-6">
          <div className="mx-auto bg-red-500/10 p-3 rounded-full mb-4 border border-red-500/20">
            <AlertTriangle className="h-6 w-6 text-red-500" />
          </div>
          <DialogTitle className="text-xl text-center text-vista-light">Delete Watch Party</DialogTitle>
          <DialogDescription className="text-center text-vista-light/70">
            This action cannot be undone. This will permanently delete your watch party and remove all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="p-4 my-2 border border-vista-light/5 rounded-lg bg-vista-dark-lighter/50 text-vista-light">
          <p className="text-sm font-medium mb-1 text-vista-light/70">Party name:</p>
          <p className="font-semibold">{partyName}</p>
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            type="button"
            variant="outline"
            className="border-vista-light/20 bg-white/5 text-vista-light hover:bg-vista-light/10 hover:border-vista-light/30 transition-all duration-300"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-md hover:shadow-lg hover:shadow-red-600/20 transition-all duration-300 font-medium"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-slate-200/30 border-t-white"></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Party
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}