'use client'

import { WatchPartyProvider } from '@/lib/WatchPartyContext'
import dynamic from 'next/dynamic'

/**
 * A wrapper component for the WatchPartyProvider that configures
 * Pusher for real-time communication.
 *
 * This enables proper real-time features for:
 * - Synchronized video playback
 * - Chat messaging
 * - Party membership updates
 * - Video position synchronization
 *
 * The system uses Pusher exclusively for all real-time communication.
 */
export function WatchPartyWrapper({
  children
}: {
  children: React.ReactNode
}) {
  // Dynamically import the WatchPartyProvider to avoid hydration issues
  const DynamicWatchPartyProvider = dynamic(
    () => import('@/lib/WatchPartyContext').then(mod => mod.WatchPartyProvider),
    { ssr: false }
  );

  return (
    <DynamicWatchPartyProvider>
      {children}
    </DynamicWatchPartyProvider>
  )
}