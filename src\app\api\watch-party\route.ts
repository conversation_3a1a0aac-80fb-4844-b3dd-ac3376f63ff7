import { NextResponse } from 'next/server';
import { pusherServer, WATCH_PARTY_EVENTS } from '@/lib/pusher-server';

interface WatchParty {
  id: string;
  contentId: string;
  contentType?: string;
  title: string;
  hostId: string;
  hostName?: string;
  private?: boolean;
  members: Array<{
    id: string;
    name: string;
    isHost: boolean;
    joinedAt: string;
    isReady?: boolean;
    lastActiveAt?: string;
  }>;
  messages?: Array<{
    id: string;
    memberId: string;
    memberName: string;
    content: string;
    timestamp: string;
    type: string;
  }>;
  currentTime: number;
  playing?: boolean;
  isPlaying?: boolean;
  createdAt: string;
  lastActiveAt?: string;
  startedAt?: string;
  currentSeason?: number;
  currentEpisode?: number;
  content?: any;
}

// Simple in-memory storage for watch parties
const watchParties = new Map<string, WatchParty>();

// Persistent party storage across API route invocations using global object
type StoredParty = {
  timestamp: number,
  accessCount: number,
  partyData: WatchParty,
  deleted?: boolean,
  deletedAt?: number
};

// Define clear global type for storing party data
declare global {
  var storedParties: Record<string, StoredParty>;
}

// Initialize global storage if it doesn't exist to make sure we always have a valid object
if (!global.storedParties) {
  global.storedParties = {};
}

// Debug logging configuration
const DEBUG = {
  DETAILED_LOGS: false, // Enable for debugging
  LOG_ALL_REQUESTS: false, // Only log non-standard requests
  LOG_PLAYBACK_UPDATES: false, // Playback updates are too frequent
  LOG_PARTY_ACCESS: false, // Only enable for detailed debugging
  LOG_GET_PARTIES: false, // Only enable for detailed debugging
  LOG_STORAGE_CHANGES: false, // Only log storage changes, not regular status
  STORAGE_CHECK_INTERVAL: 30000, // Check less frequently (30 seconds)
  LOG_THROTTLE: 10000 // Minimum ms between identical log messages
};

// Track last logged messages to prevent repetition
const lastLoggedMessages: Record<string, number> = {};

// Improve the restore parties function to be safer with null checks
function restorePartiesFromGlobalStorage() {
  try {
    // If global storage doesn't exist, initialize it
    if (!global.storedParties) {
      global.storedParties = {};
      logApi('info', 'STORAGE', 'Initialized empty global storage');
      return 0;
    }

    // Count how many parties we restore
    let restoredCount = 0;
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    // Safety check for non-object storage
    if (typeof global.storedParties !== 'object' || global.storedParties === null) {
      logApi('error', 'STORAGE', `Invalid global storage type: ${typeof global.storedParties}`);
      global.storedParties = {};
      return 0;
    }

    // Get an array of entries to avoid modification during iteration issues
    const entries = Object.entries(global.storedParties);

    for (const [partyId, storedParty] of entries) {
      if (!storedParty) continue; // Skip undefined/null entries

      // Skip deleted parties
      if (storedParty.deleted === true) {
        continue;
      }

      // Skip expired parties (older than maxAge)
      if (now - storedParty.timestamp > maxAge) {
        delete global.storedParties[partyId];
        continue;
      }

      // Skip if party is already in the Map
      if (watchParties.has(partyId)) {
        continue;
      }

      // Ensure partyData exists before attempting to restore
      const party = storedParty.partyData;
      if (party && party.id) {
        // Verify it has the minimum required fields
        if (party.contentId && party.members && Array.isArray(party.members)) {
          watchParties.set(partyId, party);
          restoredCount++;
        } else {
          logApi('warn', 'STORAGE', `Invalid party data for ${partyId}, skipping restoration`);
          delete global.storedParties[partyId]; // Remove invalid data
        }
      }
    }

    if (restoredCount > 0) {
      logApi('info', 'STORAGE', `Restored ${restoredCount} parties from global storage`);
    }

    return restoredCount;
  } catch (error) {
    logApi('error', 'STORAGE', `Error restoring parties: ${error instanceof Error ? error.message : String(error)}`);
    // Recover from errors by initializing a clean state
    global.storedParties = {};
    return 0;
  }
}

// Modified trackParty to be more robust with null checks and error handling
function trackParty(partyId: string, action: 'create' | 'access' | 'join' | 'leave' | 'delete', party?: WatchParty) {
  try {
    // Validate inputs to prevent null/undefined issues
    if (!partyId) {
      logApi('error', 'TRACKER', 'Attempted to track party with empty ID');
      return;
    }

    // Ensure global storage exists
    if (!global.storedParties) {
      global.storedParties = {};
    }

    // Special handling for creation to ensure party is properly added to Map
    if (action === 'create' && party) {
      // Add the party to the watchParties Map FIRST
      // This is critical - add to Map before tracking in global storage
      logApi('info', 'TRACKER', `Adding party ${partyId} to watchParties Map`);
      watchParties.set(partyId, party);

      // Verify the party was actually added to the Map
      if (!watchParties.has(partyId)) {
        logApi('error', 'TRACKER', `Failed to add party ${partyId} to Map - critical error!`);
        // Try one more time
        watchParties.set(partyId, party);
        if (!watchParties.has(partyId)) {
          logApi('error', 'TRACKER', `Second attempt to add party ${partyId} to Map failed!`);
          throw new Error('Failed to store party in Map');
        }
      }
    }

    // Special handling for deletion - mark the party as deleted in global storage
    if (action === 'delete') {
      // First verify the party is gone from the Map
      if (watchParties.has(partyId)) {
        logApi('error', 'TRACKER', `Party ${partyId} still exists in Map during delete tracking!`);
        // Force delete it again
        watchParties.delete(partyId);
      }

      if (global.storedParties && partyId in global.storedParties) {
        logApi('info', 'TRACKER', `Marking party ${partyId} as deleted in global storage`);

        // Preserve only essential data to save space
        const partyData = global.storedParties[partyId]?.partyData;
        const minimalPartyData = partyData ? {
          id: partyData.id,
          title: partyData.title,
          contentId: partyData.contentId,
          hostId: partyData.hostId,
          createdAt: partyData.createdAt,
          // Add required fields to satisfy the WatchParty type
          members: [],
          currentTime: 0
        } : {
          // Fallback minimal party data if original is missing
          id: partyId,
          title: 'Deleted Party',
          contentId: '0',
          hostId: '0',
          createdAt: new Date().toISOString(),
          members: [],
          currentTime: 0
        };

        // Instead of deleting, set a flag and timestamp to prevent restoration
        // Store minimal data to save space
        global.storedParties[partyId] = {
          timestamp: Date.now(),
          accessCount: global.storedParties[partyId]?.accessCount || 0,
          deleted: true,
          deletedAt: Date.now(),
          partyData: minimalPartyData
        };

        // Verify the deleted flag was set
        if (global.storedParties[partyId]?.deleted !== true) {
          logApi('error', 'TRACKER', `Failed to set deleted flag for party ${partyId} - forcing direct update`);
          // Force direct update if something went wrong
          global.storedParties[partyId].deleted = true;
          global.storedParties[partyId].deletedAt = Date.now();
        }

        logApi('info', 'TRACKER', `Successfully marked party ${partyId} as deleted with minimal data (flag: ${global.storedParties[partyId]?.deleted === true})`);
      } else {
        logApi('warn', 'TRACKER', `Party ${partyId} not found in global storage during delete tracking`);
      }
      return;
    }

    // Skip any other actions for deleted parties
    if (global.storedParties[partyId]?.deleted === true) {
      logApi('warn', 'TRACKER', `Attempted to ${action} a deleted party ${partyId} - ignoring`);
      return;
    }

    // For all actions, update global storage
    // If party is provided, always ensure it's in storage
    if (party) {
      // Store or update in global storage
      global.storedParties[partyId] = {
        timestamp: Date.now(),
        accessCount: (global.storedParties[partyId]?.accessCount ?? 0) + 1,
        partyData: party
      };

      // For create action, log confirmation
      if (action === 'create') {
        logApi('info', 'TRACKER', `Party ${partyId} added to global storage with key ${partyId}`);
        const allKeys = Object.keys(global.storedParties).join(', ');
        logApi('debug', 'TRACKER', `Global storage now has parties: ${allKeys}`);
      }
    }
    // If there's an existing stored party, update access info
    else if (global.storedParties[partyId]) {
      if (action === 'access' || action === 'join') {
        // Update access count
        global.storedParties[partyId].accessCount = (global.storedParties[partyId].accessCount ?? 0) + 1;
        global.storedParties[partyId].timestamp = Date.now();
      }
    }
  } catch (error) {
    logApi('error', 'TRACKER', `Error tracking party: ${error instanceof Error ? error.message : String(error)}`);
    // For create action, this is critical - rethrow to ensure proper error handling
    if (action === 'create') {
      throw error;
    }
  }
}

// Add storage persistence with interval save
setInterval(() => {
  // First, sync from global storage to ensure we haven't lost any parties
  let recoveredCount = 0;
  let purgedCount = 0;
  const now = Date.now();
  const deletedPartyMaxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  // Process all parties in global storage
  for (const partyId in global.storedParties) {
    // Check if this is a deleted party that should be permanently removed
    if (global.storedParties[partyId]?.deleted === true) {
      // If the party was deleted more than deletedPartyMaxAge ago, permanently remove it
      const deletedAt = global.storedParties[partyId]?.deletedAt || 0;
      if (now - deletedAt > deletedPartyMaxAge) {
        delete global.storedParties[partyId];
        purgedCount++;
        logApi('info', 'CLEANUP', `Permanently removed deleted party ${partyId} after ${Math.round((now - deletedAt) / (60 * 60 * 1000))} hours`);
      }
      continue;
    }

    if (global.storedParties[partyId]?.partyData && !watchParties.has(partyId)) {
      watchParties.set(partyId, global.storedParties[partyId].partyData);
      recoveredCount++;
    }
  }

  // Log purged parties if any
  if (purgedCount > 0) {
    logApi('info', 'CLEANUP', `Permanently removed ${purgedCount} deleted parties from storage`);
  }

  // Only log if changes occurred and logging is enabled
  if (recoveredCount > 0 && DEBUG.LOG_STORAGE_CHANGES) {
    logApi('info', 'STORAGE', `Recovered ${recoveredCount} parties from global storage during interval check`);
  }

  if (watchParties.size > 0) {
    const partiesCount = watchParties.size;
    const partyIds = Array.from(watchParties.keys());

    // Only log detailed tracking info when enabled AND something changed
    if (DEBUG.LOG_STORAGE_CHANGES) {
      logApi('debug', 'STORAGE', `Currently tracking ${partiesCount} parties: ${partyIds.join(', ')}`);
    }
  }

  // Handle missing parties in storage - only run checks, don't log unless needed
  let missingPartiesCount = 0;
  let restoredPartiesCount = 0;

  // Always ensure all Map parties are in global storage
  for (const partyId of Array.from(watchParties.keys())) {
    const party = watchParties.get(partyId);
    if (party && (!global.storedParties[partyId] || !global.storedParties[partyId].partyData)) {
      missingPartiesCount++;
      global.storedParties[partyId] = {
        timestamp: Date.now(),
        accessCount: global.storedParties[partyId]?.accessCount || 1,
        partyData: party
      };
    }
  }

  // Check if any parties in the access log are missing from the Map
  for (const loggedId in global.storedParties) {
    // Skip deleted parties during restoration
    if (global.storedParties[loggedId]?.deleted === true) {
      continue;
    }

    if (!watchParties.has(loggedId)) {
      if (global.storedParties[loggedId]?.partyData) {
        watchParties.set(loggedId, global.storedParties[loggedId].partyData);
        restoredPartiesCount++;
      }
    }
  }

  // Only log if changes occurred and logging is enabled
  if (missingPartiesCount > 0 && DEBUG.LOG_STORAGE_CHANGES) {
    logApi('debug', 'STORAGE', `Added ${missingPartiesCount} missing parties to global storage`);
  }

  // Only log if changes occurred and logging is enabled
  if (restoredPartiesCount > 0 && DEBUG.LOG_STORAGE_CHANGES) {
    logApi('info', 'STORAGE', `Recovered ${restoredPartiesCount} parties from global storage to Map`);
  }
}, DEBUG.STORAGE_CHECK_INTERVAL); // Use configured interval

// Add a request counter to monitor frequency
// Use global scope for persistence between route invocations
// @ts-ignore - using global for persistence
let requestCount = (global as any).__watchPartyRequestCount || 0;
(global as any).__watchPartyRequestCount = requestCount;

// Use normalized event names as keys
const requestCounts: Record<string, number> = (global as any).__watchPartyRequestCounts || {
  'create-watch-party': 0,
  'join-watch-party': 0,
  'leave-watch-party': 0,
  'playback-update': 0,
  'new-message': 0,
  'get-watch-parties': 0
};
(global as any).__watchPartyRequestCounts = requestCounts;

// Improved debug logging with more details
function logApi(level: 'info' | 'debug' | 'error' | 'warn', operation: string, message: string, extraData?: any) {
  // Skip logging based on debug flags
  if (level === 'debug' && !DEBUG.DETAILED_LOGS) return;
  if (operation === 'get-watch-parties' && !DEBUG.LOG_GET_PARTIES) return;
  if (operation === 'playback-update' && !DEBUG.LOG_PLAYBACK_UPDATES) return;
  if (operation === 'STORAGE' && !DEBUG.LOG_STORAGE_CHANGES &&
      (message.includes('Currently tracking') || message.includes('Restoring') || message.includes('Recovered'))) return;

  // Create a key for this message to prevent duplicates
  const msgKey = `${level}:${operation}:${message}`;
  const now = Date.now();

  // Check if we've logged this message recently (throttle common messages)
  if (lastLoggedMessages[msgKey] && (now - lastLoggedMessages[msgKey] < DEBUG.LOG_THROTTLE)) {
    return;
  }

  // Update the last time we logged this message
  lastLoggedMessages[msgKey] = now;

  const timestamp = new Date().toISOString().substring(11, 19); // Extract just the time HH:MM:SS
  const prefix = `[${timestamp}] [WP-API:${operation}]`;

  switch (level) {
    case 'info':
      console.log(`${prefix} ${message}`);
      break;
    case 'debug':
      console.log(`${prefix} ${message}`); // Removed DEBUG: prefix to avoid duplication
      break;
    case 'warn':
      console.warn(`${prefix} WARNING: ${message}`);
      if (extraData) console.warn(`${prefix} DATA:`, extraData);
      break;
    case 'error':
      console.error(`${prefix} ERROR: ${message}`);
      if (extraData) console.error(`${prefix} DATA:`, extraData);
      break;
  }
}

// Use logApi('error', ...) directly for error logging

// Fix - we need to call this function now rather than self-execute
// Call the restoration function when the module loads
restorePartiesFromGlobalStorage();

/**
 * Generate a unique ID for a watch party
 */
function generateId(): string {
  // Generate a 8-character random ID
  return Math.random().toString(36).substring(2, 10);
}

export async function POST(request: Request) {
  // Track total requests
  requestCount++;
  const currentRequestNumber = requestCount;
  // Save the updated count to global
  (global as any).__watchPartyRequestCount = requestCount;

  try {
    // Handle potential JSON parsing errors more gracefully
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      logApi('error', 'PARSE', `JSON parse error (req #${currentRequestNumber})`,
        parseError instanceof Error ? parseError.message : 'Unknown error');
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    const { event, data } = body;

    if (!event) {
      logApi('error', 'UNKNOWN', `Missing event parameter (req #${currentRequestNumber})`);
      return NextResponse.json({ error: 'Missing event parameter' }, { status: 400 });
    }

    // Convert event name to lowercase for case-insensitive matching
    const normalizedEvent = event.toLowerCase();

    // Track request by type using normalized event name
    if (normalizedEvent in requestCounts) {
      requestCounts[normalizedEvent]++;
      // Save updated counts to global
      (global as any).__watchPartyRequestCounts = requestCounts;
    }

    // Log the incoming request with some useful debugging info
    if (DEBUG.LOG_ALL_REQUESTS ||
        (normalizedEvent !== 'playback-update' &&
         normalizedEvent !== 'get-watch-parties')) {
      const count = normalizedEvent in requestCounts ? requestCounts[normalizedEvent] : 'unknown';
      logApi('info', normalizedEvent, `Request #${count} (active parties: ${watchParties.size})`);

      // Only log party details in detailed mode
      if (DEBUG.DETAILED_LOGS && watchParties.size > 0) {
        logApi('debug', normalizedEvent, `Available party IDs: ${Array.from(watchParties.keys()).join(', ')}`);
      }
    }

    // Handle events - using string literals for maximum reliability
    let response;

    switch (normalizedEvent) {
      case 'create-watch-party':
        response = await handleCreateParty(data);
        break;
      case 'join-watch-party':
        response = await handleJoinParty(data);
        break;
      case 'leave-watch-party':
        response = await handleLeaveParty(data);
        break;
      case 'playback-update':
        response = await handlePlaybackUpdate(data);
        break;
      case 'send-message':
      case 'new-message':
        response = await handleNewMessage(data);
        break;
      case 'get-messages':
        response = await handleGetMessages(data);
        break;
      case 'get-watch-parties':
      case 'get-parties': // Allow alternative name for backward compatibility
      case 'get_parties': // Allow underscore format for compatibility
        response = await handleGetParties();
        break;
      case 'update-watch-party':
        response = await handleUpdateWatchParty(data);
        break;
      case 'delete-watch-party':
        response = await handleDeleteWatchParty(data);
        break;
      default:
        logApi('error', 'UNKNOWN', `Unknown event: ${event} (req #${currentRequestNumber})`);
        return NextResponse.json({
          error: 'Unknown event',
          validEvents: [
            'create-watch-party',
            'join-watch-party',
            'leave-watch-party',
            'playback-update',
            'new-message',
            'get-watch-parties'
          ]
        }, { status: 400 });
    }

    return response;
  } catch (error) {
    logApi('error', 'GENERAL', `Error in request #${currentRequestNumber}`,
      error instanceof Error ? error.message : 'Unknown error');
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Improved handleCreateParty with better error handling and validation
async function handleCreateParty(reqBody: any): Promise<NextResponse> {
  try {
    logApi('debug', 'CREATE', `Received create party request with data: ${JSON.stringify(reqBody, null, 2)}`);

    if (!reqBody.contentId || !reqBody.title || !reqBody.hostId || !reqBody.hostName) {
      logApi('error', 'CREATE', 'Missing required fields');
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Generate a unique party ID
    const partyId = generateId();
    logApi('info', 'CREATE', `Creating party ${partyId} for content ${reqBody.contentId}`);

    // Create party data with content properly stored
    const partyData: WatchParty = {
      id: partyId,
      contentId: reqBody.contentId,
      contentType: reqBody.contentType || 'video',
      title: reqBody.title,
      hostId: reqBody.hostId,
      hostName: reqBody.hostName,
      private: reqBody.private === true,
      currentTime: 0,
      playing: false,
      createdAt: new Date().toISOString(),
      lastActiveAt: new Date().toISOString(),
      messages: [],
      members: [{
        id: reqBody.hostId,
        name: reqBody.hostName,
        isHost: true,
        joinedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        isReady: false
      }],
      // Store content information properly to ensure it's available
      content: {
        id: reqBody.contentId,
        title: reqBody.title,
        type: reqBody.contentType || 'video',
        posterPath: reqBody.posterPath || '',
        backdropPath: reqBody.backdropPath || '',
        overview: reqBody.overview || '',
        ...reqBody.content // Include additional fields from content if provided
      }
    };

    // Log the party data for debugging
    logApi('debug', 'CREATE', `Party data: ${JSON.stringify({
      id: partyData.id,
      contentId: partyData.contentId,
      title: partyData.title,
      contentType: partyData.contentType,
      contentTitle: partyData.content?.title
    })}`);

    // First, add the party to our Map
    try {
      watchParties.set(partyId, partyData);

      // Verify the party was actually added
      if (!watchParties.has(partyId)) {
        logApi('error', 'CREATE', `Failed to add party ${partyId} to Map - first attempt failed`);
        // Try one more time directly
        watchParties.set(partyId, partyData);

        // Check again
        if (!watchParties.has(partyId)) {
          logApi('error', 'CREATE', `Failed to add party ${partyId} to Map - second attempt failed`);
          throw new Error('Internal storage error - party creation failed');
        }
      }

      logApi('info', 'CREATE', `Successfully added party ${partyId} to watchParties Map`);
    } catch (error) {
      logApi('error', 'CREATE', `Failed to add party to Map: ${error instanceof Error ? error.message : String(error)}`);
      return NextResponse.json({ error: 'Internal storage error - party creation failed' }, { status: 500 });
    }

    // Now track it using our global storage mechanism
    try {
      trackParty(partyId, 'create', partyData);
      logApi('info', 'CREATE', `Party ${partyId} successfully tracked in global storage`);
    } catch (error) {
      logApi('error', 'CREATE', `Failed to track party in global storage: ${error instanceof Error ? error.message : String(error)}`);
      // The party is already in the Map, so we can still return success
      // but log the error for investigation
    }

    // Log the total number of parties after creation
    logApi('info', 'CREATE', `Total parties after creation: ${watchParties.size}`);

    // Broadcast the new party to all clients (fire-and-forget)
    const broadcastPromise1 = pusherServer.trigger(
      'watch-parties',
      WATCH_PARTY_EVENTS.PARTY_CREATED,
      partyData
    );
    const broadcastPromise2 = pusherServer.trigger(
      'watch-parties',
      WATCH_PARTY_EVENTS.PARTY_UPDATE,
      partyData
    );

    // Log success/errors without awaiting
    Promise.all([broadcastPromise1, broadcastPromise2])
      .then(() => {
          logApi('info', 'PUSHER_BROADCAST', `Successfully triggered Pusher broadcast for new party ${partyId}`);
      })
      .catch((broadcastError) => {
          logApi('error', 'PUSHER_BROADCAST', `Failed Pusher broadcast for new party ${partyId}: ${broadcastError instanceof Error ? broadcastError.message : String(broadcastError)}`);
          // Handle error appropriately, e.g., log for monitoring
      });

    logApi('info', 'CREATE', `Sent create response for ${partyId} without awaiting Pusher.`);

    // Return the party data immediately
    return NextResponse.json({
      success: true,
      party: partyData,
      partyId: partyData.id
    });
  } catch (error) {
    logApi('error', 'CREATE', `Party creation failed: ${error instanceof Error ? error.message : String(error)}`);
    return NextResponse.json({ error: 'Failed to create party' }, { status: 500 });
  }
}

// Handle joining an existing watch party
async function handleJoinParty(data: any) {
  const { partyId, memberId, memberName } = data;

  if (!partyId || !memberId || !memberName) {
    logApi('error', 'JOIN', `Missing required fields (partyId: ${!!partyId}, memberId: ${!!memberId}, memberName: ${!!memberName})`);
    return NextResponse.json(
      { error: 'Missing required fields' },
      { status: 400 }
    );
  }

  try {
    logApi('info', 'JOIN', `Attempting to join party: ${partyId}`);

    // Check for party in persistent tracking first
    const isPartyTracked = partyId in global.storedParties;
    if (isPartyTracked) {
      trackParty(partyId, 'access');
      logApi('debug', 'JOIN', `Party ${partyId} found in tracking system (access count: ${global.storedParties[partyId]?.accessCount})`);
    }

    // List all available parties to debug
    logApi('debug', 'JOIN', `Available parties: ${Array.from(watchParties.keys()).join(', ')}`);

    // Check if party exists
    let party = watchParties.get(partyId);

    // If party not found in Map but is in our tracking system, this could be a storage issue
    // Attempt recovery from any previous errors
    if (!party && isPartyTracked) {
      logApi('error', 'JOIN', `Party ${partyId} is in tracking system but not in storage Map - potential storage corruption!`);

      // Manual recovery attempt - check if we received any previous party data in GET_PARTIES calls
      // This is a last-ditch effort to recover
      try {
        logApi('info', 'JOIN', `Attempting recovery for party ${partyId}`);

        // Send a request to get all parties to trigger a refresh
        const partyResponse = await fetch('/api/watch-party', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event: 'get-watch-parties',
          })
        });

        if (partyResponse.ok) {
          const data = await partyResponse.json();
          const recoveredParty = data.parties?.find((p: any) => p.id === partyId);

          if (recoveredParty) {
            logApi('info', 'JOIN', `Successfully recovered party ${partyId} from API`);
            party = recoveredParty as WatchParty;
            watchParties.set(partyId, party);
          }
        }
      } catch (recoveryError) {
        logApi('error', 'JOIN', `Failed to recover party ${partyId}`, recoveryError);
      }
    }

    if (!party) {
      logApi('error', 'JOIN', `Party not found with ID ${partyId}`);
      return NextResponse.json(
        { error: `Party not found with ID: ${partyId}` },
        { status: 404 }
      );
    }

    // Track that we successfully found the party
    trackParty(partyId, 'join');

    logApi('info', 'JOIN', `Found party ${partyId} with ${party.members.length} members`);

    // Check if member already exists
    const existingMemberIndex = party.members.findIndex((m: any) => m.id === memberId);

    if (existingMemberIndex >= 0) {
      // Update existing member
      logApi('info', 'JOIN', `Updating existing member ${memberId} in party ${partyId}`);
      party.members[existingMemberIndex].name = memberName;
    } else {
      // Add new member
      logApi('info', 'JOIN', `Adding new member ${memberId} to party ${partyId}`);
      party.members.push({
        id: memberId,
        name: memberName,
        isHost: false,
        joinedAt: new Date().toISOString(),
        isReady: false
      });
    }

    // Update party in memory
    watchParties.set(partyId, party);

    // Verify the party is still in memory after the update
    if (!watchParties.has(partyId)) {
      logApi('error', 'JOIN', `Critical error: Party ${partyId} disappeared from storage after member update`);
      watchParties.set(partyId, party); // Re-add it
    }

    try {
      // Broadcast member update
      await pusherServer.trigger(
        `watch-party-${partyId}`,
        WATCH_PARTY_EVENTS.MEMBER_UPDATE,
        party.members
      );

      // Broadcast party update
      await pusherServer.trigger(
        `watch-party-${partyId}`,
        WATCH_PARTY_EVENTS.PARTY_UPDATE,
        party
      );
    } catch (pusherError) {
      logApi('error', 'JOIN', `Failed to broadcast updates for party ${partyId}`, pusherError);
      // Continue anyway since the member was added to the party in storage
    }

    logApi('info', 'JOIN', `Successfully joined party ${partyId}, now has ${party.members.length} members`);

    // Create a serializable copy of the party for debugging
    const partyCopy = DEBUG.DETAILED_LOGS ? JSON.parse(JSON.stringify(party)) : undefined;

    return NextResponse.json({
      success: true,
      party,
      _debug: partyCopy
    });
  } catch (error) {
    logApi('error', 'JOIN', `Error joining party ${partyId}:`, error);
    return NextResponse.json(
      { error: `Failed to join party: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// Handle leaving a watch party
async function handleLeaveParty(data: any) {
  const { partyId, memberId } = data;

  if (!partyId || !memberId) {
    return NextResponse.json(
      { error: 'Missing required fields' },
      { status: 400 }
    );
  }

  try {
    const party = watchParties.get(partyId);

    if (!party) {
      return NextResponse.json(
        { error: 'Party not found' },
        { status: 404 }
      );
    }

    // Remove member
    const memberIndex = party.members.findIndex((m: any) => m.id === memberId);

    if (memberIndex >= 0) {
      const isHost = party.members[memberIndex].isHost;
      party.members.splice(memberIndex, 1);

      // If host left and there are other members, assign a new host
      if (isHost && party.members.length > 0) {
        party.members[0].isHost = true;
        party.hostId = party.members[0].id;
      }

      // If no members left, remove the party
      if (party.members.length === 0) {
        watchParties.delete(partyId);
        return NextResponse.json({ success: true });
      }

      // Update party in memory
      watchParties.set(partyId, party);

      // Broadcast member update
      await pusherServer.trigger(
        `watch-party-${partyId}`,
        WATCH_PARTY_EVENTS.MEMBER_UPDATE,
        party.members
      );

      // Broadcast party update
      await pusherServer.trigger(
        `watch-party-${partyId}`,
        WATCH_PARTY_EVENTS.PARTY_UPDATE,
        party
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error leaving party:', error);
    return NextResponse.json(
      { error: 'Failed to leave party' },
      { status: 500 }
    );
  }
}

// Handle playback updates
async function handlePlaybackUpdate(data: any) {
  const { partyId, currentTime, isPlaying } = data;

  if (!partyId || currentTime === undefined || isPlaying === undefined) {
    return NextResponse.json(
      { error: 'Missing required fields' },
      { status: 400 }
    );
  }

  try {
    const party = watchParties.get(partyId);

    if (!party) {
      return NextResponse.json(
        { error: 'Party not found' },
        { status: 404 }
      );
    }

    // Update playback state
    party.currentTime = currentTime;
    party.isPlaying = isPlaying;

    // Update party in memory
    watchParties.set(partyId, party);

    // Use the dedicated playback API for better real-time performance
    try {
      const playbackResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ''}/api/watch-party/playback`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partyId,
          currentTime,
          isPlaying,
          isHost: true
        })
      });

      if (!playbackResponse.ok) {
        console.error('Error from playback API:', await playbackResponse.text());
      }
    } catch (error) {
      console.error('Error calling playback API:', error);

      // Fallback to direct Pusher broadcast
      await pusherServer.trigger(
        `watch-party-${partyId}`,
        WATCH_PARTY_EVENTS.PLAYBACK_UPDATE,
        {
          currentTime,
          isPlaying
        }
      );

      // Also try SSE
      try {
        const { broadcastToParty } = await import('./sse/route');
        await broadcastToParty(partyId, { currentTime, isPlaying }, 'playback-update');
      } catch (sseError) {
        console.error('Error broadcasting via SSE:', sseError);
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating playback:', error);
    return NextResponse.json(
      { error: 'Failed to update playback' },
      { status: 500 }
    );
  }
}

// Handle new chat messages
async function handleNewMessage(data: any) {
  const { partyId, message } = data;

  if (!partyId || !message) {
    return NextResponse.json(
      { error: 'Missing required fields' },
      { status: 400 }
    );
  }

  try {
    const party = watchParties.get(partyId);

    if (!party) {
      return NextResponse.json(
        { error: 'Party not found' },
        { status: 404 }
      );
    }

    // Initialize messages array if it doesn't exist
    if (!party.messages) {
      party.messages = [];
    }

    party.messages.push(message);

    // Limit to 100 messages
    if (party.messages.length > 100) {
      party.messages.shift();
    }

    // Update party in memory
    watchParties.set(partyId, party);

    // Broadcast new message via Pusher
    await pusherServer.trigger(
      `watch-party-${partyId}`,
      WATCH_PARTY_EVENTS.NEW_MESSAGE,
      message
    );

    // Also broadcast via SSE if available
    try {
      // Dynamic import to avoid circular dependency
      const { broadcastToParty } = await import('./sse/route');
      if (typeof broadcastToParty === 'function') {
        await broadcastToParty(partyId, message);
      }
    } catch (sseError) {
      // SSE is optional, so we don't fail if it's not available
      console.log('SSE broadcast not available:', sseError);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}

// Get chat messages for a specific party
async function handleGetMessages(data: any) {
  const { partyId } = data;

  if (!partyId) {
    return NextResponse.json(
      { error: 'Missing party ID' },
      { status: 400 }
    );
  }

  try {
    const party = watchParties.get(partyId);

    if (!party) {
      return NextResponse.json(
        { error: 'Party not found' },
        { status: 404 }
      );
    }

    // Track party access
    trackParty(partyId, 'access', party);

    // Return messages from the party
    return NextResponse.json({
      success: true,
      messages: party.messages ?? [],
      partyId
    });
  } catch (error) {
    console.error('Error getting messages:', error);
    return NextResponse.json(
      { error: 'Failed to get messages' },
      { status: 500 }
    );
  }
}

// Get all available watch parties
async function handleGetParties() {
  // Skip redundant get-parties logging in most cases
  const SKIP_LOGGING = true;

  // Increment counters once but don't log unless necessary
  requestCount++;
  requestCounts['get-watch-parties'] = (requestCounts['get-watch-parties'] || 0) + 1;
  (global as any).__watchPartyRequestCount = requestCount;
  (global as any).__watchPartyRequestCounts = requestCounts;

  // Only log once per request with essential info
  if (DEBUG.LOG_GET_PARTIES && !SKIP_LOGGING) {
    logApi('info', 'get-watch-parties', `Active parties: ${watchParties.size}`);
  }

  try {
    // Clone parties for response to avoid mutations during serialization
    const parties = Array.from(watchParties.values()).map(party => {
      // Increment access count
      trackParty(party.id, 'access');

      // Create a clean copy for the response
      return { ...party };
    });

    // Only log if we have detailed logging enabled
    if (DEBUG.LOG_GET_PARTIES && DEBUG.DETAILED_LOGS && !SKIP_LOGGING) {
      const partyInfo = parties.length > 0
        ? `IDs: ${parties.map(p => p.id).join(', ')}`
        : 'none';
      logApi('debug', 'GET_PARTIES', `Returning ${parties.length} parties. ${partyInfo}`);
    }

    // Skip most duplicate log checks
    if (DEBUG.DETAILED_LOGS && parties.length > 0 && !SKIP_LOGGING) {
      // Check for duplicates only if there are parties
      const partyIds = parties.map(p => p.id);
      const uniquePartyIds = new Set(partyIds);
      if (uniquePartyIds.size !== partyIds.length) {
        logApi('warn', 'GET_PARTIES', `Duplicate party IDs detected in response`);
      }
    }

    return NextResponse.json({ parties });
  } catch (error) {
    logApi('error', 'GET_PARTIES', `Error retrieving parties`, error);
    return NextResponse.json(
      { error: 'Failed to retrieve parties' },
      { status: 500 }
    );
  }
}

// Handle updating an existing watch party
async function handleUpdateWatchParty(data: any) {
  const { partyId, updates } = data || {};

  if (!partyId) {
    logApi('error', 'UPDATE', 'Missing party ID');
    return NextResponse.json({ error: 'Missing party ID' }, { status: 400 });
  }

  logApi('info', 'UPDATE', `Attempting to update party: ${partyId}`);

  // Check if party exists
  if (!watchParties.has(partyId)) {
    logApi('error', 'UPDATE', `Party ${partyId} not found`);
    return NextResponse.json({ error: 'Party not found' }, { status: 404 });
  }

  // Get the party
  const party = watchParties.get(partyId);
  if (!party) {
    logApi('error', 'UPDATE', `Failed to retrieve party ${partyId}`);
    return NextResponse.json({ error: 'Failed to retrieve party' }, { status: 500 });
  }

  // Apply updates
  logApi('info', 'UPDATE', `Updating party ${partyId}`);
  if (DEBUG.DETAILED_LOGS) {
    logApi('debug', 'UPDATE', `Update details:`, updates);
  }

  const updatedParty = {
    ...party,
    ...updates,
    // If we're marking it as started, ensure we have a timestamp
    ...(updates.startedAt && !party.startedAt ? { startedAt: updates.startedAt } : {}),
  };

  // Store the updated party
  watchParties.set(partyId, updatedParty);

  // Notify clients via WebSocket if we have Pusher
  if (pusherServer) {
    // Always send detailed update to party-specific channel
    await pusherServer.trigger(`watch-party-${partyId}`, WATCH_PARTY_EVENTS.PARTY_UPDATE, updatedParty);

    // Only send minimal update to global channel and only for significant changes
    const memberCountChanged = updates.members !== undefined;
    const partyStatusChanged = updates.startedAt !== undefined || updates.endedAt !== undefined;
    const titleChanged = updates.title !== undefined;

    if (memberCountChanged || partyStatusChanged || titleChanged) {
      // Send minimal data to global channel
      await pusherServer.trigger('watch-parties', WATCH_PARTY_EVENTS.PARTY_UPDATE, {
        id: partyId,
        title: updatedParty.title,
        memberCount: updatedParty.members.length,
        isActive: !!updatedParty.startedAt && !updatedParty.endedAt,
        lastActiveAt: updatedParty.lastActiveAt
      });
    }
  }

  // Return success response
  return NextResponse.json({
    success: true,
    message: `Party ${partyId} updated successfully`,
    party: updatedParty
  });
}

// Handle deleting a watch party
async function handleDeleteWatchParty(data: any) {
  const { partyId } = data || {};

  if (!partyId) {
    logApi('error', 'DELETE', 'Missing party ID');
    return NextResponse.json({ error: 'Missing party ID' }, { status: 400 });
  }

  logApi('info', 'DELETE', `Attempting to delete party: ${partyId}`);

  try {
    // Check if party exists
    if (!watchParties.has(partyId)) {
      logApi('error', 'DELETE', `Party ${partyId} not found`);
      return NextResponse.json({ error: 'Party not found' }, { status: 404 });
    }

    // Get the party before deletion (for notification)
    const party = watchParties.get(partyId);

    // Log party details for debugging
    logApi('info', 'DELETE', `Party ${partyId} found with ${party?.members?.length || 0} members. Deleting...`);

    // IMPORTANT: Ensure the party is properly removed from the Map
    const wasRemoved = watchParties.delete(partyId);

    if (!wasRemoved) {
      logApi('error', 'DELETE', `Failed to remove party ${partyId} from Map - delete() returned false`);
      return NextResponse.json(
        { error: 'Failed to delete party' },
        { status: 500 }
      );
    }

    // Double check that the party is actually gone from the Map
    if (watchParties.has(partyId)) {
      logApi('error', 'DELETE', `Party ${partyId} still exists after deletion attempt!`);

      // Force delete again
      watchParties.delete(partyId);

      // Check one more time
      if (watchParties.has(partyId)) {
        logApi('error', 'DELETE', `Party ${partyId} STILL exists after second deletion attempt!`);
        return NextResponse.json(
          { error: 'Failed to delete party after multiple attempts' },
          { status: 500 }
        );
      } else {
        logApi('warn', 'DELETE', `Party ${partyId} removed on second attempt`);
      }
    }

    // Track the deletion in global storage with enhanced logging
    logApi('info', 'DELETE', `Marking party ${partyId} as deleted in global storage`);
    trackParty(partyId, 'delete');

    // Verify deletion status in global storage
    if (global.storedParties[partyId]?.deleted === true) {
      logApi('info', 'DELETE', `Successfully marked party ${partyId} as deleted in global storage`);
    } else {
      logApi('error', 'DELETE', `Failed to mark party ${partyId} as deleted in global storage!`);

      // Force set deleted flag directly with minimal data
      if (global.storedParties[partyId]) {
        // Preserve only essential data to save space
        const partyData = global.storedParties[partyId]?.partyData;
        const minimalPartyData = partyData ? {
          id: partyData.id,
          title: partyData.title,
          contentId: partyData.contentId,
          hostId: partyData.hostId,
          createdAt: partyData.createdAt,
          members: [],
          currentTime: 0
        } : {
          id: partyId,
          title: 'Deleted Party',
          contentId: '0',
          hostId: '0',
          createdAt: new Date().toISOString(),
          members: [],
          currentTime: 0
        };

        // Update with minimal data
        global.storedParties[partyId] = {
          timestamp: Date.now(),
          accessCount: global.storedParties[partyId]?.accessCount || 0,
          deleted: true,
          deletedAt: Date.now(),
          partyData: minimalPartyData
        };

        logApi('info', 'DELETE', `Manually set deleted flag for party ${partyId} with minimal data`);
      }
    }

    // Get all current party keys for verification
    const partyKeys = Array.from(watchParties.keys());
    const wasSuccessful = !partyKeys.includes(partyId);

    logApi('info', 'DELETE', `Deletion result: success=${wasSuccessful}. Current parties: ${partyKeys.join(', ') || 'none'}`);

    // Notify clients via WebSocket with enhanced error handling
    try {
      if (pusherServer) {
        // First notify the specific party channel
        await pusherServer.trigger(
          `watch-party-${partyId}`,
          WATCH_PARTY_EVENTS.PARTY_DELETED,
          { partyId }
        );

        logApi('info', 'DELETE', `Notified party channel watch-party-${partyId} of deletion`);

        // Then notify the main channel so everyone knows
        await pusherServer.trigger(
          'watch-parties',
          WATCH_PARTY_EVENTS.PARTY_DELETED,
          { partyId }
        );

        logApi('info', 'DELETE', `Notified main channel watch-parties of deletion`);
      } else {
        logApi('warn', 'DELETE', 'Pusher server not available for notifications');
      }
    } catch (notifyError) {
      // Log the error but don't fail the deletion
      logApi('error', 'DELETE', `Error sending deletion notifications: ${notifyError}`);
      console.error('Error sending deletion notifications:', notifyError);
    }

    logApi('info', 'DELETE', `Successfully deleted party: ${partyId}`);

    // Return success response with verification info
    return NextResponse.json({
      success: true,
      message: `Party ${partyId} deleted successfully`,
      partyStillExists: watchParties.has(partyId),
      remainingParties: partyKeys.length
    });
  } catch (error) {
    logApi('error', 'DELETE', `Error deleting party: ${partyId}`, error);
    return NextResponse.json(
      { error: 'Failed to delete party' },
      { status: 500 }
    );
  }
}