import { NextRequest, NextResponse } from 'next/server';
import { pusherServer, WATCH_PARTY_EVENTS } from '@/lib/pusher-server';

// In-memory storage for watch party playback states
const playbackStates = new Map<string, {
  currentTime: number;
  isPlaying: boolean;
  lastUpdated: number;
  updateCount: number;
}>();

// Handle GET requests to retrieve current playback state
export async function GET(request: NextRequest) {
  const partyId = request.nextUrl.searchParams.get('partyId');

  if (!partyId) {
    return NextResponse.json(
      { error: 'Missing partyId parameter' },
      { status: 400 }
    );
  }

  // Get current playback state
  const playbackState = playbackStates.get(partyId);

  if (!playbackState) {
    // Instead of returning a 404, return a default state with success=true
    // This prevents errors in the client and allows for a smoother experience
    console.log(`[Playback API] No playback state found for party ${partyId}, returning default state`);
    return NextResponse.json({
      success: true,
      playback: {
        currentTime: 0,
        isPlaying: false,
        lastUpdated: Date.now(),
        updateCount: 0
      },
      isDefault: true // Flag to indicate this is a default state
    });
  }

  return NextResponse.json({
    success: true,
    playback: playbackState
  });
}

// Handle POST requests to update playback state
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { partyId, currentTime, isPlaying, isHost } = body;

    console.log(`[Playback API] Received update request:`, { partyId, currentTime, isPlaying, isHost });

    if (!partyId) {
      console.log('[Playback API] Missing partyId parameter');
      return NextResponse.json(
        { error: 'Missing partyId parameter' },
        { status: 400 }
      );
    }

    // Only hosts can update playback state
    if (!isHost) {
      console.log('[Playback API] Non-host tried to update playback state');
      return NextResponse.json(
        { error: 'Only hosts can update playback state' },
        { status: 403 }
      );
    }

    // Update or create playback state
    const existingState = playbackStates.get(partyId);
    const updateCount = existingState ? existingState.updateCount + 1 : 1;

    // Check if this is a significant update to avoid unnecessary broadcasts
    const isPlayStateChange = existingState && existingState.isPlaying !== isPlaying;
    const timeDiff = existingState ? Math.abs((existingState.currentTime || 0) - (currentTime || 0)) : 0;
    const isSignificantTimeChange = !existingState || timeDiff > 3; // 3 second threshold

    // Only proceed with update if it's significant
    if (isPlayStateChange || isSignificantTimeChange) {
      const newState = {
        currentTime: currentTime !== undefined ? currentTime : (existingState?.currentTime || 0),
        isPlaying: isPlaying !== undefined ? isPlaying : (existingState?.isPlaying || false),
        lastUpdated: Date.now(),
        updateCount
      };

      playbackStates.set(partyId, newState);

      // Check Pusher environment variables
      if (!process.env.PUSHER_APP_ID || !process.env.PUSHER_KEY || 
          !process.env.PUSHER_SECRET || !process.env.PUSHER_CLUSTER) {
        console.error('[Playback API] Missing Pusher environment variables!', {
          appId: process.env.PUSHER_APP_ID ? 'Set' : 'Missing',
          key: process.env.PUSHER_KEY ? 'Set' : 'Missing',
          secret: process.env.PUSHER_SECRET ? 'Set' : 'Missing',
          cluster: process.env.PUSHER_CLUSTER ? 'Set' : 'Missing',
        });
        return NextResponse.json(
          { error: 'Server configuration error - missing Pusher credentials' },
          { status: 500 }
        );
      }

      // Broadcast via Pusher for reliability
      try {
        console.log(`[Playback API] Attempting to trigger Pusher event for channel: watch-party-${partyId}`);
        await pusherServer.trigger(
          `watch-party-${partyId}`,
          WATCH_PARTY_EVENTS.PLAYBACK_UPDATE,
          {
            currentTime: newState.currentTime,
            isPlaying: newState.isPlaying,
            updateCount
          }
        );
        console.log(`[Playback API] Pusher event triggered successfully`);
      } catch (pusherError: unknown) {
        console.error('[Playback API] Pusher error:', pusherError);
        return NextResponse.json(
          { 
            error: 'Failed to broadcast playback update via Pusher', 
            details: pusherError instanceof Error ? pusherError.message : 'Unknown error' 
          },
          { status: 500 }
        );
      }

      console.log(`[Playback API] ${isPlayStateChange ? 'Play state changed' : 'Significant time update'} for party ${partyId}`);

      // Also broadcast via SSE for real-time updates, but only for play state changes
      // This reduces redundant communication since Pusher is already handling the update
      if (isPlayStateChange) {
        try {
          console.log(`[Playback API] Attempting to broadcast via SSE`);
          const { broadcastToParty } = await import('../sse/route');
          await broadcastToParty(
            partyId,
            {
              currentTime: newState.currentTime,
              isPlaying: newState.isPlaying,
              updateCount
            },
            'playback-update'
          );
          console.log(`[Playback API] Broadcast play state change to party ${partyId} via SSE`);
        } catch (sseError) {
          console.error('[Playback API] Error broadcasting via SSE:', sseError);
          // Continue anyway since we already broadcast via Pusher
        }
      }

      // Return success response
      return NextResponse.json({
        success: true,
        playback: newState
      });
    } else {
      // Still update the state but don't broadcast
      playbackStates.set(partyId, {
        ...existingState,
        currentTime: currentTime !== undefined ? currentTime : existingState.currentTime,
        lastUpdated: Date.now(),
        updateCount
      });

      console.log(`[Playback API] Skipped non-significant update for party ${partyId} (diff: ${timeDiff.toFixed(2)}s)`);

      // Return early with success but note that we skipped broadcasting
      return NextResponse.json({
        success: true,
        skippedBroadcast: true,
        reason: 'Non-significant update',
        playback: playbackStates.get(partyId)
      });
    }
  } catch (error: unknown) {
    console.error('[Playback API] Error updating playback state:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update playback state', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
