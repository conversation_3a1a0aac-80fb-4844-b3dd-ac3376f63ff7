import { NextRequest, NextResponse } from 'next/server';
import { VIDSRC_DOMAINS } from '@/lib/vidsrc-api';

// This API route checks the availability of streaming providers by probing their domains
// It helps determine which VidSrc domains are currently available and working

export async function GET(request: NextRequest) {
  const domains = VIDSRC_DOMAINS;
  const availableDomains: string[] = [];
  
  console.log('Checking availability of VidSrc domains...');
  
  // Check each domain concurrently
  const checkPromises = domains.map(async (domain) => {
    try {
      // Use HTTP instead of HTTPS to avoid SSL issues
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 1500); // 1.5s timeout
      
      const response = await fetch(`http://${domain}`, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // If we get any response, consider the domain available
      console.log(`Domain ${domain} is available`);
      return domain;
    } catch (error) {
      console.log(`Error checking domain ${domain}:`, error);
      return null;
    }
  });
  
  // Wait for all checks to complete with a timeout
  const results = await Promise.all(checkPromises);
  
  // Filter out null results (failed checks)
  results.forEach(domain => {
    if (domain) availableDomains.push(domain);
  });
  
  // If no domains are available, use the defaults
  if (availableDomains.length === 0) {
    console.log('No domains available, using defaults');
    availableDomains.push(...domains.slice(0, 3));
  }
  
  return NextResponse.json({
    availableDomains: availableDomains
  });
} 