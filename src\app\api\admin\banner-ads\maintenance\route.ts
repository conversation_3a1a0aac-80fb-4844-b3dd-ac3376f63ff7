import { NextRequest, NextResponse } from 'next/server';
import { verifyAdmin } from '@/lib/admin-auth';
import { BannerScheduler } from '@/lib/banner-scheduler';

/**
 * POST /api/admin/banner-ads/maintenance
 * Run banner maintenance tasks (expire old banners, activate scheduled ones)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    // Run maintenance tasks
    const results = await BannerScheduler.runMaintenance();

    return NextResponse.json({
      success: true,
      message: 'Banner maintenance completed successfully',
      results
    });

  } catch (error) {
    console.error('Error running banner maintenance:', error);
    return NextResponse.json(
      { error: 'Failed to run banner maintenance' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/banner-ads/maintenance
 * Get banner analytics and maintenance status
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminSession = await verifyAdmin(request);
    if (!adminSession) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }

    // Get analytics and top performing banners
    const [analytics, topBanners] = await Promise.all([
      BannerScheduler.getBannerAnalytics(),
      BannerScheduler.getTopPerformingBanners(10)
    ]);

    return NextResponse.json({
      analytics,
      topBanners,
      timestamp: new Date()
    });

  } catch (error) {
    console.error('Error fetching banner maintenance data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch banner maintenance data' },
      { status: 500 }
    );
  }
}
