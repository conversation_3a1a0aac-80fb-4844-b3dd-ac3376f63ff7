'use client';

import React, { createContext, useContext, useState, ReactNode, useCallback, useMemo } from 'react';
import { useToastHelpers } from '@/lib/ToastContext';
import { IContent } from '@/data/content';
import { useErrorHelpers } from './ErrorContext';
import { v4 as uuidv4 } from 'uuid';
import { useWatchPartyWithPusher } from '@/hooks/useWatchPartyWithPusher';
import { IContent as PusherContent } from '@/hooks/useWatchPartyWithPusher';

// Types
export interface WatchPartyMember {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
  joinedAt: string;
  isReady: boolean;
}

export interface ChatMessage {
  id: string;
  memberId: string;
  memberName: string;
  content: string;
  timestamp: string;
  type: 'chat' | 'system' | 'reaction';
}

export interface WatchPartyState {
  id: string;
  contentId: string;
  content?: IContent;
  hostId: string;
  members: WatchPartyMember[];
  messages: ChatMessage[];
  currentTime: number;
  isPlaying: boolean;
  createdAt: string;
  startedAt?: string;
  currentSeason?: number;
  currentEpisode?: number;
}

// Context type definition
interface WatchPartyContextType {
  currentParty: WatchPartyState | null;
  isHost: boolean;
  createParty: (content: IContent) => Promise<string>;
  joinParty: (partyId: string, memberName: string) => Promise<void>;
  leaveParty: () => void;
  updatePlayback: (currentTime: number, isPlaying: boolean) => void;
  sendMessage: (message: string) => void;
  sendReaction: (reaction: string) => void;
  connectionState: 'connected' | 'connecting' | 'disconnected' | 'failed';
  availableParties: WatchPartyState[];
  fetchAvailableParties: () => void;
  isLoading: boolean;
  userId: string;
}

// Create the context
const WatchPartyContext = createContext<WatchPartyContextType | undefined>(undefined);

// Custom hook to use the context
export function useWatchParty() {
  const context = useContext(WatchPartyContext);
  if (context === undefined) {
    throw new Error('useWatchParty must be used within a WatchPartyProvider');
  }
  return context;
}

// Type adapter function to convert between content types
function adaptContentForPusher(content: IContent): PusherContent {
  return {
    id: String(content.id), // Convert id to string
    title: content.title,
    posterPath: content.posterPath,
    backdropPath: content.backdropPath || '',
    type: content.type,
    overview: content.overview || '',
    year: content.year,
    rating: content.rating || 0,
    genres: content.genres || []
  };
}

// Props for the provider
interface WatchPartyProviderProps {
  children: React.ReactNode;
}

// Provider component
export function WatchPartyProvider({ children }: WatchPartyProviderProps) {
  const { showError } = useErrorHelpers();
  
  // Use the Pusher-based hook for all real-time functionality
  const {
    currentParty,
    availableParties,
    isLoading,
    error,
    sendMessage,
    joinParty: pusherJoinParty,
    leaveParty: pusherLeaveParty,
    updatePlayback: pusherUpdatePlayback,
    userId,
    userName,
    setUserName,
    isHost,
    createParty: pusherCreateParty,
    fetchAvailableParties: pusherFetchAvailableParties
  } = useWatchPartyWithPusher();

  // Log that we're using Pusher
  console.log("Watch Party is using Pusher for real-time communication");

  // Map the pusher hook functions to our context interface
  const createParty = useCallback(async (content: IContent): Promise<string> => {
    try {
      // Convert the content to the format expected by Pusher
      const pusherContent = adaptContentForPusher(content);
      
      // Call the pusher create party function
      return await pusherCreateParty(pusherContent);
    } catch (error) {
      console.error('Error creating party:', error);
      showError('Failed to create party', 'There was an error creating your watch party.');
      throw error;
    }
  }, [pusherCreateParty, showError]);

  const joinParty = useCallback(async (partyId: string, memberName: string): Promise<void> => {
    try {
      // Update the user name if provided
      if (memberName && memberName !== userName) {
        setUserName(memberName);
      }
      
      // Join the party
      await pusherJoinParty(partyId);
    } catch (error) {
      console.error('Error joining party:', error);
      showError('Failed to join party', 'There was an error joining the watch party.');
      throw error;
    }
  }, [pusherJoinParty, userName, setUserName, showError]);

  // Define connection state - with Pusher we always consider ourselves connected
  // unless there's an error
  const connectionState = error ? 'failed' as const : isLoading ? 'connecting' as const : 'connected' as const;

  // Create the context value
  const contextValue = useMemo(() => ({
    currentParty,
    isHost,
    createParty,
    joinParty,
    leaveParty: pusherLeaveParty,
    updatePlayback: pusherUpdatePlayback,
    sendMessage,
    sendReaction: (reaction: string) => sendMessage(`REACTION:${reaction}`),
    connectionState,
    availableParties,
    fetchAvailableParties: pusherFetchAvailableParties,
    isLoading,
    userId
  }), [
    currentParty,
    isHost,
    createParty,
    joinParty,
    pusherLeaveParty,
    pusherUpdatePlayback,
    sendMessage,
    connectionState,
    availableParties,
    pusherFetchAvailableParties,
    isLoading,
    userId
  ]);

  return (
    <WatchPartyContext.Provider value={contextValue}>
      {children}
    </WatchPartyContext.Provider>
  );
}