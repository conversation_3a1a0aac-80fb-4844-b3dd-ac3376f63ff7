import mongoose, { Schema, Document } from 'mongoose';

export interface IBannerAd extends Document {
  title: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  isActive: boolean;
  startDate?: Date;
  endDate?: Date;
  duration?: number; // Duration in days, null for infinite
  priority: number; // Higher number = higher priority
  styling: {
    backgroundColor?: string;
    textColor?: string;
    titleSize?: string;
    descriptionSize?: string;
    borderRadius?: string;
    padding?: string;
    animation?: 'none' | 'fadeIn' | 'slideIn' | 'bounce' | 'pulse';
    animationDuration?: string;
    positions?: ('top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections')[];
  };
  analytics: {
    views: number;
    clicks: number;
    impressions: number;
  };
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Define the BannerAd schema
const BannerAdSchema = new Schema<IBannerAd>({
  title: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 100
  },
  description: { 
    type: String,
    trim: true,
    maxlength: 200
  },
  imageUrl: {
    type: String,
    required: true,
    validate: {
      validator: function(v: string) {
        // Allow Cloudinary URLs and standard image URLs
        return /^https?:\/\/.+/.test(v) && (
          /\.(jpg|jpeg|png|gif|webp)$/i.test(v) || // Standard image URLs
          /cloudinary\.com/.test(v) || // Cloudinary URLs
          /res\.cloudinary\.com/.test(v) // Cloudinary resource URLs
        );
      },
      message: 'Image URL must be a valid image URL or Cloudinary URL'
    }
  },
  linkUrl: {
    type: String,
    validate: {
      validator: function(v: string) {
        if (!v) return true; // Optional field
        // Allow URLs with or without protocol
        return /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(v);
      },
      message: 'Link URL must be a valid URL (e.g., https://example.com or www.example.com)'
    }
  },
  isActive: { 
    type: Boolean, 
    default: true 
  },
  startDate: { 
    type: Date,
    default: Date.now
  },
  endDate: { 
    type: Date
  },
  duration: { 
    type: Number, // Duration in days
    min: 1,
    validate: {
      validator: function(v: number) {
        if (v === null || v === undefined) return true; // Allow null for infinite duration
        return v > 0;
      },
      message: 'Duration must be a positive number'
    }
  },
  priority: { 
    type: Number, 
    default: 1,
    min: 1,
    max: 10
  },
  styling: {
    backgroundColor: { 
      type: String, 
      default: '#1a1a1a',
      validate: {
        validator: function(v: string) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Background color must be a valid hex color'
      }
    },
    textColor: { 
      type: String, 
      default: '#ffffff',
      validate: {
        validator: function(v: string) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Text color must be a valid hex color'
      }
    },
    titleSize: { 
      type: String, 
      default: '1.5rem',
      enum: ['1rem', '1.25rem', '1.5rem', '1.75rem', '2rem', '2.5rem', '3rem']
    },
    descriptionSize: { 
      type: String, 
      default: '1rem',
      enum: ['0.75rem', '0.875rem', '1rem', '1.125rem', '1.25rem']
    },
    borderRadius: { 
      type: String, 
      default: '0.5rem',
      enum: ['0', '0.25rem', '0.5rem', '0.75rem', '1rem', '1.5rem']
    },
    padding: { 
      type: String, 
      default: '1rem',
      enum: ['0.5rem', '0.75rem', '1rem', '1.25rem', '1.5rem', '2rem']
    },
    animation: { 
      type: String, 
      default: 'fadeIn',
      enum: ['none', 'fadeIn', 'slideIn', 'bounce', 'pulse']
    },
    animationDuration: { 
      type: String, 
      default: '0.5s',
      enum: ['0.3s', '0.5s', '0.7s', '1s', '1.5s', '2s']
    },
    positions: [{
      type: String,
      enum: ['top', 'center', 'bottom', 'hero-overlay', 'between-sections']
    }]
  },
  analytics: {
    views: { 
      type: Number, 
      default: 0 
    },
    clicks: { 
      type: Number, 
      default: 0 
    },
    impressions: { 
      type: Number, 
      default: 0 
    }
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add indexes for common queries
BannerAdSchema.index({ isActive: 1 });
BannerAdSchema.index({ startDate: 1, endDate: 1 });
BannerAdSchema.index({ priority: -1 });
BannerAdSchema.index({ createdBy: 1 });

// Virtual to check if banner is currently active based on dates
BannerAdSchema.virtual('isCurrentlyActive').get(function() {
  if (!this.isActive) return false;
  
  const now = new Date();
  const startDate = this.startDate || new Date(0);
  const endDate = this.endDate;
  
  if (now < startDate) return false;
  if (endDate && now > endDate) return false;
  
  return true;
});

// Method to calculate end date based on duration
BannerAdSchema.methods.calculateEndDate = function() {
  if (!this.duration || !this.startDate) return null;
  
  const endDate = new Date(this.startDate);
  endDate.setDate(endDate.getDate() + this.duration);
  return endDate;
};

// Pre-save middleware to set end date based on duration
BannerAdSchema.pre('save', function(next) {
  if (this.duration && this.startDate && !this.endDate) {
    this.endDate = this.calculateEndDate();
  }
  next();
});

// Use mongoose.models.BannerAd if it exists, otherwise create a new model
const BannerAd = mongoose.models.BannerAd as mongoose.Model<IBannerAd> || 
                 mongoose.model<IBannerAd>('BannerAd', BannerAdSchema);

export default BannerAd;
