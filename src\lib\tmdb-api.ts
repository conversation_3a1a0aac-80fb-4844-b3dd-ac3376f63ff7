/**
 * TMDb API Service
 *
 * Provides utilities for interacting with The Movie Database API
 * to get details about movies and TV shows.
 */

import { TMDB_CONFIG } from '@/config/api';
import { ensureTMDBImagePath } from '@/utils/image-utils';

// Add Genre Map (ID -> Name)
// (Consider moving this to a shared types/constants file later)
export const TMDB_GENRE_MAP: Record<number, string> = {
  28: 'Action',
  12: 'Adventure',
  16: 'Animation',
  35: 'Comedy',
  80: 'Crime',
  99: 'Documentary',
  18: 'Drama',
  10751: 'Family',
  14: 'Fantasy',
  36: 'History',
  27: 'Horror',
  10402: 'Music',
  9648: 'Mystery',
  10749: 'Romance',
  878: 'Science Fiction',
  10770: 'TV Movie',
  53: 'Thriller',
  10752: 'War',
  37: 'Western'
};

// Add Reverse Genre Map (Name -> ID)
export const TMDB_GENRE_NAME_TO_ID_MAP: Record<string, number> = Object.entries(
  TMDB_GENRE_MAP
).reduce((acc, [id, name]) => {
  acc[name] = parseInt(id, 10);
  return acc;
}, {} as Record<string, number>);

// TMDb Image Sizes
export const TMDB_POSTER_SIZES = TMDB_CONFIG.POSTER_SIZES;

// Interface for TMDb movie/show result
export interface TMDbItem {
  id: number;
  title?: string;               // For movies
  name?: string;                // For TV shows
  original_title?: string;
  original_name?: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  vote_average: number;
  vote_count: number;
  release_date?: string;        // For movies
  first_air_date?: string;      // For TV shows
  genre_ids: number[];
  media_type?: 'movie' | 'tv';  // For multi-search results
}

// Interfaces for different API responses
export interface TMDbMovieResponse {
  page: number;
  results: TMDbItem[];
  total_pages: number;
  total_results: number;
}

export interface TMDbTVShowResponse {
  page: number;
  results: TMDbItem[];
  total_pages: number;
  total_results: number;
}

export interface TMDbSearchResponse {
  page: number;
  results: TMDbItem[];
  total_pages: number;
  total_results: number;
}

// Mapped content for our app
export interface MappedContent {
  id: string;
  title: string;
  overview: string;
  posterUrl: string;
  backdropUrl: string;
  voteAverage: number;
  releaseDate: string | null;
  tmdbId: string;
  imdbId?: string;
  idType: 'tmdb' | 'imdb';
  year?: number;
  genreIds?: number[]; // Add genre IDs for filtering
  mediaType?: 'movie' | 'tv';
  voteCount?: number;
}

/**
 * Get the full URL for TMDb images
 */
export function getTMDbImageUrl(path: string | null, size = TMDB_POSTER_SIZES.medium): string {
  // Log the incoming path for debugging
  // console.log(`[TMDB API] Processing image path: "${path}", size: ${size}`);

  // If no path is provided or it's invalid, return placeholder
  if (!path ||
      path === 'null' ||
      path === 'undefined' ||
      path.trim() === '' ||
      path.includes('null') ||
      path.includes('undefined')) {
    // console.log('[TMDB API] Invalid path detected, returning placeholder');
    return "https://placehold.co/300x450/171717/CCCCCC?text=No+Image";
  }

  try {
    // Clean up the path
    const cleanPath = path.trim();

    // If it's already a full URL, return it directly
    if (cleanPath.startsWith('http')) {
      // console.log(`[TMDB API] Already a full URL, using directly: ${cleanPath}`);
      return cleanPath;
    }

    // Ensure path starts with a slash
    const formattedPath = cleanPath.startsWith('/') ? cleanPath : `/${cleanPath}`;

    // Construct full URL with proper components
    const baseUrl = TMDB_CONFIG.IMAGE_BASE_URL;
    const fullUrl = `${baseUrl}/${size}${formattedPath}`;

    // console.log(`[TMDB API] Final URL: ${fullUrl}`);
    return fullUrl;
  } catch (error) {
    console.error('[TMDB API] Error generating image URL:', error);
    return "https://placehold.co/300x450/171717/CCCCCC?text=Error";
  }
}

/**
 * Helper function to make TMDb API requests
 */
async function tmdbFetch(endpoint: string, params: Record<string, string> = {}): Promise<any> {
  const url = new URL(`${TMDB_CONFIG.BASE_URL}${endpoint}`);

  // Add other params
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  const headers: HeadersInit = {
    'Content-Type': 'application/json'
  };

  // Add authentication based on AUTH_METHOD
  if (TMDB_CONFIG.AUTH_METHOD === 'api_key') {
    // Use API key in URL params
    url.searchParams.append('api_key', TMDB_CONFIG.API_KEY);
  } else {
    // Use bearer token in Authorization header
    headers['Authorization'] = `Bearer ${TMDB_CONFIG.ACCESS_TOKEN}`;
  }

  const response = await fetch(url.toString(), { headers });

  if (!response.ok) {
    throw new Error(`TMDb API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Map TMDb items to our app's format
 */
export function mapTMDbItem(item: TMDbItem): MappedContent {
  if (!item) {
    console.error('Received undefined or null TMDb item');
    // Return a placeholder item
    return {
      id: 'placeholder',
      title: 'Unknown Title',
      overview: '',
      posterUrl: "https://placehold.co/300x450/171717/CCCCCC?text=No+Image",
      backdropUrl: "https://placehold.co/1920x1080/171717/CCCCCC?text=No+Data",
      voteAverage: 0,
      releaseDate: null,
      tmdbId: 'placeholder',
      idType: 'tmdb',
      mediaType: 'movie',
      voteCount: 0
    };
  }

  // Determine if it's a movie or TV show - prioritize explicit media_type over title/name existence
  let mediaType: 'movie' | 'tv';
  let title: string;

  // If media_type is explicitly provided, use that
  if (item.media_type) {
    mediaType = item.media_type;
    title = mediaType === 'movie' ? (item.title || 'Unknown Title') : (item.name || 'Unknown Title');
  }
  // Otherwise, use presence of title or name to determine type
  else {
    mediaType = item.title ? 'movie' : 'tv';
    title = item.title || item.name || 'Unknown Title';
  }

  // Extract year from release date or first air date
  let year: number | undefined;
  let releaseDate = item.release_date || item.first_air_date || null;

  if (releaseDate) {
    try {
      year = new Date(releaseDate).getFullYear();
      // Validate year is a reasonable number
      if (year < 1900 || year > 2100) {
        year = undefined;
      }
    } catch (e) {
      console.warn('Error parsing date:', releaseDate);
      year = undefined;
      releaseDate = null;
    }
  }

  // Use our utility function to ensure poster and backdrop paths are properly formatted
  let posterUrl = item.poster_path ? getTMDbImageUrl(item.poster_path) : '';
  let backdropUrl = item.backdrop_path ? getTMDbImageUrl(item.backdrop_path, TMDB_POSTER_SIZES.original) : '';

  // Log the image paths for debugging
  // console.log(`[TMDB API] Mapped item: ${title}`);
  // console.log(`[TMDB API] Original poster_path: ${item.poster_path}`);
  // console.log(`[TMDB API] Processed posterUrl: ${posterUrl}`);
  // console.log(`[TMDB API] Original backdrop_path: ${item.backdrop_path}`);
  // console.log(`[TMDB API] Processed backdropUrl: ${backdropUrl}`);

  // Ensure we have a valid ID
  const id = item.id ? item.id.toString() : `tmdb-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  // console.log(`[TMDB API] Using ID: ${id} for item: ${title}`);

  const mappedItem: MappedContent = {
    id,
    title,
    overview: item.overview || '',
    posterUrl,
    backdropUrl,
    voteAverage: item.vote_average || 0,
    releaseDate: releaseDate || null,
    tmdbId: item.id.toString(),
    idType: 'tmdb',
    year,
    genreIds: item.genre_ids || [],
    mediaType,
    voteCount: item.vote_count || 0
  };

  return mappedItem;
}

/**
 * Get popular movies from TMDb
 */
export async function getPopularMovies(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/movie/popular', { page: page.toString() }) as TMDbMovieResponse;
  return data.results.map(mapTMDbItem);
}

/**
 * Get top-rated movies from TMDb
 */
export async function getTopRatedMovies(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/movie/top_rated', { page: page.toString() }) as TMDbMovieResponse;
  return data.results.map(mapTMDbItem);
}

/**
 * Get now playing movies from TMDb
 */
export async function getNowPlayingMovies(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/movie/now_playing', { page: page.toString() }) as TMDbMovieResponse;
  return data.results.map(mapTMDbItem);
}

/**
 * Get upcoming movies from TMDb
 */
export async function getUpcomingMovies(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/movie/upcoming', { page: page.toString() }) as TMDbMovieResponse;
  return data.results.map(mapTMDbItem);
}

/**
 * Get popular TV shows from TMDb
 */
export async function getPopularTVShows(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/tv/popular', { page: page.toString() }) as TMDbTVShowResponse;

  // Ensure each result has media_type set to 'tv'
  const results = data.results.map(item => ({
    ...item,
    media_type: 'tv' as const  // Force media_type to be 'tv'
  }));

  return results.map(mapTMDbItem);
}

/**
 * Get top-rated TV shows from TMDb
 */
export async function getTopRatedTVShows(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/tv/top_rated', { page: page.toString() }) as TMDbTVShowResponse;

  // Ensure each result has media_type set to 'tv'
  const results = data.results.map(item => ({
    ...item,
    media_type: 'tv' as const  // Force media_type to be 'tv'
  }));

  return results.map(mapTMDbItem);
}

/**
 * Get TV shows airing today from TMDb
 */
export async function getTVShowsAiringToday(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/tv/airing_today', { page: page.toString() }) as TMDbTVShowResponse;

  // Ensure each result has media_type set to 'tv'
  const results = data.results.map(item => ({
    ...item,
    media_type: 'tv' as const  // Force media_type to be 'tv'
  }));

  return results.map(mapTMDbItem);
}

/**
 * Get TV shows on the air from TMDb
 */
export async function getTVShowsOnTheAir(page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch('/tv/on_the_air', { page: page.toString() }) as TMDbTVShowResponse;

  // Ensure each result has media_type set to 'tv'
  const results = data.results.map(item => ({
    ...item,
    media_type: 'tv' as const  // Force media_type to be 'tv'
  }));

  return results.map(mapTMDbItem);
}

/**
 * Search for movies, TV shows, or both
 */
export async function search(query: string, page = 1, type: 'movie' | 'tv' | 'multi' = 'multi'): Promise<MappedContent[]> {
  const data = await tmdbFetch(`/search/${type}`, {
    query,
    page: page.toString(),
    include_adult: 'false'
  }) as TMDbSearchResponse;

  return data.results.map(mapTMDbItem);
}

/**
 * Get movie details by TMDb ID
 */
export async function getMovieDetails(id: string): Promise<any> {
  return tmdbFetch(`/movie/${id}`, { append_to_response: 'videos,credits,similar,recommendations' });
}

/**
 * Get TV show details by TMDb ID
 */
export async function getTVShowDetails(id: string): Promise<any> {
  const data = await tmdbFetch(`/tv/${id}`, { append_to_response: 'videos,credits,similar,recommendations,season/1' });

  // Log the season count for debugging
  console.log(`TV Show ${id} details:`, {
    name: data.name,
    number_of_seasons: data.number_of_seasons,
    seasons: data.seasons ? data.seasons.length : 'N/A',
    hasSeasons: !!data.seasons
  });

  return data;
}

/**
 * Get TV season details
 */
export async function getTVSeasonDetails(showId: string, seasonNumber: number): Promise<any> {
  return tmdbFetch(`/tv/${showId}/season/${seasonNumber}`);
}

/**
 * Get TV episode details
 */
export async function getTVEpisodeDetails(showId: string, seasonNumber: number, episodeNumber: number): Promise<any> {
  return tmdbFetch(`/tv/${showId}/season/${seasonNumber}/episode/${episodeNumber}`);
}

/**
 * Get trending content (daily)
 */
export async function getTrendingDaily(mediaType: 'all' | 'movie' | 'tv' = 'all', page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch(`/trending/${mediaType}/day`, { page: page.toString() });
  return data.results.map(mapTMDbItem);
}

/**
 * Get trending content (weekly)
 */
export async function getTrendingWeekly(mediaType: 'all' | 'movie' | 'tv' = 'all', page = 1): Promise<MappedContent[]> {
  const data = await tmdbFetch(`/trending/${mediaType}/week`, { page: page.toString() });
  return data.results.map(mapTMDbItem);
}

/**
 * Search for movies specifically
 */
export async function searchMovies(query: string, page = 1): Promise<MappedContent[]> {
  return search(query, page, 'movie');
}

/**
 * Search for TV shows specifically
 */
export async function searchTVShows(query: string, page = 1): Promise<MappedContent[]> {
  try {
    console.log(`[DEBUG] Searching TV shows with query: "${query}"`);

    const data = await tmdbFetch(`/search/tv`, {
      query,
      page: page.toString(),
      include_adult: 'false'
    }) as TMDbSearchResponse;

    // Log the raw data for debugging
    if (data.results && data.results.length > 0) {
      console.log(`[DEBUG] Raw search results count: ${data.results.length}`);
      console.log(`[DEBUG] First result raw data:`, {
        id: data.results[0].id,
        name: data.results[0].name,
        poster_path: data.results[0].poster_path
      });
    }

    // Ensure each result has media_type set to 'tv'
    const results = data.results.map(item => ({
      ...item,
      media_type: 'tv' as const  // Force media_type to be 'tv'
    }));

    const mappedResults = results.map(mapTMDbItem);

    // Log the mapped results for debugging
    if (mappedResults && mappedResults.length > 0) {
      console.log(`[DEBUG] Mapped results count: ${mappedResults.length}`);
      console.log(`[DEBUG] First mapped result:`, {
        id: mappedResults[0].id,
        title: mappedResults[0].title,
        mediaType: mappedResults[0].mediaType,
        posterUrl: mappedResults[0].posterUrl
      });
    }

    return mappedResults;
  } catch (error) {
    console.error(`[ERROR] Failed to search TV shows:`, error);
    return [];
  }
}

/**
 * Discover movies based on filters and sorting
 */
export interface DiscoverFilters {
  page?: number;
  sort_by?: string; // e.g., 'popularity.desc', 'vote_average.desc', 'release_date.desc'
  with_genres?: string; // Comma-separated string of genre IDs
  primary_release_year?: string; // Specific year
  'vote_average.gte'?: string; // Min vote average (0-10)
  'vote_average.lte'?: string; // Max vote average (0-10)
  'vote_count.gte'?: string; // Minimum number of votes
  // Add other discover parameters as needed: https://developer.themoviedb.org/reference/discover-movie
}

export async function discoverMovies(filters: DiscoverFilters): Promise<{ results: MappedContent[], totalPages: number, totalResults: number }> {
  // Default page if not provided
  const params: Record<string, string> = {
    page: (filters.page || 1).toString(),
    // Default sort if not specified
    sort_by: filters.sort_by || 'popularity.desc',
  };

  // Add optional filters
  if (filters.with_genres) params.with_genres = filters.with_genres;
  if (filters.primary_release_year) params.primary_release_year = filters.primary_release_year;
  if (filters['vote_average.gte']) params['vote_average.gte'] = filters['vote_average.gte'];
  if (filters['vote_average.lte']) params['vote_average.lte'] = filters['vote_average.lte'];
  if (filters['vote_count.gte']) params['vote_count.gte'] = filters['vote_count.gte'];

  // Add language (optional, but good practice)
  params.language = 'en-US';

  try {
      const data = await tmdbFetch('/discover/movie', params) as TMDbMovieResponse;
      return {
          results: data.results.map(mapTMDbItem),
          totalPages: data.total_pages,
          totalResults: data.total_results
      };
  } catch (error) {
      console.error("Error discovering movies:", error);
      // Rethrow or return empty state
      throw error;
      // Or return { results: [], totalPages: 0, totalResults: 0 };
  }
}

/**
 * Discover TV shows based on filters and sorting
 */
export interface DiscoverTVFilters {
  page?: number;
  sort_by?: string; // e.g., 'popularity.desc', 'vote_average.desc', 'first_air_date.desc'
  with_genres?: string; // Comma-separated string of genre IDs
  first_air_date_year?: string; // Specific year
  'vote_average.gte'?: string; // Min vote average (0-10)
  'vote_average.lte'?: string; // Max vote average (0-10)
  'vote_count.gte'?: string; // Minimum number of votes
  // Add other discover parameters as needed: https://developer.themoviedb.org/reference/discover-tv
}

export async function discoverTVShows(filters: DiscoverTVFilters): Promise<{ results: MappedContent[], totalPages: number, totalResults: number }> {
  const params: Record<string, string> = {
    page: (filters.page || 1).toString(),
    sort_by: filters.sort_by || 'popularity.desc',
  };

  // Add optional filters
  if (filters.with_genres) params.with_genres = filters.with_genres;
  if (filters.first_air_date_year) params.first_air_date_year = filters.first_air_date_year;
  if (filters['vote_average.gte']) params['vote_average.gte'] = filters['vote_average.gte'];
  if (filters['vote_average.lte']) params['vote_average.lte'] = filters['vote_average.lte'];
  if (filters['vote_count.gte']) params['vote_count.gte'] = filters['vote_count.gte'];

  // Add language
  params.language = 'en-US';

  try {
      const data = await tmdbFetch('/discover/tv', params) as TMDbTVShowResponse;

      // Explicitly mark results as TV before mapping
      const tvResults = data.results.map(item => ({ ...item, media_type: 'tv' as const }));

      return {
          results: tvResults.map(mapTMDbItem), // Use mapTMDbItem which now handles media_type
          totalPages: data.total_pages,
          totalResults: data.total_results
      };
  } catch (error) {
      console.error("Error discovering TV shows:", error);
      throw error;
  }
}

/**
 * Get movies by category name
 * Handles special categories (Movies, TV Shows) and genre-based categories
 */
export async function getMoviesByCategory(category: string, count: number = 5): Promise<TMDbItem[]> {
  // Map category names to genre IDs
  const CATEGORY_TO_GENRE_ID: Record<string, number> = {
    'Action': 28,
    'Adventure': 12,
    'Animation': 16,
    'Comedy': 35,
    'Crime': 80,
    'Documentary': 99,
    'Drama': 18,
    'Family': 10751,
    'Fantasy': 14,
    'History': 36,
    'Horror': 27,
    'Music': 10402,
    'Mystery': 9648,
    'Romance': 10749,
    'Science Fiction': 878,
    'Sci-Fi': 878, // Alias for Science Fiction
    'TV Movie': 10770,
    'Thriller': 53,
    'War': 10752,
    'Western': 37
  };

  try {
    // Handle special categories
    if (category === 'Movies') {
      const response = await tmdbFetch('/movie/popular', { page: '1' });
      return response.results.slice(0, count);
    }

    if (category === 'TV Shows') {
      const response = await tmdbFetch('/tv/popular', { page: '1' });
      return response.results.slice(0, count);
    }

    // Handle genre-based categories
    const genreId = CATEGORY_TO_GENRE_ID[category];

    if (genreId) {
      // Get both movies and TV shows for this genre
      const [movieResponse, tvResponse] = await Promise.all([
        tmdbFetch('/discover/movie', { with_genres: genreId.toString(), page: '1' }),
        tmdbFetch('/discover/tv', { with_genres: genreId.toString(), page: '1' })
      ]);

      // Combine results and return the requested number
      const combinedResults = [...movieResponse.results, ...tvResponse.results];
      return combinedResults.slice(0, count);
    }

    // Default fallback - return popular movies
    const response = await tmdbFetch('/movie/popular', { page: '1' });
    return response.results.slice(0, count);
  } catch (error) {
    console.error(`Error fetching movies for category ${category}:`, error);
    return [];
  }
}