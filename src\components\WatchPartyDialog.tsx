"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useWatchParty } from "@/hooks/useWatchParty"

interface WatchPartyDialogProps {
  contentId: string
}

export function WatchPartyDialog({ contentId }: WatchPartyDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [mode, setMode] = useState<"create" | "join">("create")
  const [partyId, setPartyId] = useState("")
  const [name, setName] = useState("")
  const { createParty, joinParty } = useWatchParty()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (mode === "create") {
      await createParty(contentId, name)
    } else {
      await joinParty(partyId, name)
    }
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Watch Party</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Watch Party</DialogTitle>
          <DialogDescription>
            Create a new watch party or join an existing one.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="flex items-center gap-4">
              <Button
                type="button"
                variant={mode === "create" ? "default" : "outline"}
                onClick={() => setMode("create")}
              >
                Create
              </Button>
              <Button
                type="button"
                variant={mode === "join" ? "default" : "outline"}
                onClick={() => setMode("join")}
              >
                Join
              </Button>
            </div>
            {mode === "join" && (
              <div className="grid gap-2">
                <Label htmlFor="party-id">Party ID</Label>
                <Input
                  id="party-id"
                  value={partyId}
                  onChange={(e) => setPartyId(e.target.value)}
                  placeholder="Enter party ID"
                  required
                />
              </div>
            )}
            <div className="grid gap-2">
              <Label htmlFor="name">Your Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your name"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">
              {mode === "create" ? "Create Party" : "Join Party"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 