"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

// Extended style type to support CSS variables
interface CustomStyles extends React.CSSProperties {
  '--progress-foreground'?: string;
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    size?: "xs" | "sm" | "md" | "lg"
    variant?: "default" | "blue" | "red" | "green" | "amber" | "custom"
    showValue?: boolean
    formatValue?: (value: number) => string
    indicatorClassName?: string
  }
>(({
  className,
  value = 0,
  max = 100,
  size = "md",
  variant = "default",
  showValue = false,
  formatValue,
  style,
  indicatorClassName,
  ...props
}, ref) => {
  const sizeClasses = {
    xs: "h-1",
    sm: "h-2",
    md: "h-3",
    lg: "h-4"
  }

  const variantClasses = {
    default: "bg-vista-light/30",
    blue: "bg-vista-blue/30",
    red: "bg-red-500/30",
    green: "bg-green-500/30",
    amber: "bg-amber-500/30",
    custom: "bg-vista-light/30"
  }

  const indicatorClasses = {
    default: "bg-vista-light",
    blue: "bg-vista-blue",
    red: "bg-red-500",
    green: "bg-green-500",
    amber: "bg-amber-500",
    custom: ""
  }

  const percentage = Math.min(Math.max(0, value || 0), max) / max * 100;

  const displayValue = React.useMemo(() => {
    if (formatValue) {
      return formatValue(value || 0);
    }
    return `${Math.round(percentage)}%`;
  }, [formatValue, percentage, value]);

  // Handle custom styling for the indicator
  const isCustomVariant = variant === 'custom';
  const customStyle = style as CustomStyles | undefined;
  const indicatorStyle = isCustomVariant && customStyle?.['--progress-foreground']
    ? { backgroundColor: customStyle['--progress-foreground'] }
    : {};

  return (
    <div className="w-full">
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(
          "relative overflow-hidden rounded-full",
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        style={{
          // Fix overflow issue on Safari
          transform: "translateZ(0)",
          ...style
        }}
        value={value}
        max={max}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(
            "h-full w-full flex-1 transition-all",
            indicatorClassName || (isCustomVariant ? "" : indicatorClasses[variant])
          )}
          style={{
            transform: `translateX(-${100 - percentage}%)`,
            ...indicatorStyle
          }}
        />
      </ProgressPrimitive.Root>
      {showValue && (
        <div className="mt-1 text-xs text-vista-light/80 text-right">
          {displayValue}
        </div>
      )}
    </div>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

// Streaming specific progress bar for video watching progress
const WatchProgress = ({
  duration,
  currentTime,
  className,
  size = "xs",
  onSeek,
  ...props
}: {
  duration: number
  currentTime: number
  onSeek?: (time: number) => void
  size?: "xs" | "sm" | "md" | "lg"
} & Omit<React.ComponentPropsWithoutRef<typeof Progress>, "value" | "max" | "size">
) => {
  const progressBarRef = React.useRef<HTMLDivElement>(null);
  const percentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!onSeek || !progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const offsetX = e.clientX - rect.left;
    const percentage = offsetX / rect.width;
    const newTime = duration * percentage;

    onSeek(newTime);
  };

  return (
    <div
      ref={progressBarRef}
      className={cn("relative cursor-pointer", className)}
      onClick={handleClick}
    >
      <Progress
        value={percentage}
        max={100}
        size={size}
        variant="blue"
        showValue={false}
        className="hover:h-3 transition-all duration-200"
        {...props}
      />
      {onSeek && (
        <div className="hidden absolute bottom-full mb-2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-vista-dark-lighter px-2 py-1 rounded text-xs">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      )}
    </div>
  );
};

export { Progress, WatchProgress }
